<template>
  <div class="rag-test-page">
    <a-card title="RAG功能测试" style="margin: 24px;">
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="智能体id">
              <a-input-number 
                v-model:value="testForm.智能体id" 
                :min="1" 
                placeholder="请输入智能体id"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="测试模式">
              <a-select v-model:value="testForm.测试模式" style="width: 100%">
                <a-select-option value="complete">完整测试</a-select-option>
                <a-select-option value="retrieval_only">仅检索测试</a-select-option>
                <a-select-option value="generation_only">仅生成测试</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="测试问题">
          <a-textarea
            v-model:value="testForm.测试问题"
            placeholder="请输入测试问题"
            :rows="3"
          />
        </a-form-item>

        <!-- 查询优化配置 -->
        <a-collapse ghost>
          <a-collapse-panel key="query-optimization" header="🔧 查询优化配置">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="启用查询优化">
                  <a-switch v-model:checked="testForm.查询优化配置.启用查询优化" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="优化策略">
                  <a-select
                    v-model:value="testForm.查询优化配置.查询优化策略"
                    :disabled="!testForm.查询优化配置.启用查询优化"
                    style="width: 100%"
                  >
                    <a-select-option value="rewrite">查询重写</a-select-option>
                    <a-select-option value="expand">查询扩展</a-select-option>
                    <a-select-option value="multi_query">多查询生成</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="优化模型">
                  <a-select
                    v-model:value="testForm.查询优化配置.查询优化模型id"
                    :disabled="!testForm.查询优化配置.启用查询优化"
                    :loading="查询优化模型加载中"
                    @focus="加载查询优化模型列表"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option
                      v-for="模型 in 查询优化模型列表"
                      :key="模型.id"
                      :value="模型.id"
                    >
                      {{ 模型.显示名称 }} ({{ 模型.提供商 }})
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="提示词模板">
                  <a-button
                    size="small"
                    @click="显示提示词编辑器 = true"
                    :disabled="!testForm.查询优化配置.启用查询优化"
                  >
                    编辑提示词
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        
        <a-form-item label="检索配置">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="最大检索数量">
                <a-input-number 
                  v-model:value="testForm.检索配置.最大检索数量" 
                  :min="1" 
                  :max="20"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="相似度阈值">
                <a-input-number 
                  v-model:value="testForm.检索配置.相似度阈值" 
                  :min="0" 
                  :max="1"
                  :step="0.1"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="检索策略">
                <a-select v-model:value="testForm.检索配置.检索策略" style="width: 100%">
                  <a-select-option value="similarity">相似度检索</a-select-option>
                  <a-select-option value="mmr">MMR检索</a-select-option>
                  <a-select-option value="similarity_score_threshold">阈值检索</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button 
              type="primary" 
              @click="执行RAG测试" 
              :loading="测试中"
              :disabled="!testForm.智能体id || !testForm.测试问题"
            >
              执行测试
            </a-button>
            <a-button @click="重置表单">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      
      <!-- 测试结果 -->
      <div v-if="测试结果" class="test-result">
        <a-divider>测试结果</a-divider>
        
        <a-alert
          :type="测试结果.success ? 'success' : 'error'"
          :message="测试结果.message"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <div v-if="测试结果.success && 测试结果.data">
          <a-tabs>
            <a-tab-pane key="basic" tab="基本信息">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="智能体id">
                  {{ 测试结果.data.基本信息?.智能体id }}
                </a-descriptions-item>
                <a-descriptions-item label="智能体名称">
                  {{ 测试结果.data.基本信息?.智能体名称 }}
                </a-descriptions-item>
                <a-descriptions-item label="测试查询">
                  {{ 测试结果.data.基本信息?.测试查询 }}
                </a-descriptions-item>
                <a-descriptions-item label="测试模式">
                  {{ 测试结果.data.基本信息?.测试模式 }}
                </a-descriptions-item>
                <a-descriptions-item label="测试时间">
                  {{ 测试结果.data.基本信息?.测试时间 }}
                </a-descriptions-item>
                <a-descriptions-item label="会话ID">
                  {{ 测试结果.data.基本信息?.会话ID }}
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>
            
            <a-tab-pane key="retrieval" tab="检索阶段">
              <div v-if="测试结果.data.检索阶段">
                <a-descriptions :column="2" bordered style="margin-bottom: 16px">
                  <a-descriptions-item label="检索状态">
                    <a-tag :color="测试结果.data.检索阶段.success ? 'green' : 'red'">
                      {{ 测试结果.data.检索阶段.success ? '成功' : '失败' }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="结果数量">
                    {{ 测试结果.data.检索阶段.结果数量 || 0 }}
                  </a-descriptions-item>
                  <a-descriptions-item label="检索耗时">
                    {{ (测试结果.data.检索阶段.检索耗时 || 0).toFixed(3) }}s
                  </a-descriptions-item>
                  <a-descriptions-item label="检索参数">
                    <pre>{{ JSON.stringify(测试结果.data.检索阶段.检索参数, null, 2) }}</pre>
                  </a-descriptions-item>
                </a-descriptions>
                
                <div v-if="测试结果.data.检索阶段.检索结果?.length">
                  <h4>检索结果</h4>
                  <a-list
                    :data-source="测试结果.data.检索阶段.检索结果"
                    item-layout="vertical"
                  >
                    <template #renderItem="{ item, index }">
                      <a-list-item>
                        <a-list-item-meta>
                          <template #title>
                            文档 {{ index + 1 }} (相似度: {{ item.相似度分数?.toFixed(3) || 'N/A' }})
                          </template>
                          <template #description>
                            {{ item.文档内容?.substring(0, 200) }}...
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
                
                <div v-if="测试结果.data.检索阶段.error" class="error-info">
                  <a-alert type="error" :message="测试结果.data.检索阶段.error" />
                </div>
              </div>
            </a-tab-pane>
            
            <a-tab-pane key="generation" tab="生成阶段">
              <div v-if="测试结果.data.生成阶段">
                <a-descriptions :column="2" bordered style="margin-bottom: 16px">
                  <a-descriptions-item label="生成状态">
                    <a-tag :color="测试结果.data.生成阶段.成功 ? 'green' : 'red'">
                      {{ 测试结果.data.生成阶段.成功 ? '成功' : '失败' }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="生成耗时">
                    {{ (测试结果.data.生成阶段.耗时 || 0).toFixed(3) }}s
                  </a-descriptions-item>
                  <a-descriptions-item label="令牌消耗">
                    {{ 测试结果.data.生成阶段.令牌消耗 || 0 }}
                  </a-descriptions-item>
                  <a-descriptions-item label="使用上下文">
                    {{ 测试结果.data.生成阶段.生成分析?.使用上下文 ? '是' : '否' }}
                  </a-descriptions-item>
                </a-descriptions>
                
                <div v-if="测试结果.data.生成阶段.智能体回复">
                  <h4>智能体回复</h4>
                  <a-card>
                    <pre>{{ 测试结果.data.生成阶段.智能体回复 }}</pre>
                  </a-card>
                </div>
              </div>
            </a-tab-pane>
            
            <a-tab-pane key="performance" tab="性能指标">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="总耗时">
                  {{ (测试结果.data.性能指标?.总耗时 || 0).toFixed(3) }}s
                </a-descriptions-item>
                <a-descriptions-item label="检索耗时">
                  {{ (测试结果.data.性能指标?.检索耗时 || 0).toFixed(3) }}s
                </a-descriptions-item>
                <a-descriptions-item label="生成耗时">
                  {{ (测试结果.data.性能指标?.生成耗时 || 0).toFixed(3) }}s
                </a-descriptions-item>
                <a-descriptions-item label="令牌消耗">
                  {{ 测试结果.data.性能指标?.令牌消耗 || 0 }}
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-card>
  </div>

  <!-- 查询优化提示词编辑器模态框 -->
  <a-modal
    v-model:open="显示提示词编辑器"
    title="查询优化提示词编辑器"
    width="800px"
    :footer="null"
  >
    <div class="prompt-editor-modal">
      <a-form layout="vertical">
        <a-form-item label="优化策略">
          <a-select
            v-model:value="testForm.查询优化配置.查询优化策略"
            style="width: 200px"
          >
            <a-select-option value="rewrite">查询重写</a-select-option>
            <a-select-option value="expand">查询扩展</a-select-option>
            <a-select-option value="multi_query">多查询生成</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <template #label>
            <a-space>
              提示词模板
              <a-tooltip title="使用 {query} 作为用户查询的占位符">
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </template>
          <a-textarea
            v-model:value="testForm.查询优化配置.查询优化提示词"
            :rows="8"
            placeholder="请输入查询优化提示词模板，使用 {query} 作为用户查询的占位符..."
            style="font-family: 'Consolas', 'Monaco', monospace;"
          />
        </a-form-item>

        <a-form-item>
          <div class="prompt-templates">
            <h4>📋 常用模板</h4>
            <a-space direction="vertical" style="width: 100%;">
              <a-card size="small" title="查询重写模板" class="template-card">
                <p class="template-desc">将用户查询重写为更适合向量检索的形式</p>
                <a-button
                  size="small"
                  @click="testForm.查询优化配置.查询优化提示词 = '请将以下用户查询重写为更适合向量检索的形式，保持原意但使用更准确的关键词：\\n\\n用户查询：{query}\\n\\n重写后的查询：'"
                >
                  使用此模板
                </a-button>
              </a-card>

              <a-card size="small" title="查询扩展模板" class="template-card">
                <p class="template-desc">为查询添加相关的同义词和概念</p>
                <a-button
                  size="small"
                  @click="testForm.查询优化配置.查询优化提示词 = '请为以下查询添加相关的同义词、相关概念和扩展词汇，以提高向量检索的召回率：\\n\\n原始查询：{query}\\n\\n请返回扩展后的查询，包含原始查询和相关词汇：'"
                >
                  使用此模板
                </a-button>
              </a-card>

              <a-card size="small" title="多查询生成模板" class="template-card">
                <p class="template-desc">生成多个不同角度的相关查询</p>
                <a-button
                  size="small"
                  @click="testForm.查询优化配置.查询优化提示词 = '基于以下原始查询，生成3个不同角度但相关的查询变体，以提高检索覆盖率：\\n\\n原始查询：{query}\\n\\n请生成3个查询变体，每个一行：\\n1.\\n2.\\n3.'"
                >
                  使用此模板
                </a-button>
              </a-card>
            </a-space>
          </div>
        </a-form-item>
      </a-form>

      <div class="modal-footer">
        <a-space>
          <a-button @click="显示提示词编辑器 = false">取消</a-button>
          <a-button
            type="primary"
            @click="显示提示词编辑器 = false"
          >
            保存
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import adminLangchainService from '@/services/adminLangchainService'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { reactive, ref } from 'vue'

// 响应式数据
const 测试中 = ref(false)
const 测试结果 = ref(null)

// 查询优化相关数据
const 查询优化模型列表 = ref([])
const 查询优化模型加载中 = ref(false)
const 显示提示词编辑器 = ref(false)

const testForm = reactive({
  智能体id: 1,
  测试问题: '测试RAG功能',
  测试模式: 'complete',
  检索配置: {
    最大检索数量: 5,
    相似度阈值: 0.7,
    检索策略: 'similarity'
  },
  查询优化配置: {
    启用查询优化: false,
    查询优化策略: 'rewrite',
    查询优化模型id: null,
    查询优化提示词: ''
  }
})

// 方法
const 执行RAG测试 = async () => {
  if (!testForm.智能体id || !testForm.测试问题) {
    message.warning('请填写智能体id和测试问题')
    return
  }

  try {
    测试中.value = true
    测试结果.value = null
    
    console.log('🔍 开始RAG测试...', testForm)
    
    const 响应 = await adminLangchainService.测试智能体RAG功能(testForm)
    
    console.log('📡 RAG测试响应:', 响应)
    
    if (响应.status === 100) {
      测试结果.value = {
        success: true,
        message: '✅ RAG测试成功',
        data: 响应.data
      }
      message.success('RAG测试成功')
    } else {
      测试结果.value = {
        success: false,
        message: `❌ RAG测试失败: ${响应.message || '未知错误'}`,
        data: null
      }
      message.error(响应.message || 'RAG测试失败')
    }
    
  } catch (error) {
    console.error('❌ RAG测试异常:', error)
    测试结果.value = {
      success: false,
      message: `❌ RAG测试异常: ${error.message || '未知错误'}`,
      data: null
    }
    message.error('RAG测试异常')
  } finally {
    测试中.value = false
  }
}

const 重置表单 = () => {
  Object.assign(testForm, {
    智能体id: 1,
    测试问题: '测试RAG功能',
    测试模式: 'complete',
    检索配置: {
      最大检索数量: 5,
      相似度阈值: 0.7,
      检索策略: 'similarity'
    },
    查询优化配置: {
      启用查询优化: false,
      查询优化策略: 'rewrite',
      查询优化模型id: null,
      查询优化提示词: ''
    }
  })
  测试结果.value = null
}

/**
 * 加载查询优化模型列表
 */
const 加载查询优化模型列表 = async () => {
  if (查询优化模型加载中.value || 查询优化模型列表.value.length > 0) return

  try {
    查询优化模型加载中.value = true
    console.log('🤖 开始加载查询优化模型列表...')

    const 响应 = await adminLangchainService.获取可用查询优化模型列表()

    if (响应.success && 响应.data) {
      查询优化模型列表.value = 响应.data.map(model => ({
        id: Number(model.id),
        模型名称: model.模型名称,
        显示名称: model.显示名称 || model.模型名称,
        提供商: model.提供商,
        模型类型: model.模型类型
      }))
      console.log('✅ 查询优化模型列表加载成功:', 查询优化模型列表.value.length)
    } else {
      console.error('❌ 查询优化模型API响应异常:', 响应)
      message.error(响应.message || '加载查询优化模型列表失败')
    }
  } catch (error) {
    console.error('❌ 加载查询优化模型列表失败:', error)
    message.error('加载查询优化模型列表失败')
  } finally {
    查询优化模型加载中.value = false
  }
}
</script>

<style scoped>
.rag-test-page {
  background: #f0f2f5;
  min-height: 100vh;
}

.test-result {
  margin-top: 24px;
}

.error-info {
  margin-top: 16px;
}

pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

/* 提示词编辑器模态框样式 */
.prompt-editor-modal {
  padding: 16px 0;
}

.template-card {
  margin-bottom: 8px;
}

.template-desc {
  font-size: 12px;
  color: #666;
  margin: 8px 0;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}
</style>
