/**
 * 团队达人管理API服务
 * 专门处理团队达人相关的业务逻辑和数据交互
 * 
 * 产品功能设计理念：
 * 1. 团队视角的达人管理 - 从团队维度统一管理所有成员的达人资源
 * 2. 数据透明化 - 团队管理者可以清楚了解团队达人分布和利用情况
 * 3. 资源优化配置 - 支持达人在团队成员间的转移和重新分配
 * 4. 绩效考核支持 - 提供团队和个人的达人管理绩效数据
 */

import api from '../api'

/**
 * 团队达人服务类
 * 封装所有团队达人管理相关的API调用
 */
class TeamTalentService {
  
  /**
   * 获取团队达人统计数据
   * 产品价值：为团队管理者提供整体数据概览，支持决策制定
   * 
   * @param {number} teamId - 团队id
   * @param {Object} options - 查询选项
   * @param {string} options.timeRange - 时间范围：'7d', '30d', '90d', 'all'
   * @param {boolean} options.includeInactive - 是否包含非活跃达人
   * @returns {Promise} 包含团队达人统计数据的响应
   * 
   * 返回数据结构：
   * {
   *   totalTalents: number,      // 团队达人总数
   *   activeMembers: number,     // 有达人认领的活跃成员数
   *   monthlyNew: number,        // 本月新增达人数
   *   avgPerMember: number,      // 平均每人达人数
   *   topPerformers: Array,      // 达人数量排行榜
   *   categoryDistribution: Object, // 达人类别分布
   *   memberStats: Array         // 成员达人分布详情
   * }
   */
  async getTeamTalentStats(teamId, options = {}) {
    try {
      console.log(`获取团队${teamId}的达人统计数据`, options)
      
      const response = await api.post('/team/talent/stats', {
        团队id: teamId,
        时间范围: options.timeRange || '30d',
        包含非活跃: options.includeInactive || false,
        详细统计: true
      })
      
      // 数据处理和格式化
      if (response.status === 100) {
        const data = response.data
        
        // 计算衍生指标
        const processedData = {
          ...data,
          // 计算团队达人利用率
          talentUtilizationRate: data.totalTalents > 0 ? 
            (data.activeTalents / data.totalTalents * 100).toFixed(1) : 0,
          
          // 计算成员参与度
          memberParticipationRate: data.totalMembers > 0 ? 
            (data.activeMembers / data.totalMembers * 100).toFixed(1) : 0,
          
          // 处理成员统计数据
          memberStats: (data.memberStats || []).map(member => ({
            ...member,
            performanceLevel: this.calculatePerformanceLevel(member.talentCount, data.avgPerMember)
          }))
        }
        
        console.log('团队达人统计数据处理完成:', processedData)
        return { ...response, data: processedData }
      }
      
      return response
    } catch (error) {
      console.error('获取团队达人统计失败:', error)

      // 不再提供模拟数据，直接抛出错误让前端处理
      throw error
    }
  }

  /**
   * 获取团队微信达人统计
   * 产品价值：提供团队微信达人的全面统计分析，支持管理决策
   *
   * @param {number} teamId - 团队id
   * @param {Object} options - 可选参数
   * @param {string} options.timeRange - 时间范围：'7d', '30d', '90d', 'all'
   * @param {boolean} options.includeInactive - 是否包含非活跃达人
   * @returns {Promise} 包含团队微信达人统计的响应
   */
  async getTeamWechatTalentStats(teamId, options = {}) {
    try {
      console.log(`获取团队${teamId}的微信达人统计数据`, options)

      const response = await api.post('/team/wechat-talent/stats', {
        团队id: teamId,
        时间范围: options.timeRange || '30d',
        包含非活跃: options.includeInactive || false,
        详细统计: true
      })

      // 数据处理和格式化
      if (response.status === 100) {
        const data = response.data

        // 计算衍生指标
        const processedData = {
          ...data,
          // 计算团队微信达人利用率
          talentUtilizationRate: data.totalTalents > 0 ?
            (data.activeTalents / data.totalTalents * 100).toFixed(1) : 0,

          // 计算成员参与度
          memberParticipationRate: data.totalMembers > 0 ?
            (data.activeMembers / data.totalMembers * 100).toFixed(1) : 0,

          // 处理成员统计数据
          memberStats: (data.memberStats || []).map(member => ({
            ...member,
            performanceLevel: this.calculatePerformanceLevel(member.talentCount, data.avgPerMember)
          }))
        }

        console.log('团队微信达人统计数据处理完成:', processedData)
        return { ...response, data: processedData }
      }

      return response
    } catch (error) {
      console.error('获取团队微信达人统计失败:', error)

      // 不再提供模拟数据，直接抛出错误让前端处理
      throw error
    }
  }

  /**
   * 获取团队达人列表
   * 产品价值：提供团队所有达人的统一视图，支持搜索、筛选和批量操作
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 当前页码
   * @param {number} params.每页数量 - 每页显示数量
   * @param {number} params.成员ID - 按成员筛选（可选）
   * @param {string} params.关键词 - 搜索关键词（可选）
   * @param {string} params.状态筛选 - 达人状态筛选（可选）
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序方式 - 排序方式：'asc', 'desc'
   * @returns {Promise} 包含团队达人列表的响应
   */
  async getTeamTalentList(params) {
    try {
      console.log('获取团队达人列表:', params)
      
      const requestParams = {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        成员ID: params.成员ID || null,
        关键词: params.关键词 || null,
        状态筛选: params.状态筛选 || null,
        排序字段: params.排序字段 || '认领时间',
        排序方式: params.排序方式 || 'desc',
        平台: params.平台 || 'douyin',  // 平台参数：douyin（抖音）、wechat（微信）
        包含成员信息: true,  // 包含认领人详细信息
        包含达人详情: true   // 包含达人详细信息
      }
      
      const response = await api.post('/team/talent/list', requestParams)
      
      if (response.status === 100) {
        // 处理响应数据，添加前端需要的计算字段
        const talents = (response.data.talents || []).map(talent => ({
          ...talent,
          // 添加认领天数
          claimedDays: this.calculateClaimedDays(talent.认领时间),
          // 添加达人价值评级
          valueRating: this.calculateTalentValue(talent),
          // 格式化粉丝数显示
          formattedFansCount: this.formatNumber(talent.粉丝数),
          // 添加状态标签
          statusLabel: this.getTalentStatusLabel(talent.账号状态),
          // 添加认领人简化信息
          claimerInfo: {
            id: talent.claimerId,
            name: talent.claimerName,
            avatar: talent.claimerAvatar,
            position: talent.claimerPosition
          }
        }))
        
        const processedResponse = {
          ...response,
          data: {
            ...response.data,
            talents,
            // 添加列表统计信息
            listStats: {
              totalValue: talents.reduce((sum, t) => sum + (t.estimatedValue || 0), 0),
              avgFansCount: talents.length > 0 ? 
                talents.reduce((sum, t) => sum + t.粉丝数, 0) / talents.length : 0,
              topCategories: this.calculateTopCategories(talents)
            }
          }
        }
        
        console.log('团队达人列表数据处理完成:', processedResponse.data.talents.length, '个达人')
        return processedResponse
      }
      
      return response
    } catch (error) {
      console.error('获取团队达人列表失败:', error)

      // 不再提供模拟数据，直接抛出错误让前端处理
      throw error
    }
  }

  /**
   * 获取团队微信达人列表
   * 产品价值：提供团队所有微信达人的统一视图，支持搜索、筛选和批量操作
   *
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 当前页码
   * @param {number} params.每页数量 - 每页显示数量
   * @param {number} params.成员ID - 按成员筛选（可选）
   * @param {string} params.关键词 - 搜索关键词（可选）
   * @param {string} params.状态筛选 - 达人状态筛选（可选）
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序方式 - 排序方式：'asc', 'desc'
   * @returns {Promise} 包含团队微信达人列表的响应
   */
  async getTeamWechatTalentList(params) {
    try {
      console.log('获取团队微信达人列表:', params)

      const requestParams = {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        成员ID: params.成员ID || null,
        关键词: params.关键词 || null,
        状态筛选: params.状态筛选 || null,
        排序字段: params.排序字段 || '认领时间',
        排序方式: params.排序方式 || 'desc',
        包含成员信息: true,  // 包含认领人详细信息
        包含达人详情: true   // 包含达人详细信息
      }

      const response = await api.post('/team/wechat-talent/list', requestParams)

      if (response.status === 100) {
        // 处理响应数据，添加前端需要的计算字段
        const talents = (response.data.talents || []).map(talent => ({
          ...talent,
          // 添加认领天数
          claimedDays: this.calculateClaimedDays(talent.认领时间),
          // 添加达人价值评级（微信达人基于不同指标）
          valueRating: this.calculateWechatTalentValue(talent),
          // 格式化粉丝数显示
          formattedFansText: talent.fansText || '未知',
          // 添加状态标签
          statusLabel: this.getWechatTalentStatusLabel(talent.hasContact),
          // 添加认领人简化信息
          claimerInfo: {
            id: talent.claimerId,
            name: talent.claimerName,
            avatar: talent.claimerAvatar,
            position: talent.claimerPosition
          }
        }))

        const processedResponse = {
          ...response,
          data: {
            ...response.data,
            talents,
            // 添加列表统计信息
            listStats: {
              totalWithContact: talents.filter(t => t.hasContact).length,
              avgContentTypes: this.calculateAvgContentTypes(talents),
              topCategories: this.calculateTopWechatCategories(talents)
            }
          }
        }

        console.log('团队微信达人列表数据处理完成:', processedResponse.data.talents.length, '个达人')
        return processedResponse
      }

      return response
    } catch (error) {
      console.error('获取团队微信达人列表失败:', error)

      // 不再提供模拟数据，直接抛出错误让前端处理
      throw error
    }
  }

  /**
   * 获取团队达人详情
   * 产品价值：提供达人完整详细信息，支持深度分析和管理决策
   *
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.达人id - 达人id
   * @returns {Promise} 包含达人详情的响应
   */
  async getTalentDetail(params) {
    try {
      console.log('获取团队达人详情:', params)
      
      const response = await api.post('/team/talent/detail', {
        团队id: params.团队id,
        达人id: params.达人id
      })
      
      if (response.status === 100) {
        console.log('成功获取达人详情:', response.data?.昵称)
        return response
      }
      
      return response
    } catch (error) {
      console.error('获取团队达人详情失败:', error)
      throw error
    }
  }

  /**
   * 获取团队达人详细分析数据
   * 产品价值：为团队管理者提供数据驱动的决策支持，包含多维度图表分析
   * 
   * @param {number} teamId - 团队id
   * @param {string} timeRange - 分析时间范围：'7d', '30d', '90d', '1y'
   * @returns {Promise} 包含详细分析数据的响应
   * 
   * 返回数据结构：
   * {
   *   totalValue: number,           // 团队达人总价值
   *   valueGrowth: number,          // 价值增长率
   *   efficiencyIndex: number,      // 效率指数
   *   activityScore: number,        // 活跃度评分
   *   participationRate: number,    // 成员参与率
   *   activeMembers: number,        // 活跃成员数
   *   totalMembers: number,         // 总成员数
   *   trendData: Array,             // 趋势图数据
   *   categoryData: Array,          // 类别分布数据
   *   fansDistribution: Array,      // 粉丝分布数据
   *   memberDetails: Array          // 成员详细数据
   * }
   */
  async getTeamTalentAnalysis(teamId, timeRange = '30d') {
    try {
      console.log(`获取团队${teamId}的达人详细分析，时间范围：${timeRange}`)
      
      const response = await api.post('/team/talent/analysis', {
        团队id: teamId,
        时间范围: timeRange
      })
      
      // 数据处理和验证
      if (response.status === 100) {
        const data = response.data
        
        // 验证必要的数据字段
        const requiredFields = ['totalValue', 'efficiencyIndex', 'activityScore', 'participationRate']
        for (const field of requiredFields) {
          if (data[field] === undefined || data[field] === null) {
            console.warn(`分析数据缺少必要字段: ${field}`)
            data[field] = 0
          }
        }
        
        // 确保数组字段不为空
        data.trendData = data.trendData || []
        data.categoryData = data.categoryData || []
        data.fansDistribution = data.fansDistribution || []
        data.memberDetails = data.memberDetails || []
        
        console.log('团队达人详细分析数据处理完成:', {
          totalValue: data.totalValue,
          memberCount: data.memberDetails.length,
          trendDataPoints: data.trendData.length
        })
        
        return { ...response, data }
      }
      
      return response
    } catch (error) {
      console.error('获取团队达人详细分析失败:', error)
      
      // 提供降级数据，确保UI正常显示
      return {
        status: 100,
        message: '使用模拟分析数据',
        data: this.generateMockAnalysisData(teamId, timeRange)
      }
    }
  }


  
  /**
   * 批量操作团队达人
   * 产品价值：提升管理效率，支持批量转移、批量标记等操作
   * 
   * @param {Object} params - 批量操作参数
   * @param {Array} params.达人id列表 - 要操作的达人id数组
   * @param {string} params.操作类型 - 操作类型：'transfer', 'tag', 'export'
   * @param {Object} params.操作参数 - 操作相关参数
   * @returns {Promise} 批量操作结果
   */
  async batchOperateTalents(params) {
    try {
      console.log('执行达人批量操作:', params)
      
      const response = await api.post('/team/talent/batch-operate', {
        达人id列表: params.达人id列表,
        操作类型: params.操作类型,
        操作参数: params.操作参数,
        团队id: params.团队id,
        操作人ID: params.操作人ID
      })
      
      if (response.status === 100) {
        console.log('批量操作成功:', response.data)
        
        // 记录批量操作日志
        this.logTeamTalentOperation('batch_' + params.操作类型, {
          talentCount: params.达人id列表.length,
          operationType: params.操作类型,
          params: params.操作参数
        })
      }
      
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw new Error('批量操作失败，请检查操作参数')
    }
  }
  
  /**
   * 获取团队达人操作日志
   * 产品价值：提供操作审计功能，增强团队管理的透明度
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.操作类型 - 操作类型筛选
   * @param {string} params.时间范围 - 时间范围
   * @returns {Promise} 操作日志列表
   */
  async getTeamTalentLogs(params) {
    try {
      const response = await api.post('/team/talent/logs', {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        操作类型: params.操作类型 || null,
        时间范围: params.时间范围 || '30d'
      })
      
      return response
    } catch (error) {
      console.error('获取团队达人日志失败:', error)
      return {
        status: 100,
        data: { logs: [], total: 0 },
        message: '暂无日志数据'
      }
    }
  }
  
  /**
   * 导出团队达人数据
   * 产品价值：支持数据分析和外部工具集成
   * 
   * @param {Object} params - 导出参数
   * @param {number} params.团队id - 团队id
   * @param {string} params.导出格式 - 'excel', 'csv', 'json'
   * @param {Array} params.字段列表 - 要导出的字段
   * @param {Object} params.筛选条件 - 导出数据的筛选条件
   * @returns {Promise} 导出任务信息或下载链接
   */
  async exportTeamTalents(params) {
    try {
      console.log('开始导出团队达人数据:', params)
      
      const response = await api.post('/team/talent/export', {
        团队id: params.团队id,
        导出格式: params.导出格式 || 'excel',
        字段列表: params.字段列表 || ['基本信息', '统计数据', '认领信息'],
        筛选条件: params.筛选条件 || {},
        导出范围: params.导出范围 || 'current_filter' // 'all', 'current_filter', 'selected'
      })
      
      if (response.status === 100) {
        console.log('导出任务创建成功:', response.data)
      }
      
      return response
    } catch (error) {
      console.error('导出失败:', error)
      throw new Error('导出功能暂时不可用，请稍后重试')
    }
  }
  
  // ==================== 工具方法 ====================
  
  /**
   * 计算达人认领天数
   * @param {string} claimedTime - 认领时间
   * @returns {number} 认领天数
   */
  calculateClaimedDays(claimedTime) {
    if (!claimedTime) return 0
    const claimedDate = new Date(claimedTime)
    const now = new Date()
    const diffTime = Math.abs(now - claimedDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }
  
  /**
   * 计算达人价值评级
   * @param {Object} talent - 达人信息
   * @returns {string} 价值评级：'S', 'A', 'B', 'C'
   */
  calculateTalentValue(talent) {
    const fansCount = talent.粉丝数 || 0
    const engagementRate = talent.互动率 || 0
    const categoryScore = this.getCategoryScore(talent.categories)
    
    // 综合评分算法
    const score = fansCount * 0.4 + engagementRate * 1000 * 0.3 + categoryScore * 0.3
    
    if (score >= 500000) return 'S'
    if (score >= 100000) return 'A'
    if (score >= 50000) return 'B'
    return 'C'
  }

  /**
   * 计算微信达人价值评级
   * @param {Object} talent - 微信达人信息
   * @returns {string} 价值评级：'S', 'A', 'B', 'C'
   */
  calculateWechatTalentValue(talent) {
    const hasContact = talent.hasContact || 0
    const contentTypes = talent.contentTypes || []
    const categories = talent.categories || []

    // 微信达人评分算法（基于联系方式、内容类型、类别等）
    let score = 0

    // 有联系方式加分
    if (hasContact) score += 50

    // 内容类型多样性加分
    score += contentTypes.length * 10

    // 类别多样性加分
    score += categories.length * 15

    // GMV文本解析加分
    if (talent.gmvText && talent.gmvText !== '未知') {
      score += 30
    }

    if (score >= 100) return 'S'
    if (score >= 70) return 'A'
    if (score >= 40) return 'B'
    return 'C'
  }

  /**
   * 获取微信达人状态标签
   * @param {number} hasContact - 是否有联系方式
   * @returns {Object} 状态标签信息
   */
  getWechatTalentStatusLabel(hasContact) {
    return hasContact ?
      { text: '有联系方式', color: 'green' } :
      { text: '无联系方式', color: 'orange' }
  }

  /**
   * 计算平均内容类型数
   * @param {Array} talents - 达人列表
   * @returns {number} 平均内容类型数
   */
  calculateAvgContentTypes(talents) {
    if (talents.length === 0) return 0
    const totalTypes = talents.reduce((sum, t) => sum + (t.contentTypes?.length || 0), 0)
    return (totalTypes / talents.length).toFixed(1)
  }

  /**
   * 计算微信达人热门类别
   * @param {Array} talents - 达人列表
   * @returns {Array} 热门类别列表
   */
  calculateTopWechatCategories(talents) {
    const categoryCount = {}

    talents.forEach(talent => {
      if (talent.categories && Array.isArray(talent.categories)) {
        talent.categories.forEach(category => {
          categoryCount[category] = (categoryCount[category] || 0) + 1
        })
      }
    })

    return Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([category, count]) => ({ category, count }))
  }

  /**
   * 获取类别评分
   * @param {Object} categories - 达人类别
   * @returns {number} 类别评分
   */
  getCategoryScore(categories) {
    if (!categories) return 0
    
    // 热门类别权重映射
    const categoryWeights = {
      '美妆': 100,
      '时尚': 90,
      '美食': 85,
      '旅游': 80,
      '科技': 75,
      '健身': 70,
      '宠物': 65,
      '其他': 50
    }
    
    const categoryList = Object.keys(categories)
    if (categoryList.length === 0) return 0
    
    const avgWeight = categoryList.reduce((sum, cat) => {
      return sum + (categoryWeights[cat] || 50)
    }, 0) / categoryList.length
    
    return avgWeight
  }
  
  /**
   * 获取达人状态标签
   * @param {number} status - 状态码
   * @returns {Object} 状态标签信息
   */
  getTalentStatusLabel(status) {
    const statusMap = {
      0: { text: '正常', color: 'green' },
      1: { text: '已注销', color: 'red' },
      2: { text: '异常', color: 'orange' }
    }
    
    return statusMap[status] || statusMap[0]
  }
  
  /**
   * 计算绩效等级
   * @param {number} talentCount - 达人数量
   * @param {number} avgCount - 平均数量
   * @returns {string} 绩效等级
   */
  calculatePerformanceLevel(talentCount, avgCount) {
    if (talentCount >= avgCount * 1.5) return 'excellent'
    if (talentCount >= avgCount * 1.2) return 'good'
    if (talentCount >= avgCount * 0.8) return 'average'
    return 'below_average'
  }
  
  /**
   * 计算热门类别
   * @param {Array} talents - 达人列表
   * @returns {Array} 热门类别统计
   */
  calculateTopCategories(talents) {
    const categoryCount = {}
    
    talents.forEach(talent => {
      if (talent.categories) {
        Object.keys(talent.categories).forEach(category => {
          categoryCount[category] = (categoryCount[category] || 0) + 1
        })
      }
    })
    
    return Object.entries(categoryCount)
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }
  
  /**
   * 格式化数字显示
   * @param {number} num - 数字
   * @returns {string} 格式化后的字符串
   */
  formatNumber(num) {
    if (!num) return '0'
    if (num >= 10000) return (num / 10000).toFixed(1) + 'w'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
    return num.toString()
  }
  
  /**
   * 记录团队达人操作日志
   * @param {string} operation - 操作类型
   * @param {Object} details - 操作详情
   */
  logTeamTalentOperation(operation, details) {
    // 前端埋点日志，用于用户行为分析
    console.log('团队达人操作日志:', {
      operation,
      details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    })
    
    // 可以集成第三方分析工具，如百度统计、Google Analytics等
    // analytics.track('team_talent_operation', { operation, ...details })
  }
  
  /**
   * 生成模拟团队统计数据（开发阶段使用）
   * @param {number} teamId - 团队id
   * @returns {Object} 模拟统计数据
   */
  generateMockTeamStats(teamId) {
    return {
      totalTalents: 156,
      activeMembers: 8,
      monthlyNew: 23,
      avgPerMember: 19.5,
      talentUtilizationRate: 85.2,
      memberParticipationRate: 72.7,
      topPerformers: [
        { userId: 1, username: '张小明', talentCount: 45 },
        { userId: 2, username: '李小红', talentCount: 32 },
        { userId: 3, username: '王小华', talentCount: 28 }
      ],
      categoryDistribution: {
        '美妆': 45,
        '时尚': 32,
        '美食': 28,
        '旅游': 25,
        '科技': 18,
        '其他': 8
      },
      memberStats: [
        { userId: 1, username: '张小明', talentCount: 45, performanceLevel: 'excellent' },
        { userId: 2, username: '李小红', talentCount: 32, performanceLevel: 'good' },
        { userId: 3, username: '王小华', talentCount: 28, performanceLevel: 'good' },
        { userId: 4, username: '刘小美', talentCount: 25, performanceLevel: 'average' },
        { userId: 5, username: '陈小强', talentCount: 22, performanceLevel: 'average' }
      ]
    }
  }
  
  /**
   * 生成模拟达人列表数据（开发阶段使用）
   * @param {Object} params - 查询参数
   * @returns {Array} 模拟达人列表
   */
  generateMockTalentList(params) {
    const mockData = []
    const members = [
      { id: 1, name: '张小明', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member1' },
      { id: 2, name: '李小红', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member2' },
      { id: 3, name: '王小华', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member3' },
      { id: 4, name: '刘小美', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member4' },
      { id: 5, name: '陈小强', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member5' }
    ]
    
    const categories = ['美妆', '时尚', '美食', '旅游', '科技', '健身', '宠物']
    const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉']
    
    for (let i = 1; i <= (params.每页数量 || 20); i++) {
      const randomMember = members[Math.floor(Math.random() * members.length)]
      const fansCount = Math.floor(Math.random() * 1000000) + 1000
      const claimedTime = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
      
      mockData.push({
        id: i,
        nickname: `达人${i}号`,
        account_douyin: `douyin_user_${i}`,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=talent${i}`,
        粉丝数: fansCount,
        formattedFansCount: this.formatNumber(fansCount),
        city: cities[Math.floor(Math.random() * cities.length)],
        categories: { [categories[Math.floor(Math.random() * categories.length)]]: true },
        账号状态: Math.random() > 0.9 ? 1 : 0,
        认领时间: claimedTime.toISOString(),
        claimedDays: this.calculateClaimedDays(claimedTime.toISOString()),
        claimerId: randomMember.id,
        claimerName: randomMember.name,
        claimerAvatar: randomMember.avatar,
        claimerPosition: '团队成员',
        valueRating: this.calculateTalentValue({ 粉丝数: fansCount, 互动率: Math.random() * 0.1 }),
        statusLabel: this.getTalentStatusLabel(Math.random() > 0.9 ? 1 : 0),
        claimerInfo: {
          id: randomMember.id,
          name: randomMember.name,
          avatar: randomMember.avatar,
          position: '团队成员'
        }
      })
    }
    
    return mockData
  }

  /**
   * 生成模拟的团队达人详细分析数据
   * 用于API调用失败时的降级处理
   * 
   * @param {number} teamId - 团队id
   * @param {string} timeRange - 时间范围
   * @returns {Object} 模拟的分析数据
   */
  generateMockAnalysisData(teamId, timeRange) {
    // 根据时间范围生成不同数量的趋势数据点
    const timeRangeMapping = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    }
    
    const days = timeRangeMapping[timeRange] || 30
    const dataPoints = Math.min(days, 30) // 最多30个数据点
    
    // 生成趋势数据
    const trendData = []
    const baseTalentCount = 45
    const baseFansCount = 89000
    
    for (let i = 0; i < dataPoints; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (dataPoints - 1 - i))
      const dateString = date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      
      // 模拟增长趋势
      const talentCount = baseTalentCount + Math.floor(Math.random() * i * 2)
      const totalFans = baseFansCount + Math.floor(Math.random() * i * 1000)
      
      trendData.push({
        date: dateString,
        talentCount,
        totalFans
      })
    }
    
    // 生成类别分布数据
    const categoryData = [
      { name: '美妆时尚', value: Math.floor(Math.random() * 15) + 20 },
      { name: '生活方式', value: Math.floor(Math.random() * 15) + 15 },
      { name: '科技数码', value: Math.floor(Math.random() * 10) + 10 },
      { name: '美食探店', value: Math.floor(Math.random() * 10) + 8 },
      { name: '其他', value: Math.floor(Math.random() * 10) + 15 }
    ]
    
    // 生成粉丝分布数据
    const fansDistribution = [
      Math.floor(Math.random() * 10) + 5,   // <1万
      Math.floor(Math.random() * 15) + 15,  // 1-5万
      Math.floor(Math.random() * 15) + 10,  // 5-10万
      Math.floor(Math.random() * 12) + 8,   // 10-50万
      Math.floor(Math.random() * 7) + 3,    // 50-100万
      Math.floor(Math.random() * 4) + 1     // >100万
    ]
    
    // 生成成员详细数据
    const memberNames = ['张小美', '李达人', '王网红', '陈主播', '刘博主', '赵UP主']
    const memberDetails = memberNames.slice(0, 5).map((name, index) => ({
      key: String(index + 1),
      memberName: name,
      talentCount: Math.floor(Math.random() * 17) + 8,
      totalFans: Math.floor(Math.random() * 350000) + 150000,
      performanceScore: Math.floor(Math.random() * 30) + 65,
      growthRate: parseFloat((Math.random() * 30 - 5).toFixed(1)),
      fansQuality: Math.floor(Math.random() * 25) + 70,
      activityLevel: Math.floor(Math.random() * 20) + 75,
      conversionRate: Math.floor(Math.random() * 25) + 60
    }))
    
    return {
      totalValue: parseFloat((Math.random() * 100 + 100).toFixed(1)),
      valueGrowth: parseFloat((Math.random() * 15 + 5).toFixed(1)),
      efficiencyIndex: Math.floor(Math.random() * 20) + 75,
      activityScore: parseFloat((Math.random() * 20 + 70).toFixed(1)),
      participationRate: parseFloat((Math.random() * 25 + 60).toFixed(1)),
      activeMembers: Math.floor(Math.random() * 3) + 5,
      totalMembers: Math.floor(Math.random() * 4) + 8,
      trendData,
      categoryData,
      fansDistribution,
      memberDetails
    }
  }

  /**
   * 生成模拟微信达人列表数据（开发阶段使用）
   * @param {Object} params - 查询参数
   * @returns {Array} 模拟微信达人列表
   */
  generateMockWechatTalentList(params) {
    const mockData = []
    const members = [
      { id: 1, name: '张小明', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member1' },
      { id: 2, name: '李小红', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member2' },
      { id: 3, name: '王小华', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member3' },
      { id: 4, name: '刘小美', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member4' },
      { id: 5, name: '陈小强', avatar: 'https://api.dicebear.com/7.x/personas/svg?seed=member5' }
    ]

    const wechatNames = [
      '微信美妆达人小雅', '时尚博主小丽', '美食分享者小王', '旅游达人小张',
      '科技评测师小李', '健身教练小陈', '宠物博主小刘', '生活分享者小赵',
      '摄影师小孙', '音乐达人小周', '读书分享者小吴', '手工达人小郑',
      '育儿专家小钱', '理财达人小冯', '游戏主播小何', '舞蹈老师小沈'
    ]

    const contentTypes = [
      ['图文', '视频'], ['短视频', '直播'], ['图文'], ['视频', '直播'],
      ['短视频'], ['图文', '短视频'], ['视频'], ['图文', '视频', '直播']
    ]

    const categories = [
      ['美妆', '护肤'], ['时尚', '穿搭'], ['美食', '探店'], ['旅游', '攻略'],
      ['科技', '数码'], ['健身', '运动'], ['宠物', '萌宠'], ['生活', '日常'],
      ['摄影', '艺术'], ['音乐', '娱乐'], ['读书', '文化'], ['手工', 'DIY'],
      ['育儿', '亲子'], ['理财', '投资'], ['游戏', '电竞'], ['舞蹈', '才艺']
    ]

    for (let i = 0; i < 20; i++) {
      const member = members[i % members.length]
      const nameIndex = i % wechatNames.length
      const contentTypeIndex = i % contentTypes.length
      const categoryIndex = i % categories.length

      mockData.push({
        talentId: 1000 + i,
        nickname: wechatNames[nameIndex],
        finderUsername: `wx_${nameIndex}_${i}`,
        avatar: `https://api.dicebear.com/7.x/personas/svg?seed=wechat${i}`,
        fansText: ['1万+', '5万+', '10万+', '50万+', '100万+'][Math.floor(Math.random() * 5)],
        gmvText: ['未知', '1万+', '5万+', '10万+', '50万+'][Math.floor(Math.random() * 5)],
        contentTypes: contentTypes[contentTypeIndex],
        categories: categories[categoryIndex],
        hasContact: Math.random() > 0.3 ? 1 : 0,
        claimTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        claimerId: member.id,
        claimerName: member.name,
        claimerAvatar: member.avatar,
        claimerPosition: ['团队负责人', '高级成员', '普通成员'][Math.floor(Math.random() * 3)]
      })
    }

    return mockData
  }
}

// 创建团队达人服务实例
const teamTalentService = new TeamTalentService()

// 导出服务实例和主要方法
export default teamTalentService
export const {
  getTeamTalentStats,
  getTeamWechatTalentStats,  // 新增微信达人统计方法
  getTeamTalentList,
  getTeamWechatTalentList,  // 新增微信达人列表方法
  getTeamTalentAnalysis,
  batchOperateTalents,
  getTeamTalentLogs,
  exportTeamTalents
} = teamTalentService