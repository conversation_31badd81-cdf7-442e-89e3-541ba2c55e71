<template>
  <div :class="$style.leftSidebar">
    <!-- 导航头部 -->
    <div :class="$style.navHeader">
      <h3>配置步骤</h3>
      <a-progress
        :percent="configProgress"
        size="small"
        :stroke-color="configProgress === 100 ? '#52c41a' : '#1890ff'"
      />
    </div>

    <!-- 配置菜单 -->
    <a-menu
      :selectedKeys="[currentStep]"
      mode="inline"
      :class="$style.configMenu"
      @click="handleStepChange"
    >
      <a-menu-item key="basic" :class="$style.menuItem">
        <template #icon>
          <UserOutlined :style="{ color: isStepCompleted('basic') ? '#52c41a' : '#999' }" />
        </template>
        <span>基础信息</span>
        <a-badge v-if="isStepCompleted('basic')" status="success" />
      </a-menu-item>

      <a-menu-item key="prompt" :class="$style.menuItem">
        <template #icon>
          <EditOutlined :style="{ color: isStepCompleted('prompt') ? '#52c41a' : '#999' }" />
        </template>
        <span>提示词配置</span>
        <a-badge v-if="isStepCompleted('prompt')" status="success" />
      </a-menu-item>

      <a-menu-item key="model" :class="$style.menuItem">
        <template #icon>
          <RobotOutlined :style="{ color: isStepCompleted('model') ? '#52c41a' : '#999' }" />
        </template>
        <span>模型配置</span>
        <a-badge v-if="isStepCompleted('model')" status="success" />
      </a-menu-item>

      <a-menu-item key="rag" :class="$style.menuItem">
        <template #icon>
          <DatabaseOutlined :style="{ color: isStepCompleted('rag') ? '#52c41a' : '#999' }" />
        </template>
        <span>RAG配置</span>
        <a-badge v-if="isStepCompleted('rag')" status="success" />
      </a-menu-item>

      <a-menu-item key="tools" :class="$style.menuItem">
        <template #icon>
          <ToolOutlined :style="{ color: isStepCompleted('tools') ? '#52c41a' : '#999' }" />
        </template>
        <span>工具配置</span>
        <a-badge v-if="isStepCompleted('tools')" status="success" />
      </a-menu-item>

      <a-menu-item key="output" :class="$style.menuItem">
        <template #icon>
          <ExportOutlined :style="{ color: isStepCompleted('output') ? '#52c41a' : '#999' }" />
        </template>
        <span>结构化输出</span>
        <a-badge v-if="isStepCompleted('output')" status="success" />
      </a-menu-item>

      <a-menu-item key="variables" :class="$style.menuItem">
        <template #icon>
          <SettingOutlined :style="{ color: isStepCompleted('variables') ? '#52c41a' : '#999' }" />
        </template>
        <span>自定义变量</span>
        <a-badge v-if="isStepCompleted('variables')" status="success" />
      </a-menu-item>
    </a-menu>

    <!-- 快速操作 -->
    <div :class="$style.quickActions">
      <a-space direction="vertical" style="width: 100%">
        <a-button 
          type="default" 
          size="small" 
          block 
          :class="$style.resetBtn"
          @click="handleReset"
        >
          <ReloadOutlined />
          重置配置
        </a-button>
        <a-button 
          type="primary" 
          size="small" 
          block
          @click="handleExport"
        >
          <DownloadOutlined />
          导出配置
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  UserOutlined,
  EditOutlined,
  RobotOutlined,
  DatabaseOutlined,
  ExportOutlined,
  ToolOutlined,
  SettingOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  currentStep: {
    type: String,
    default: 'basic'
  },
  agentForm: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['step-change', 'reset', 'export'])

// 计算配置完成度
const configProgress = computed(() => {
  const steps = ['basic', 'model', 'rag', 'output', 'tools']
  const completedSteps = steps.filter(step => isStepCompleted(step)).length
  return Math.round((completedSteps / steps.length) * 100)
})

// 验证步骤完成状态
const isStepCompleted = (step) => {
  const form = props.agentForm
  switch (step) {
    case 'basic':
      return !!(form.智能体名称 && form.智能体描述 && form.系统提示词)
    case 'model':
      return !!(form.对话模型)
    case 'rag':
      return form.启用RAG ? !!(form.知识库列表?.length) : true
    case 'output':
      return !!(form.输出模式)
    case 'tools':
      return true // 工具配置是可选的
    default:
      return false
  }
}

// 处理步骤切换
const handleStepChange = ({ key }) => {
  emit('step-change', key)
}

// 处理重置
const handleReset = () => {
  emit('reset')
}

// 处理导出
const handleExport = () => {
  emit('export')
}
</script>

<style module>
/* 左侧导航栏 */
.leftSidebar {
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  flex-shrink: 0;
  flex-grow: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 导航头部 */
.navHeader {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.navHeader h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 配置菜单 */
.configMenu {
  flex: 1;
  border: none;
  background: transparent;
  overflow-y: auto;
}

.menuItem {
  margin: 4px 12px;
  border-radius: 6px;
  transition: all 0.2s;
}

.menuItem:hover {
  background: #f0f8ff;
}

/* 快速操作区域 */
.quickActions {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.resetBtn {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.resetBtn:hover {
  background: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 1000px) {
  .leftSidebar {
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
    max-height: 300px !important;
    order: 1 !important;
  }
  
  .configMenu {
    flex-direction: row !important;
    height: auto !important;
  }
  
  .quickActions {
    border-top: none !important;
    border-left: 1px solid #f0f0f0 !important;
    padding: 16px !important;
  }
}
</style>
