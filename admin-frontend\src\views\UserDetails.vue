<template>
  <div>
    <a-page-header
      style="border: 1px solid rgb(235, 237, 240); margin-bottom: 16px;"
      title="用户详情"
      @back="() => router.back()"
    />
    <a-card :bordered="false">
      <a-spin :spinning="loading">
        <!-- 用户基本信息 -->
        <a-descriptions 
          v-if="userDetails"
                title="用户基本信息" 
          bordered 
          :column="2"
          style="margin-bottom: 24px;"
        >
          <a-descriptions-item label="用户id">{{ userDetails.id }}</a-descriptions-item>
          <a-descriptions-item label="用户名">{{ userDetails.昵称 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="邮箱">{{ userDetails.email || '-' }}</a-descriptions-item>
          <a-descriptions-item label="手机号">{{ userDetails.phone || '-' }}</a-descriptions-item>
          <a-descriptions-item label="注册时间">{{ formatDate(userDetails.created_at) }}</a-descriptions-item>
          <a-descriptions-item label="等级">{{ userDetails.level ?? '-' }}</a-descriptions-item>
          <a-descriptions-item label="是否管理员">
            <a-tag :color="userDetails.is_admin ? 'success' : 'default'">
              {{ userDetails.is_admin ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="是否激活">
            <a-tag :color="userDetails.is_active ? 'success' : 'error'">
              {{ userDetails.is_active ? '已激活' : '未激活' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 用户登录信息 -->
        <a-divider orientation="left">登录信息</a-divider>
        <a-spin :spinning="loginInfoLoading">
          <a-descriptions 
            v-if="loginInfo"
            title="详细登录信息" 
            bordered 
            :column="2"
            style="margin-bottom: 24px;"
          >
            <a-descriptions-item label="用户状态">
              <a-tag :color="loginInfo.用户状态 === '活跃' ? 'success' : 'warning'">
                {{ loginInfo.用户状态 || '未知' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="总登录次数">
              <a-statistic 
                :value="loginInfo.登录次数 || 0" 
                :value-style="{ color: '#1890ff' }"
                suffix="次"
              />
            </a-descriptions-item>
            <a-descriptions-item label="最近7天登录次数">
              <a-statistic 
                :value="loginInfo.最近7天登录次数 || 0" 
                :value-style="{ color: loginInfo.最近7天登录次数 > 0 ? '#52c41a' : '#faad14' }"
                suffix="次"
              />
            </a-descriptions-item>
            <a-descriptions-item label="上次登录时间">
              <span v-if="loginInfo.上次登录时间">
                {{ formatDate(loginInfo.上次登录时间) }}
              </span>
              <a-tag v-else color="default">从未登录</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="上次登录IP">
              <a-tag v-if="loginInfo.IP地址" color="blue">{{ loginInfo.IP地址 }}</a-tag>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="首次登录时间">
              <span v-if="loginInfo.首次登录时间">
                {{ formatDate(loginInfo.首次登录时间) }}
              </span>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="最后活跃时间">
              <span v-if="loginInfo.最后活跃时间">
                {{ formatDate(loginInfo.最后活跃时间) }}
              </span>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="设备信息">
              <span v-if="loginInfo.设备信息">{{ loginInfo.设备信息 }}</span>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="地理位置">
              <span v-if="loginInfo.地理位置">{{ loginInfo.地理位置 }}</span>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
          
          <!-- 登录活跃度图表 -->
          <a-card 
            v-if="loginInfo" 
            title="登录活跃度分析" 
            size="small" 
            style="margin-bottom: 24px;"
          >
            <div class="login-stats">
              <div class="stat-item">
                <div class="stat-title">活跃度评分</div>
                <a-progress 
                  :percent="getActivityScore()" 
                  :stroke-color="getActivityColor()"
                  :show-info="true"
                  :format="percent => `${percent}%`"
                />
              </div>
              <div class="stat-item">
                <div class="stat-title">登录频率</div>
                <div class="stat-value">
                  {{ getLoginFrequency() }}
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-title">账户年龄</div>
                <div class="stat-value">
                  {{ getUserAccountAge() }}
                </div>
              </div>
            </div>
          </a-card>
          
          <a-empty v-else-if="!loginInfoLoading" description="无法获取登录信息" />
        </a-spin>

        <!-- 用户关联店铺列表 -->
        <a-divider orientation="left">关联店铺</a-divider>
        
        <a-spin :spinning="shopsLoading">
          <a-list
            v-if="userShops && userShops.length > 0"
            item-layout="horizontal"
            :data-source="userShops"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :src="item.avatar" :size="64" shape="square">
                      <template #icon><shop-outlined /></template>
                    </a-avatar>
                  </template>
                  <template #title>
                    <span>{{ item.shop_name }}</span>
                  </template>
                  <template #description>
                    <div>
                      <p>店铺ID: {{ item.shop_id }}</p>
                      <p v-if="item.created_at">创建时间: {{ formatDate(item.created_at) }}</p>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
          <a-empty v-else description="该用户未关联任何店铺" />
        </a-spin>

        <a-empty v-if="!userDetails && !loading && !fetchError" description="未能加载用户数据或用户不存在" />
        <a-result
          v-if="fetchError && !userDetails"
          status="error"
          title="数据加载失败"
          :sub-title="fetchError"
        >
          <template #extra>
            <a-button type="primary" @click="fetchUserDetails">重试</a-button>
          </template>
        </a-result>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ShopOutlined } from '@ant-design/icons-vue';
import superAdminService from '../services/superAdminService'; // 使用统一的API服务
import { useSuperAdminRequest } from '../composables/useApiRequest'; // 使用统一的响应处理
import { formatDate, getAccountAge } from '../utils/dateUtils'; // 使用统一的日期工具

const route = useRoute();
const router = useRouter();

const userDetails = ref(null);
const fetchError = ref('');

// 店铺相关状态
const userShops = ref([]);
const shopsLoading = ref(false);

// 登录信息状态
const loginInfo = ref(null);
const loginInfoLoading = ref(false);

// 使用统一的API响应处理
const { loading, 执行API请求 } = useSuperAdminRequest();

// formatDate 函数已从 dateUtils 导入，无需重复定义

// 计算活跃度评分 (0-100)
const getActivityScore = () => {
  if (!loginInfo.value) return 0;
  
  const recent7Days = loginInfo.value.最近7天登录次数 || 0;
  const totalLogins = loginInfo.value.登录次数 || 0;
  
  // 基于最近7天登录次数和总登录次数计算活跃度
  let score = 0;
  
  // 最近7天登录次数权重 (60%)
  if (recent7Days >= 7) score += 60;
  else if (recent7Days >= 3) score += 40;
  else if (recent7Days >= 1) score += 20;
  
  // 总登录次数权重 (40%)
  if (totalLogins >= 100) score += 40;
  else if (totalLogins >= 50) score += 30;
  else if (totalLogins >= 10) score += 20;
  else if (totalLogins >= 1) score += 10;
  
  return Math.min(score, 100);
};

// 获取活跃度颜色
const getActivityColor = () => {
  const score = getActivityScore();
  if (score >= 80) return '#52c41a'; // 绿色
  if (score >= 60) return '#1890ff'; // 蓝色
  if (score >= 40) return '#faad14'; // 黄色
  return '#ff4d4f'; // 红色
};

// 获取登录频率描述
const getLoginFrequency = () => {
  if (!loginInfo.value) return '未知';
  
  const recent7Days = loginInfo.value.最近7天登录次数 || 0;
  
  if (recent7Days >= 7) return '每日登录';
  if (recent7Days >= 3) return '经常登录';
  if (recent7Days >= 1) return '偶尔登录';
  return '很少登录';
};

// 获取账户年龄（使用统一的工具函数）
const getUserAccountAge = () => {
  return getAccountAge(userDetails.value?.created_at);
};

const fetchUserDetails = async () => {
  fetchError.value = '';
  const userId = route.params.userId;
  if (!userId) {
    message.error('用户id未提供');
    fetchError.value = '用户id未在路由参数中找到。';
    return;
  }

  // 使用统一的API调用，自动处理错误和加载状态
  const result = await 执行API请求(() => superAdminService.getUserDetail(userId));
  
  if (result) {
    // 成功获取用户详情
    userDetails.value = result;
    // 获取用户详情成功后，加载关联店铺和最后登录时间
    await Promise.all([
      fetchUserShops(userId),
      fetchUserLoginInfo(userId)
    ]);
  } else {
    // 失败时设置错误信息（错误处理已在executeWithResponse中完成）
    fetchError.value = '获取用户详情失败';
    userDetails.value = null;
  }
};

// 获取用户关联店铺
const fetchUserShops = async (userId) => {
  shopsLoading.value = true;
  
  // 使用统一的API调用，静默处理错误（不显示错误提示）
  const result = await 执行API请求(() => superAdminService.getUserShops(userId), '', { showErrorMessage: false });
  
  if (result && Array.isArray(result.items)) {
    userShops.value = result.items;
  } else {
    console.warn('获取用户关联店铺数据格式不正确或items为空');
    userShops.value = [];
  }
  
  shopsLoading.value = false;
};

// 获取用户详细登录信息 (使用新的优化接口)
const fetchUserLoginInfo = async (userId) => {
  loginInfoLoading.value = true;
  
  // 使用统一的API调用，静默处理错误（不显示错误提示）
  const result = await 执行API请求(() => superAdminService.getUserLoginHistory(userId), '', { showErrorMessage: false });
  
  if (result) {
    loginInfo.value = result;
    console.log('获取到的登录信息:', loginInfo.value);
  } else {
    console.warn('获取用户登录信息失败');
    loginInfo.value = null;
  }
  
  loginInfoLoading.value = false;
};

onMounted(() => {
  fetchUserDetails();
});
</script>

<style scoped>
.login-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.ant-descriptions-item-label {
  font-weight: 600;
}

.ant-statistic {
  text-align: center;
}
</style>