<template>
  <div class="store-info-module">
    <!-- 店铺概览仪表板 -->
    <div class="dashboard-section">
      <a-row :gutter="[16, 16]">
        <!-- 统计卡片 -->
        <a-col :xs="24" :sm="12" :md="6" v-for="stat in statsCards" :key="stat.key">
          <a-card class="stat-card" :bordered="false">
            <div class="stat-content">
              <div class="stat-icon" :style="{ background: stat.color }">
                <component :is="stat.icon" />
              </div>
              <div class="stat-data">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 店铺管理区域 -->
    <a-card class="management-card" title="我的店铺" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showBindModal = true">
            <plus-outlined />
            绑定店铺
          </a-button>
          <a-button @click="refreshStoreData">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索和筛选区域 -->
      <div class="filter-section">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8">
            <a-input-search
              v-model:value="searchText"
              placeholder="搜索店铺名称或ID"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-select
              v-model:value="filterStatus"
              placeholder="店铺状态"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option value="正常">正常</a-select-option>
              <a-select-option value="异常">异常</a-select-option>
              <a-select-option value="待审核">待审核</a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-select
              v-model:value="filterType"
              placeholder="店铺类型"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option value="母婴用品">母婴用品</a-select-option>
              <a-select-option value="食品饮料">食品饮料</a-select-option>
              <a-select-option value="个人护理">个人护理</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </div>

      <!-- 店铺列表 -->
      <div class="store-list">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :lg="8" v-for="store in filteredStores" :key="store.店铺ID">
            <a-card class="store-card" :bordered="false" hoverable>
              <div class="store-content">
                <!-- 店铺头像 -->
                <div class="store-avatar">
                  <a-avatar :size="60" :src="store.店铺头像 || '/images/default_store.png'" />
                </div>
                
                <!-- 店铺信息 -->
                <div class="store-info">
                  <h3 class="store-name">{{ store.店铺名称 }}</h3>
                  <p class="store-id">ID: {{ store.shop_id || store.店铺ID }}</p>
                  <div class="store-meta">
                    <a-tag :color="getStatusColor(store.店铺状态)">
                      {{ store.店铺状态 || '正常' }}
                    </a-tag>
                    <span class="store-type">{{ store.店铺类型 || '未分类' }}</span>
                  </div>
                  <div class="store-date">
                    关联时间: {{ formatDate(store.关联时间 || store.创建时间) }}
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="store-actions">
                <a-button type="link" @click="viewStoreDetail(store)">
                  <eye-outlined />
                  查看详情
                </a-button>
                <a-button type="link" @click="editStore(store)">
                  <edit-outlined />
                  编辑
                </a-button>
                <a-dropdown>
                  <a-button type="link">
                    <ellipsis-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="exportStoreData(store)">
                        <download-outlined />
                        导出数据
                      </a-menu-item>
                      <a-menu-item @click="unbindStore(store)" danger>
                        <disconnect-outlined />
                        解除绑定
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 空状态 -->
        <a-empty v-if="filteredStores.length === 0" description="暂无店铺数据">
          <a-button type="primary" @click="showBindModal = true">
            立即绑定店铺
          </a-button>
        </a-empty>
      </div>
    </a-card>

    <!-- 绑定店铺弹窗 -->
    <StoreBindModal
      v-model:visible="showBindModal"
      @success="handleBindSuccess"
    />

    <!-- 店铺详情抽屉 -->
    <StoreDetailDrawer
      v-model:visible="showDetailDrawer"
      :store-data="selectedStore"
      @update="handleStoreUpdate"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  EllipsisOutlined,
  DownloadOutlined,
  DisconnectOutlined,
  ShopOutlined,
  RiseOutlined,
  HeartOutlined
} from '@ant-design/icons-vue'

// 导入子组件
import StoreBindModal from './StoreBindModal.vue'
import StoreDetailDrawer from './StoreDetailDrawer.vue'

// 导入API服务
import storeService from '@/services/storeService'

// 响应式数据
const loading = ref(false)
const showBindModal = ref(false)
const showDetailDrawer = ref(false)
const selectedStore = ref(null)
const searchText = ref('')
const filterStatus = ref(undefined)
const filterType = ref(undefined)

// 店铺列表数据
const storeList = ref([])

// 统计卡片数据
const statsCards = reactive([
  {
    key: 'total',
    label: '店铺总数',
    value: 0,
    icon: ShopOutlined,
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'active',
    label: '活跃店铺',
    value: 0,
    icon: RiseOutlined,
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'new',
    label: '本月新增',
    value: 0,
    icon: HeartOutlined,
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

/**
 * 计算过滤后的店铺列表
 * 根据搜索文本、状态筛选、类型筛选等条件过滤店铺
 */
const filteredStores = computed(() => {
  let result = [...storeList.value]
  
  // 根据搜索文本过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(store => 
      store.店铺名称?.toLowerCase().includes(search) ||
      store.shop_id?.toString().includes(search) ||
      store.店铺ID?.toString().includes(search)
    )
  }
  
  // 根据状态过滤
  if (filterStatus.value) {
    result = result.filter(store => store.店铺状态 === filterStatus.value)
  }
  
  // 根据类型过滤
  if (filterType.value) {
    result = result.filter(store => store.店铺类型 === filterType.value)
  }
  
  return result
})

/**
 * 获取店铺状态对应的颜色
 * @param {string} status - 店铺状态
 * @returns {string} 状态颜色
 */
const getStatusColor = (status) => {
  const colorMap = {
    '正常': 'green',
    '异常': 'red',
    '待审核': 'orange',
    '停用': 'gray'
  }
  return colorMap[status] || 'blue'
}

/**
 * 格式化日期显示
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return date.toLocaleDateString('zh-CN')
}

/**
 * 加载店铺列表数据
 * 从后端API获取用户关联的店铺列表
 */
const loadStoreList = async () => {
  try {
    loading.value = true

    // 调用店铺服务API获取用户店铺列表
    const response = await storeService.getUserStores()

    if (response.status === 100) {
      // 适配后端返回的数据格式
      storeList.value = response.data || []
      updateStats()

      console.log('✅ 店铺列表加载成功:', {
        店铺数量: storeList.value.length
      })
    } else {
      console.warn('⚠️ 店铺列表加载失败:', response.message)
      message.error(response.message || '加载店铺列表失败')
    }

  } catch (error) {
    console.error('❌ 加载店铺列表失败:', error)
    message.error('加载店铺列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 加载店铺统计数据
 * 从后端API获取店铺统计信息
 */
const loadStoreStats = async () => {
  try {
    const response = await storeService.getStoreStats()

    if (response.status === 100) {
      const stats = response.data || {}

      // 更新统计卡片数据
      statsCards[0].value = stats.总数 || storeList.value.length
      statsCards[1].value = stats.活跃数量 || storeList.value.filter(store => store.店铺状态 === '正常').length
      statsCards[2].value = stats.本月新增 || 0

      console.log('✅ 店铺统计数据加载成功:', stats)
    } else {
      // 如果API失败，使用本地计算
      updateStatsLocally()
    }
  } catch (error) {
    console.error('❌ 加载店铺统计失败:', error)
    // 如果API失败，使用本地计算
    updateStatsLocally()
  }
}

/**
 * 本地计算统计数据
 * 根据店铺列表计算各项统计指标
 */
const updateStatsLocally = () => {
  const total = storeList.value.length
  const active = storeList.value.filter(store => store.店铺状态 === '正常').length
  const newThisMonth = storeList.value.filter(store => {
    const createTime = new Date(store.关联时间 || store.创建时间)
    const now = new Date()
    return createTime.getMonth() === now.getMonth() &&
           createTime.getFullYear() === now.getFullYear()
  }).length

  statsCards[0].value = total
  statsCards[1].value = active
  statsCards[2].value = newThisMonth
}

/**
 * 更新统计数据
 * 优先使用API，失败时使用本地计算
 */
const updateStats = async () => {
  await loadStoreStats()
}

/**
 * 处理搜索功能
 * @param {string} value - 搜索关键词
 */
const handleSearch = (value) => {
  searchText.value = value
}

/**
 * 处理筛选功能
 */
const handleFilter = () => {
  // 筛选逻辑已在computed中实现
}

/**
 * 刷新店铺列表
 */
const refreshStoreList = () => {
  loadStoreList()
}

/**
 * 查看店铺详情
 * @param {Object} store - 店铺数据
 */
const viewStoreDetail = async (store) => {
  try {
    // 获取店铺详细信息
    const response = await storeService.getStoreDetail(store.店铺ID)

    if (response.status === 100) {
      selectedStore.value = response.data || store
      showDetailDrawer.value = true
    } else {
      // 如果API失败，使用基础信息
      selectedStore.value = store
      showDetailDrawer.value = true
      console.warn('⚠️ 获取店铺详情失败，使用基础信息')
    }
  } catch (error) {
    console.error('❌ 获取店铺详情失败:', error)
    // 如果API失败，使用基础信息
    selectedStore.value = store
    showDetailDrawer.value = true
  }
}

/**
 * 编辑店铺信息
 * @param {Object} store - 店铺数据
 */
const editStore = async (store) => {
  await viewStoreDetail(store)
}

/**
 * 导出店铺数据
 * @param {Object} store - 店铺数据
 */
const exportStoreData = (store) => {
  // TODO: 实现店铺数据导出功能
  message.info('导出功能开发中...')
}

/**
 * 解除店铺绑定
 * @param {Object} store - 店铺数据
 */
const unbindStore = async (store) => {
  try {
    const response = await storeService.unbindStore(store.店铺ID)

    if (response.status === 100) {
      // 从列表中移除店铺
      const index = storeList.value.findIndex(s => s.店铺ID === store.店铺ID)
      if (index !== -1) {
        storeList.value.splice(index, 1)
        updateStats()
      }
      message.success('店铺解绑成功！')
    } else {
      message.error(response.message || '店铺解绑失败')
    }
  } catch (error) {
    console.error('❌ 店铺解绑失败:', error)
    message.error('店铺解绑失败，请稍后重试')
  }
}

/**
 * 处理绑定成功事件
 * @param {Object} newStore - 新绑定的店铺数据
 */
const handleBindSuccess = (newStore) => {
  storeList.value.push(newStore)
  updateStats()
  message.success('店铺绑定成功！')
}

/**
 * 处理店铺更新事件
 * @param {Object} updatedStore - 更新后的店铺数据
 */
const handleStoreUpdate = (updatedStore) => {
  const index = storeList.value.findIndex(store => store.店铺ID === updatedStore.店铺ID)
  if (index !== -1) {
    storeList.value[index] = updatedStore
    updateStats()
    message.success('店铺信息更新成功！')
  }
}

/**
 * 刷新店铺数据
 */
const refreshStoreData = async () => {
  await loadStoreList()
  message.success('数据刷新成功！')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStoreList()
})
</script>

<style scoped>
/* 模块整体样式 */
.store-info-module {
  padding: 0;
}

/* 仪表板区域样式 */
.dashboard-section {
  margin-bottom: 24px;
}

/* 统计卡片样式 */
.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-data {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1d1d1d;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 管理卡片样式 */
.management-card {
  border-radius: 12px;
  overflow: hidden;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

/* 店铺列表样式 */
.store-list {
  margin-top: 16px;
}

/* 店铺卡片样式 */
.store-card {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.store-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.store-content {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.store-avatar {
  flex-shrink: 0;
}

.store-info {
  flex: 1;
  min-width: 0;
}

.store-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1d;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-id {
  font-size: 12px;
  color: #999;
  margin: 0 0 8px 0;
}

.store-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.store-type {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.store-date {
  font-size: 12px;
  color: #999;
}

.store-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-content {
    flex-direction: column;
    text-align: center;
  }
  
  .store-avatar {
    align-self: center;
  }
  
  .filter-section {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .stat-icon {
    align-self: center;
  }
  
  .store-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 