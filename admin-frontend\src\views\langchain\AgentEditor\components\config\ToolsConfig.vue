<template>
  <div class="tools-config">
    <div class="section-header">
      <h2><ToolOutlined /> 工具调用配置</h2>
      <p>配置智能体可使用的工具和函数</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <!-- 工具开关 -->
      <a-card title="工具设置" class="config-card">
        <a-form-item label="启用工具调用">
          <a-switch
            v-model:checked="enableTools"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <span class="switch-desc">
            {{ enableTools ? '智能体可以调用外部工具' : '仅使用模型对话能力' }}
          </span>
        </a-form-item>

        <!-- 工具配置内容 -->
        <div v-if="enableTools" class="tools-settings">
          <!-- 可用工具列表 -->
          <a-form-item label="可用工具">
            <div class="tools-grid">
              <div
                v-for="tool in availableTools"
                :key="tool.工具名称"
                class="tool-card"
                :class="{ active: isToolSelected(tool.工具名称) }"
              >
                <div class="tool-header">
                  <div class="tool-icon">{{ tool.图标 || '🔧' }}</div>
                  <div class="tool-name">{{ tool.工具名称 }}</div>
                  <a-switch
                    :checked="isToolSelected(tool.工具名称)"
                    size="small"
                    @change="(checked) => handleToolSwitch(tool.工具名称, checked)"
                  />
                </div>
                <div class="tool-description">{{ tool.工具描述 }}</div>
                <div class="tool-meta">
                  <a-space size="small">
                    <a-tag size="small" color="blue">{{ tool.工具类型 }}</a-tag>
                    <a-tag size="small" :color="tool.状态 === '可用' ? 'green' : 'orange'">
                      {{ tool.状态 }}
                    </a-tag>
                  </a-space>
                </div>
              </div>
            </div>
          </a-form-item>

          <!-- 已选工具配置 -->
          <div v-if="selectedTools.length > 0" class="selected-tools">
            <div class="section-title">已选择的工具配置</div>
            <a-collapse v-model:activeKey="activeToolKeys" ghost>
              <a-collapse-panel 
                v-for="tool in selectedTools" 
                :key="tool.工具名称"
                :header="tool.工具名称"
              >
                <template #extra>
                  <a-space size="small">
                    <a-tag size="small" color="blue">{{ tool.工具类型 }}</a-tag>
                    <a-button 
                      type="text" 
                      size="small" 
                      danger
                      @click.stop="removeTool(tool.工具名称)"
                    >
                      移除
                    </a-button>
                  </a-space>
                </template>

                <div class="tool-config-content">
                  <!-- 工具参数配置 -->
                  <div v-if="tool.参数配置 && tool.参数配置.length > 0" class="tool-params">
                    <h4>参数配置</h4>
                    <a-form layout="vertical">
                      <a-form-item 
                        v-for="param in tool.参数配置" 
                        :key="param.参数名"
                        :label="param.参数名"
                      >
                        <a-input
                          v-if="param.参数类型 === 'string'"
                          v-model:value="param.默认值"
                          :placeholder="param.参数描述"
                          @change="updateToolConfig(tool.工具名称, param.参数名, param.默认值)"
                        />
                        <a-input-number
                          v-else-if="param.参数类型 === 'number'"
                          v-model:value="param.默认值"
                          :placeholder="param.参数描述"
                          @change="updateToolConfig(tool.工具名称, param.参数名, param.默认值)"
                        />
                        <a-switch
                          v-else-if="param.参数类型 === 'boolean'"
                          v-model:checked="param.默认值"
                          @change="updateToolConfig(tool.工具名称, param.参数名, param.默认值)"
                        />
                        <div class="param-desc">{{ param.参数描述 }}</div>
                      </a-form-item>
                    </a-form>
                  </div>

                  <!-- 工具权限配置 -->
                  <div class="tool-permissions">
                    <h4>权限配置</h4>
                    <a-checkbox-group v-model:value="tool.权限列表" @change="updateToolPermissions(tool.工具名称, $event)">
                      <a-checkbox value="read">读取权限</a-checkbox>
                      <a-checkbox value="write">写入权限</a-checkbox>
                      <a-checkbox value="execute">执行权限</a-checkbox>
                      <a-checkbox value="admin">管理权限</a-checkbox>
                    </a-checkbox-group>
                  </div>

                  <!-- 工具测试 -->
                  <div class="tool-test">
                    <h4>工具测试</h4>
                    <a-space>
                      <a-button size="small" @click="testTool(tool.工具名称)">
                        <PlayCircleOutlined /> 测试工具
                      </a-button>
                      <a-button size="small" @click="viewToolDocs(tool.工具名称)">
                        <FileTextOutlined /> 查看文档
                      </a-button>
                    </a-space>
                  </div>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>

          <!-- 工具调用策略 -->
          <a-form-item label="工具调用策略">
            <a-radio-group v-model:value="localForm.工具调用策略" @change="handleFormChange">
              <a-radio value="auto">自动选择</a-radio>
              <a-radio value="manual">手动确认</a-radio>
              <a-radio value="parallel">并行调用</a-radio>
            </a-radio-group>
            <div class="strategy-desc">
              {{ getToolStrategyDesc(localForm.工具调用策略) }}
            </div>
          </a-form-item>

          <!-- 工具调用限制 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大并发调用数">
                <a-input-number
                  v-model:value="localForm.最大并发工具数"
                  :min="1"
                  :max="10"
                  @change="handleFormChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="工具调用超时(秒)">
                <a-input-number
                  v-model:value="localForm.工具调用超时"
                  :min="5"
                  :max="300"
                  @change="handleFormChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ToolOutlined, PlayCircleOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  availableTools: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Object,
    default: () => ({})
  },
  autoSave: {
    type: Function,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'test-tool'])

// 本地状态
const activeToolKeys = ref([])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工具开关
const enableTools = computed({
  get: () => Boolean(localForm.value.启用工具调用),
  set: async (value) => {
    // 创建新的表单数据副本
    const newFormData = { ...localForm.value }
    newFormData.启用工具调用 = value

    if (!value) {
      // 关闭工具：清空工具列表
      newFormData.工具列表 = []
      // 清空展开的工具配置
      activeToolKeys.value = []
    } else {
      // 开启工具：确保工具列表存在
      if (!newFormData.工具列表) {
        newFormData.工具列表 = []
      }
    }

    // 通过computed的setter更新数据
    localForm.value = newFormData

    // 自动保存
    if (props.autoSave) {
      try {
        await props.autoSave()
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    }
  }
})

// 已选择的工具
const selectedTools = computed(() => {
  if (!localForm.value.工具列表) return []
  return props.availableTools.filter(tool => 
    localForm.value.工具列表.includes(tool.工具名称)
  )
})

// 检查工具是否被选中
const isToolSelected = (toolName) => {
  return localForm.value.工具列表?.includes(toolName) || false
}

// 获取工具策略描述
const getToolStrategyDesc = (strategy) => {
  const descriptions = {
    'auto': '智能体自动决定何时调用工具',
    'manual': '每次工具调用都需要用户确认',
    'parallel': '允许同时调用多个工具'
  }
  return descriptions[strategy] || ''
}



// 处理工具开关事件（专门用于开关组件）
const handleToolSwitch = async (toolName, checked) => {
  // 创建新的表单数据副本
  const newFormData = { ...localForm.value }

  if (!newFormData.工具列表) {
    newFormData.工具列表 = []
  } else {
    // 创建工具列表的副本
    newFormData.工具列表 = [...newFormData.工具列表]
  }

  const isSelected = newFormData.工具列表.includes(toolName)

  if (checked && !isSelected) {
    // 开启工具
    newFormData.工具列表.push(toolName)
    // 展开新添加的工具配置
    if (!activeToolKeys.value.includes(toolName)) {
      activeToolKeys.value.push(toolName)
    }
  } else if (!checked && isSelected) {
    // 关闭工具
    const index = newFormData.工具列表.indexOf(toolName)
    newFormData.工具列表.splice(index, 1)
    // 收起移除的工具配置
    const keyIndex = activeToolKeys.value.indexOf(toolName)
    if (keyIndex > -1) {
      activeToolKeys.value.splice(keyIndex, 1)
    }
  }

  // 通过computed的setter更新数据
  localForm.value = newFormData

  // 自动保存
  if (props.autoSave) {
    try {
      await props.autoSave()
    } catch (error) {
      console.error('自动保存失败:', error)
    }
  }
}



// 移除工具
const removeTool = (toolName) => {
  handleToolSwitch(toolName, false)
}

// 更新工具配置
const updateToolConfig = (toolName, paramName, value) => {
  // 这里可以实现工具参数的保存逻辑
  console.log('更新工具配置:', toolName, paramName, value)
  handleFormChange()
}

// 更新工具权限
const updateToolPermissions = (toolName, permissions) => {
  // 这里可以实现工具权限的保存逻辑
  console.log('更新工具权限:', toolName, permissions)
  handleFormChange()
}

// 测试工具
const testTool = (toolName) => {
  emit('test-tool', toolName)
}

// 查看工具文档
const viewToolDocs = (toolName) => {
  // 这里可以打开工具文档
  message.info(`查看 ${toolName} 工具文档`)
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}


</script>

<style scoped>
.tools-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.config-card {
  margin-bottom: 16px;
}

.switch-desc {
  margin-left: 12px;
  color: #8c8c8c;
  font-size: 12px;
}

.tools-settings {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.tool-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.tool-card.active {
  border-color: #1890ff;
  background: #f6ffed;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.tool-icon {
  font-size: 20px;
}

.tool-name {
  flex: 1;
  font-weight: 500;
  color: #262626;
}

.tool-description {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.tool-meta {
  margin-top: 8px;
}

.selected-tools {
  margin-top: 24px;
}

.section-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 16px;
  font-size: 14px;
}

.tool-config-content {
  padding: 8px 0;
}

.tool-config-content h4 {
  margin: 16px 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.tool-config-content h4:first-child {
  margin-top: 0;
}

.param-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.strategy-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}
</style>
