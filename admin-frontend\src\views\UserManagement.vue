<template>
  <div class="user-management-container">
    <ErrorBoundary>
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>
              <UserOutlined />
              用户管理系统
            </h1>
            <p class="header-subtitle">管理和维护系统用户信息</p>
          </div>
          <div class="header-actions">
            <a-space size="large">
              <div class="stats-summary">
                <a-statistic 
          title="总用户数" 
          :value="分页配置.total" 
          :value-style="{ color: '#1890ff', fontSize: '20px', fontWeight: '600' }"
        />
              </div>
              <a-button 
                type="primary" 
                size="large"
                @click="显示添加模态框" 
                :icon="h(PlusOutlined)"
                class="add-user-btn"
              >
                新增用户
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
      
      <!-- 搜索筛选区域 -->
      <a-card class="search-section" title="用户搜索">
        
        <!-- 优化的搜索区域 - 简洁高效 -->
        <div class="search-container">
          <a-row :gutter="16" align="middle">
            <a-col :xs="24" :sm="16" :md="12" :lg="8">
              <a-input-search
                v-model:value="搜索关键词"
                placeholder="搜索用户id、昵称、邮箱或手机号"
                enter-button="搜索"
                size="large"
                allow-clear
                @search="处理搜索"
                @press-enter="处理搜索"
                :loading="loading"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input-search>
            </a-col>
            <a-col :xs="24" :sm="8" :md="6" :lg="4">
              <a-select
                v-model:value="搜索表单状态.状态"
                placeholder="用户状态"
                size="large"
                allow-clear
                @change="处理搜索"
                style="width: 100%"
              >
                <a-select-option value="active">正常</a-select-option>
                <a-select-option value="inactive">未激活</a-select-option>
                <a-select-option value="banned">已禁用</a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="24" :sm="24" :md="6" :lg="4">
              <a-space>
                <a-button
                  @click="重置搜索表单"
                  :icon="h(ReloadOutlined)"
                  size="large"
                >
                  重置
                </a-button>
                <a-button
                  @click="导出用户数据"
                  :icon="h(ExportOutlined)"
                  size="large"
                >
                  导出
                </a-button>
                <a-tooltip title="智能搜索：短数字优先匹配用户id，长数字优先匹配手机号，文字搜索昵称和邮箱">
                  <InfoCircleOutlined style="color: #999; cursor: help;" />
                </a-tooltip>
              </a-space>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- 用户数据表格 -->
      <a-card class="table-section" title="用户列表">
        <template #extra>
          <a-space>
            <a-tooltip title="刷新数据">
              <a-button 
                type="text" 
                :icon="h(ReloadOutlined)" 
                @click="获取用户列表"
                :loading="loading"
              />
            </a-tooltip>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="处理批量操作菜单">
                  <a-menu-item key="export-excel">
                    <ExportOutlined /> 导出Excel
                  </a-menu-item>
                  <a-menu-item key="export-csv">
                    <FileTextOutlined /> 导出CSV
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="batch-enable" :disabled="选中行键.length === 0">
                    <PoweroffOutlined /> 批量启用
                  </a-menu-item>
                  <a-menu-item key="batch-disable" :disabled="选中行键.length === 0">
                    <PoweroffOutlined /> 批量禁用
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="batch-delete" danger :disabled="选中行键.length === 0">
                    <DeleteOutlined /> 批量删除 ({{ 选中行键.length }})
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>
                更多操作 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
        
        <!-- 批量操作栏 -->
        <div v-if="选中行键.length > 0" class="batch-actions mb-4">
          <a-alert
            message="批量操作"
            :description="`已选择 ${选中行键.length} 个用户`"
            type="info"
            show-icon
            action
          >
            <template #action>
              <a-space>
                <a-button size="small" type="primary" @click="batchModalVisible = true">
                  批量操作
                </a-button>
                <a-button size="small" @click="选中行键 = []">取消选择</a-button>
              </a-space>
            </template>
          </a-alert>
        </div>

        <a-table
          :columns="表格列配置"
          :data-source="数据源"
          :loading="loading"
          row-key="id"
          :pagination="分页配置"
          @change="处理表格变化"
          :row-selection="行选择配置"
          :scroll="{ x: 1200 }"
          class="user-table"
        >
          <template #bodyCell="{ column, record }">
            <!-- {{ AURA-X: Modify - 直接使用中文字段，移除昵称兼容. Approval: 寸止(ID:1721062800). }} -->
            <template v-if="column.key === 'avatar'">
              <a-avatar
                :size="40"
                :src="record.avatar"
                :style="{ backgroundColor: 获取头像颜色(record.昵称) }"
              >
                {{ record.昵称?.charAt(0)?.toUpperCase() }}
              </a-avatar>
            </template>

            <template v-else-if="column.key === '昵称'">
              <div class="user-info">
                <div class="user-name">{{ record.昵称 }}</div>
                <div class="user-id">ID: {{ record.id }}</div>
              </div>
            </template>
            
            <!-- {{ AURA-X: Modify - 统一使用中文字段名显示联系方式. Approval: 寸止(ID:1721062800). }} -->
            <template v-else-if="column.key === 'contact'">
              <div class="contact-info">
                <div class="email">
                  <MailOutlined /> {{ record.邮箱 }}
                </div>
                <div class="phone" v-if="record.手机号">
                  <PhoneOutlined /> {{ record.手机号 }}
                </div>
              </div>
            </template>
            
            <template v-else-if="column.key === 'login_status'">
              <a-tag
                :color="获取登录状态颜色(record.最后登录时间)"
                class="login-status-tag"
              >
                {{ 获取登录状态文本(record.最后登录时间) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'status'">
              <a-tag 
                :color="获取状态颜色(record.status)"
                class="status-tag"
              >
                {{ 获取状态文本(record.status) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'created_at'">
              <div class="time-info">
                <div>{{ record.格式化注册时间 }}</div>
                <div class="time-ago">{{ 相对时间(record.created_at) }}</div>
              </div>
            </template>

            <template v-else-if="column.key === 'last_login_time'">
              <div class="login-info" v-if="record.最后登录时间">
                <div class="login-time">{{ 格式化日期(record.最后登录时间) }}</div>
                <div class="time-ago">{{ 相对时间(record.最后登录时间) }}</div>
              </div>
              <div v-else class="no-login">从未登录</div>
            </template>

            <template v-else-if="column.key === 'login_ip'">
              <div class="ip-info" v-if="record.最后登录IP">
                <div class="ip-address">{{ record.最后登录IP }}</div>
                <div class="ip-location" v-if="record.最后登录归属地">{{ record.最后登录归属地 }}</div>
              </div>
              <div v-else class="no-ip">-</div>
            </template>

            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-tooltip title="查看详情">
                  <a-button 
                    type="text" 
                    :icon="h(EyeOutlined)"
                    @click="查看用户详情(record)"
                    class="action-btn"
                  />
                </a-tooltip>
                <a-tooltip title="编辑用户">
                  <a-button 
                    type="text" 
                    :icon="h(EditOutlined)"
                    @click="编辑用户(record)"
                    class="action-btn"
                  />
                </a-tooltip>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="(e) => 处理用户操作菜单(e, record)">
                      <a-menu-item key="reset-password">
                        <KeyOutlined /> 重置密码
                      </a-menu-item>
                      <a-menu-item key="toggle-status">
                        <PoweroffOutlined /> {{ record.status === 'active' ? '禁用' : '启用' }}用户
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" danger>
                        <DeleteOutlined /> 删除用户
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="text" :icon="h(MoreOutlined)" class="action-btn" />
                </a-dropdown>
              </a-space>
            </template>
            
            <template v-else-if="column.key === 'is_admin'">
              <a-tag :color="record.is_admin ? 'blue' : 'default'">
                {{ record.权限标识 }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'level'">
              <a-tag color="purple">
                {{ record.显示等级 }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 添加用户模态框 -->
      <a-modal
        v-model:open="addModalVisible"
        title="新增用户"
        @ok="处理添加模态框确认"
        @cancel="处理添加模态框取消"
        :confirm-loading="addModalConfirmLoading"
        destroyOnClose
        width="600px"
      >
        <a-form
          ref="addModalFormRef"
          :model="addModalFormState"
          layout="vertical"
          name="userAddModalForm"
        >
          <a-form-item
            name="用户名"
            label="用户名"
            :rules="[{ required: true, message: '请输入用户名!' }]"
          >
            <a-input v-model:value="addModalFormState.昵称" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item
            name="邮箱"
            label="邮箱"
            :rules="[{ required: true, message: '请输入邮箱地址!' }, { type: 'email', message: '请输入有效的邮箱地址!' }]"
          >
            <a-input v-model:value="addModalFormState.email" placeholder="请输入邮箱地址" />
          </a-form-item>
          <a-form-item
            name="密码"
            label="密码"
            :rules="[{ required: true, message: '请输入密码!' }]"
          >
            <a-input-password v-model:value="addModalFormState.密码" placeholder="请输入密码 (新增时必填)" />
          </a-form-item>
          <a-form-item name="手机号" label="手机号">
            <a-input v-model:value="addModalFormState.phone" placeholder="请输入手机号" />
          </a-form-item>
        </a-form>
      </a-modal>
      
      <!-- 编辑用户模态框 -->
      <a-modal
        v-model:open="editModalVisible"
        title="编辑用户"
        @ok="处理编辑模态框确认"
        @cancel="处理编辑模态框取消"
        :confirm-loading="editModalConfirmLoading"
        destroyOnClose
        width="600px"
      >
        <a-form
          ref="editModalFormRef"
          :model="editModalFormState"
          layout="vertical"
          name="userEditModalForm"
        >
          <a-form-item
            name="用户名"
            label="用户名"
            :rules="[{ required: true, message: '请输入用户名!' }]"
          >
            <a-input v-model:value="editModalFormState.昵称" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item
            name="邮箱"
            label="邮箱"
            :rules="[{ required: true, message: '请输入邮箱地址!' }, { type: 'email', message: '请输入有效的邮箱地址!' }]"
          >
            <a-input v-model:value="editModalFormState.email" placeholder="请输入邮箱地址" />
          </a-form-item>
          <a-form-item name="手机号" label="手机号">
            <a-input v-model:value="editModalFormState.phone" placeholder="请输入手机号" />
          </a-form-item>
           <a-form-item
            name="is_admin"
            label="管理员权限"
          >
            <a-switch v-model:checked="editModalFormState.is_admin" />
          </a-form-item>
          <!-- 密码字段在编辑时不直接显示，通常通过特定操作如"重置密码"来修改 -->
        </a-form>
      </a-modal>
      
      <LoadingSpinner v-if="loading" type="card" tip="正在加载用户数据..." />

      <!-- 用户详情抽屉 -->
      <a-drawer
        v-model:open="用户详情抽屉可见"
        title="用户详情"
        width="1000px"
        @close="关闭用户详情"
      >
        <div class="user-details" v-if="当前查看用户">
          <a-tabs v-model:activeKey="详情活跃标签" @change="处理详情标签切换">
            <!-- 基本信息标签页 -->
            <a-tab-pane key="basic" tab="基本信息" :tab-icon="h(UserOutlined)">
              <a-spin :spinning="详情加载中">
                <!-- 快速操作按钮 -->
                <a-card size="small" class="mb-4">
                  <template #title>
                    <span>快速操作</span>
                  </template>
                  <a-space>
                    <a-button type="primary" @click="showStatusModal">状态管理</a-button>
                    <a-button @click="exportUserDetail">导出详情</a-button>
                    <a-button @click="refreshAllData">刷新数据</a-button>
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="handleMenuClick">
                          <a-menu-item key="resetPassword">重置密码</a-menu-item>
                          <a-menu-item key="sendNotification">发送通知</a-menu-item>
                          <a-menu-item key="viewSecurity">安全审计</a-menu-item>
                        </a-menu>
                      </template>
                      <a-button>
                        更多操作 <DownOutlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </a-card>

                <a-row :gutter="[16, 16]">
                  <a-col :span="24">
                    <a-card title="用户基本信息" size="small">
                      <a-descriptions :column="2" bordered size="small">
                        <a-descriptions-item label="用户id">{{ 当前查看用户.id }}</a-descriptions-item>
                        <a-descriptions-item label="昵称">{{ 当前查看用户.昵称 || '未设置' }}</a-descriptions-item>
                        <a-descriptions-item label="邮箱">{{ 当前查看用户.email || '未设置' }}</a-descriptions-item>
                        <a-descriptions-item label="手机号">{{ 当前查看用户.phone || '未设置' }}</a-descriptions-item>
                        <a-descriptions-item label="用户等级">
                          <a-tag color="blue">等级 {{ 用户详细统计?.基本信息?.等级 || 1 }}</a-tag>
                        </a-descriptions-item>
                        <a-descriptions-item label="管理员权限">
                          <a-tag :color="当前查看用户.is_admin ? 'red' : 'default'">
                            {{ 当前查看用户.is_admin ? '是' : '否' }}
                          </a-tag>
                        </a-descriptions-item>
                        <a-descriptions-item label="账户状态">
                          <a-tag :color="当前查看用户.status === 'active' ? 'green' : 'red'">
                            {{ 当前查看用户.status === 'active' ? '正常' : '异常' }}
                          </a-tag>
                        </a-descriptions-item>
                        <a-descriptions-item label="注册时间">{{ 当前查看用户.created_at || '未知' }}</a-descriptions-item>
                        <a-descriptions-item label="最后登录" :span="2">{{ 当前查看用户.最后登录时间 || '从未登录' }}</a-descriptions-item>
                      </a-descriptions>
                    </a-card>
                  </a-col>
                  
                  <!-- 基本信息统计卡片 -->
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic title="关联店铺" :value="用户详细统计?.店铺信息?.数量 || 0" suffix="个" />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic title="登录次数" :value="用户详细统计?.登录信息?.统计?.总登录次数 || 0" suffix="次" />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic title="接口调用" :value="用户详细统计?.接口调用?.统计?.总调用次数 || 0" suffix="次" />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic title="邀约成功" :value="userInvitationStats?.基础统计?.成功邀约次数 || 0" suffix="次" />
                    </a-card>
                  </a-col>
                  

                  
                  <!-- 关联店铺列表 -->
                  <a-col :span="24" v-if="用户详细统计?.店铺信息?.列表?.length > 0">
                    <a-card title="关联店铺" size="small">
                      <a-list 
                        :dataSource="用户详细统计.店铺信息.列表" 
                        size="small"
                        :split="false"
                      >
                        <template #renderItem="{ item }">
                          <a-list-item>
                            <a-list-item-meta>
                              <template #avatar>
                                <a-avatar :src="item.avatar" :size="32">
                                  {{ item.shop_name?.[0] || 'S' }}
                                </a-avatar>
                              </template>
                              <template #title>{{ item.shop_name || '未命名店铺' }}</template>
                              <template #description>店铺ID: {{ item.shop_id || '未设置' }}</template>
                            </a-list-item-meta>
                          </a-list-item>
                        </template>
                      </a-list>
                    </a-card>
                  </a-col>
                  
                  <!-- 团队信息 -->
                  <a-col :span="24" v-if="用户详细统计?.团队信息?.length > 0">
                    <a-card title="团队信息" size="small">
                      <a-table 
                        :dataSource="用户详细统计.团队信息" 
                        :columns="[
                          { title: '团队名称', dataIndex: '团队名称', key: '团队名称' },
                          { title: '职位', dataIndex: '职位', key: '职位' },
                          { title: '状态', dataIndex: '状态', key: '状态' },
                          { title: '加入时间', dataIndex: '加入时间', key: '加入时间' }
                        ]"
                        :pagination="false"
                        size="small"
                      />
                    </a-card>
                  </a-col>
                </a-row>
              </a-spin>
            </a-tab-pane>
            
            <!-- 登录历史标签页 -->
            <a-tab-pane key="login" tab="登录历史" :tab-icon="h(FileTextOutlined)">
              <a-table
                :dataSource="登录历史列表"
                :columns="[
                  { title: '登录时间', dataIndex: '格式化时间', key: '格式化时间', width: 180 },
                  { title: 'IP地址', dataIndex: 'IP地址', key: 'IP地址', width: 150 },
                  { title: 'IP归属地', dataIndex: 'IP归属地', key: 'IP归属地', width: 200 }
                ]"
                :pagination="{
                  current: 登录历史分页.current,
                  pageSize: 登录历史分页.pageSize,
                  total: 登录历史分页.total,
                  showSizeChanger: false,
                  onChange: 处理登录历史分页变化
                }"
                size="small"
              >
                <template #emptyText>
                  <a-empty description="暂无登录记录" />
                </template>
              </a-table>
            </a-tab-pane>
            
            <!-- 接口调用历史标签页 -->
            <a-tab-pane key="api" tab="接口调用历史" :tab-icon="h(MailOutlined)">
              <a-table 
                :dataSource="接口调用历史列表"
                :columns="[
                  { title: '调用时间', dataIndex: '格式化时间', key: '格式化时间', width: 150 },
                  { title: '请求路径', dataIndex: '请求路径', key: '请求路径', ellipsis: true },
                  { title: '方法', dataIndex: '请求方法', key: '请求方法', width: 80 },
                  { title: '状态码', dataIndex: '状态码', key: '状态码', width: 80 },
                  { title: '耗时(ms)', dataIndex: '耗时', key: '耗时', width: 100 },
                  { title: 'IP地址', dataIndex: 'IP地址', key: 'IP地址', width: 120 }
                ]"
                :pagination="{
                  current: 接口调用历史分页.current,
                  pageSize: 接口调用历史分页.pageSize,
                  total: 接口调用历史分页.total,
                  showSizeChanger: false,
                  onChange: 处理接口调用历史分页变化
                }"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === '状态码'">
                    <a-tag :color="record.状态码 < 300 ? 'green' : record.状态码 < 500 ? 'orange' : 'red'">
                      {{ record.状态码 }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === '耗时'">
                    {{ Math.round(record.耗时 * 1000) / 1000 }}
                  </template>
                </template>
                <template #emptyText>
                  <a-empty description="暂无调用记录" />
                </template>
              </a-table>
            </a-tab-pane>

            <!-- 邀约分析标签页 -->
            <a-tab-pane key="invitation" tab="邀约分析">
              <div v-if="userInvitationStats">
                <!-- 邀约统计概览 -->
                <a-row :gutter="16" class="mb-4">
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="总邀约次数" 
                        :value="userInvitationStats.基础统计?.总邀约次数 || 0" 
                        suffix="次" 
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="成功邀约" 
                        :value="userInvitationStats.基础统计?.成功邀约次数 || 0" 
                        suffix="次" 
                        :value-style="{ color: '#3f8600' }"
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="近30天邀约" 
                        :value="userInvitationStats.基础统计?.近30天邀约 || 0" 
                        suffix="次" 
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="成功率" 
                        :value="calculateSuccessRate(userInvitationStats.基础统计)" 
                        suffix="%" 
                        :precision="1"
                        :value-style="{ color: '#3f8600' }"
                      />
                    </a-card>
                  </a-col>
                </a-row>

                <!-- 邀约状态分布 -->
                <a-card title="邀约状态分布" size="small" class="mb-4">
                  <a-table 
                    :columns="invitationStatusColumns" 
                    :data-source="userInvitationStats.状态分布 || []" 
                    :pagination="false"
                    size="small"
                  />
                </a-card>

                <!-- 邀约趋势分析 -->
                <a-card title="近30天邀约趋势" size="small">
                  <div style="height: 300px; overflow-y: auto;">
                    <a-table 
                      :columns="invitationTrendColumns" 
                      :data-source="userInvitationStats.趋势分析 || []" 
                      :pagination="false"
                      size="small"
                    />
                  </div>
                </a-card>
              </div>
            </a-tab-pane>

            <!-- 权限详情标签页 -->
            <a-tab-pane key="permissions" tab="权限详情">
              <div v-if="userPermissions && userPermissions.会员权限信息">
                <!-- 会员权限信息 -->
                <a-card title="会员权限信息" size="small" class="mb-4">
                  <a-table
                    :columns="memberPermissionColumns"
                    :data-source="userPermissions.会员权限信息 || []"
                    :pagination="false"
                    size="small"
                    :expandable="{
                      expandedRowRender: (record) => {
                        return h('div', { class: 'p-4' }, [
                          h('h4', { class: 'mb-2' }, '权限详情'),
                          h('a-table', {
                            columns: permissionDetailColumns,
                            dataSource: record.权限详情 || [],
                            pagination: false,
                            size: 'small'
                          })
                        ]);
                      },
                      rowExpandable: (record) => record.权限详情 && record.权限详情.length > 0
                    }"
                  />
                </a-card>


              </div>
              <a-empty v-else description="暂无权限信息" />
            </a-tab-pane>

            <!-- 安全审计标签页 -->
            <a-tab-pane key="security" tab="安全审计">
              <div v-if="userSecurityAudit">
                <!-- 安全概览 -->
                <a-row :gutter="16" class="mb-4">
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="不同IP数量" 
                        :value="userSecurityAudit.登录安全?.不同IP数量 || 0" 
                        suffix="个" 
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="今日登录" 
                        :value="userSecurityAudit.登录安全?.今日登录 || 0" 
                        suffix="次" 
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="近1小时API调用" 
                        :value="userSecurityAudit.接口安全?.近1小时调用次数 || 0" 
                        suffix="次" 
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="6">
                    <a-card size="small">
                      <a-statistic 
                        title="错误请求" 
                        :value="userSecurityAudit.接口安全?.客户端错误次数 || 0" 
                        suffix="次" 
                        :value-style="{ color: '#cf1322' }"
                      />
                    </a-card>
                  </a-col>
                </a-row>

                <!-- 常用IP地址 -->
                <a-card title="常用IP地址" size="small" class="mb-4">
                  <a-table 
                    :columns="ipColumns" 
                    :data-source="userSecurityAudit.常用IP || []" 
                    :pagination="false"
                    size="small"
                  />
                </a-card>

                <!-- 近期登录记录 -->
                <a-card title="近期登录记录" size="small">
                  <a-table 
                    :columns="recentLoginColumns" 
                    :data-source="userSecurityAudit.近期登录 || []" 
                    :pagination="false"
                    size="small"
                  />
                </a-card>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-drawer>

      <!-- 状态管理对话框 -->
      <a-modal
        v-model:open="statusModalVisible"
        title="用户状态管理"
        @ok="updateUserStatus"
        :confirm-loading="statusLoading"
      >
        <a-form :model="statusForm" layout="vertical">
          <a-form-item label="当前状态">
            <a-tag>{{ 当前查看用户?.status || '未知' }}</a-tag>
          </a-form-item>
          <a-form-item label="新状态" required>
            <a-select v-model:value="statusForm.新状态" placeholder="请选择新状态">
              <a-select-option value="正常">正常</a-select-option>
              <a-select-option value="禁用">禁用</a-select-option>
              <a-select-option value="冻结">冻结</a-select-option>
              <a-select-option value="待审核">待审核</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="备注">
            <a-textarea v-model:value="statusForm.备注" placeholder="请输入状态变更备注" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 批量操作对话框 -->
      <a-modal
        v-model:open="batchModalVisible"
        title="批量操作"
        @ok="executeBatchOperation"
        :confirm-loading="batchLoading"
        width="600"
      >
        <a-form :model="batchForm" layout="vertical">
          <a-form-item label="选中用户">
            <a-tag v-for="id in 选中行键" :key="id">用户id: {{ id }}</a-tag>
            <div class="text-gray-500 mt-2">已选择 {{ 选中行键.length }} 个用户</div>
          </a-form-item>
          <a-form-item label="操作类型" required>
            <a-select v-model:value="batchForm.操作类型" placeholder="请选择操作类型">
              <a-select-option value="状态变更">状态变更</a-select-option>
              <a-select-option value="重置密码">重置密码</a-select-option>
              <a-select-option value="发送通知">发送通知</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="batchForm.操作类型 === '状态变更'" label="目标状态">
            <a-select v-model:value="batchForm.参数.状态" placeholder="请选择状态">
              <a-select-option value="正常">正常</a-select-option>
              <a-select-option value="禁用">禁用</a-select-option>
              <a-select-option value="冻结">冻结</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="备注">
            <a-textarea v-model:value="batchForm.参数.备注" placeholder="请输入操作备注" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>
    </ErrorBoundary>
  </div>
</template>

<script setup>
import {
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  ExportOutlined,
  EyeOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  KeyOutlined,
  MailOutlined,
  MoreOutlined,
  PhoneOutlined,
  PlusOutlined,
  PoweroffOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import { h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import ErrorBoundary from '../components/common/ErrorBoundary.vue';
import LoadingSpinner from '../components/common/LoadingSpinner.vue';
import { useSuperAdminRequest } from '../composables/useApiRequest'; // 使用统一的响应处理
import superAdminService from '../services/superAdminService'; // 使用统一的API服务

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const router = useRouter();

// 搜索关键词
const 搜索关键词 = ref('')

// 表格列配置 - 优化版本，支持更多功能
const 表格列配置 = [
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    width: 80,
    align: 'center'
  },
  {
    title: '用户信息',
    dataIndex: '昵称',
    key: '昵称',
    width: 200,
    ellipsis: true,
    sorter: true
  },
  {
    title: '联系方式',
    key: 'contact',
    width: 220,
    ellipsis: true
  },
  {
    title: '管理员',
    dataIndex: 'is_admin',
    key: 'is_admin',
    width: 100,
    align: 'center'
  },
  {
    title: '登录状态',
    key: 'login_status',
    width: 120,
    align: 'center'
  },
  {
    title: '用户状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '注册时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: true
  },
  {
    title: '最后登录',
    dataIndex: '最后登录时间',
    key: 'last_login_time',
    width: 180,
    sorter: true
  },
  {
    title: '最后登录IP地址',
    key: 'login_ip',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 搜索表单状态 - 简化版本
const 搜索表单状态 = reactive({
  状态: undefined
});

// 使用统一的API响应处理
const { loading, 执行API请求 } = useSuperAdminRequest();

// 统一的API调用方法 - 简化版，直接返回数据
const 创建用户 = async (用户数据) => {
  return await 执行API请求(async () => {
    const response = await superAdminService.createUser(用户数据);
    if (superAdminService.isSuccess(response)) {
      return superAdminService.getData(response);
    } else {
      throw new Error(superAdminService.getMessage(response));
    }
  }, {
    onSuccess: () => message.success('创建用户成功')
  });
}

const 更新用户 = async (用户数据) => {
  return await 执行API请求(async () => {
    const response = await superAdminService.updateUser(用户数据);
    if (superAdminService.isSuccess(response)) {
      return superAdminService.getData(response);
    } else {
      throw new Error(superAdminService.getMessage(response));
    }
  }, {
    onSuccess: () => message.success('更新用户成功')
  });
}

const 删除用户 = async (用户id) => {
  return await 执行API请求(async () => {
    const response = await superAdminService.deleteUser(用户id);
    if (superAdminService.isSuccess(response)) {
      return superAdminService.getData(response);
    } else {
      throw new Error(superAdminService.getMessage(response));
    }
  }, {
    onSuccess: () => message.success('删除用户成功')
  });
}

const 数据源 = ref([]);
const 分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 / 总共 ${total} 条`,
});

// 行选择配置
const 选中行键 = ref([])
const 行选择配置 = {
  selectedRowKeys: 选中行键,
  onChange: (selectedRowKeys) => {
    选中行键.value = selectedRowKeys
  },
  getCheckboxProps: (record) => ({
    disabled: record.id === 1, // 禁用管理员账户选择
    name: record.昵称 || record.用户名 || `用户${record.id}`
  })
}

// 判断登录状态
const 判断登录状态 = (最后登录时间) => {
  if (!最后登录时间) return 'never_login'
  
  const now = dayjs()
  const lastLogin = dayjs(最后登录时间)
  const diffHours = now.diff(lastLogin, 'hour')
  
  if (diffHours <= 1) return 'online'
  if (diffHours <= 24) return 'recently_active'
  if (diffHours <= 168) return 'inactive' // 7天内
  return 'long_inactive'
}

// 获取登录状态颜色
const 获取登录状态颜色 = (最后登录时间) => {
  if (!最后登录时间) return 'default';
  
  const 现在 = dayjs();
  const 登录时间 = dayjs(最后登录时间);
  const 天数差 = 现在.diff(登录时间, 'day');
  const 小时差 = 现在.diff(登录时间, 'hour');
  
  if (小时差 <= 1) return 'green';      // 在线
  if (小时差 <= 24) return 'blue';      // 最近活跃
  if (天数差 <= 7) return 'cyan';       // 一周内活跃
  if (天数差 <= 30) return 'orange';    // 不活跃
  return 'red';                          // 长期未登录
}

// 获取登录状态文本
const 获取登录状态文本 = (最后登录时间) => {
  if (!最后登录时间) return '从未登录';
  
  const 现在 = dayjs();
  const 登录时间 = dayjs(最后登录时间);
  const 天数差 = 现在.diff(登录时间, 'day');
  const 小时差 = 现在.diff(登录时间, 'hour');
  
  if (小时差 <= 1) return '在线';
  if (小时差 <= 24) return '最近活跃';
  if (天数差 <= 7) return '一周内活跃';
  if (天数差 <= 30) return '不活跃';
  return '长期未登录';
}

// 获取用户状态颜色
const 获取状态颜色 = (status) => {
  const 颜色映射 = {
    'active': 'green',
    'inactive': 'orange',
    'banned': 'red'
  }
  return 颜色映射[status] || 'default'
}

// 获取用户状态文本
const 获取状态文本 = (status) => {
  const 文本映射 = {
    'active': '正常',
    'inactive': '未激活',
    'banned': '已禁用'
  }
  return 文本映射[status] || '未知'
}

// 获取头像颜色
const 获取头像颜色 = (昵称) => {
  const 颜色列表 = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068', '#108ee9']
  const 索引 = (昵称 || '').charCodeAt(0) % 颜色列表.length
  return 颜色列表[索引]
}



// 格式化日期
const 格式化日期 = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 相对时间
const 相对时间 = (dateString) => {
  if (!dateString) return ''
  return dayjs(dateString).fromNow()
}

// {{ AURA-X: Modify - 统一使用中文字段名构建搜索参数. Approval: 寸止(ID:1721062800). }}
// 获取用户列表数据 - 优化版本，支持完整的用户管理功能
const 获取用户列表数据 = async (params = {}) => {
  // 构建请求参数 - 使用统一搜索关键词
  const 请求参数 = {
    页码: params.page || 分页配置.current,
    每页数量: params.pageSize || 分页配置.pageSize,
    搜索关键词: 搜索关键词.value.trim() || '',
    状态筛选: 搜索表单状态.状态 || null,
    时间范围: null,
    排序字段: params.sortField || null,
    排序顺序: params.sortOrder || null
  };

  console.log('🌐 API请求: 获取用户列表', 请求参数);

  try {
    // 调用API获取数据
    const response = await superAdminService.获取用户列表数据(请求参数);
    console.log('📥 API原始响应:', response);
    
    // 检查响应是否成功
    if (superAdminService.isSuccess(response)) {
      const 响应数据 = superAdminService.getData(response);
      console.log('✅ 解析后的数据:', 响应数据);
      
      // {{ AURA-X: Modify - 简洁高效，直接使用中文字段，移除冗余处理. Approval: 寸止(ID:1721062800). }}
      // 处理用户列表数据
      const 用户列表 = 响应数据.列表?.map((用户, index) => {
        // 添加调试日志
        if (index < 3) {
          console.log(`🔍 用户${index + 1} 最后登录时间调试:`, {
            用户id: 用户.id,
            昵称: 用户.昵称,
            原始最后登录时间: 用户.最后登录时间,
            时间类型: typeof 用户.最后登录时间,
            是否有效: !!用户.最后登录时间
          });
        }

        return {
          ...用户,
          // 添加登录状态判断
          login_status: 判断登录状态(用户.最后登录时间),
          // 确保必要字段存在
          avatar: 用户.avatar || null,
          status: 用户.status || 'active',
          // 添加用户等级和权限显示
          显示等级: `等级 ${用户.level || 1}`,
          权限标识: 用户.is_admin ? '管理员' : '普通用户',
          // 格式化时间显示
          格式化注册时间: 用户.created_at ? dayjs(用户.created_at).format('YYYY-MM-DD HH:mm') : '未知',
          格式化最后登录时间: 用户.最后登录时间 ? dayjs(用户.最后登录时间).format('YYYY-MM-DD HH:mm') : '从未登录',
          // 添加状态颜色
          状态颜色: 用户.status === 'active' ? 'green' : 'red',
          登录状态颜色: 判断登录状态颜色(用户.最后登录时间)
        };
      }) || [];
      
      // 更新数据源和分页配置
      数据源.value = 用户列表;
      分页配置.total = 响应数据.总数 || 0;
      分页配置.current = 响应数据.页码 || 1;
      分页配置.pageSize = 响应数据.每页数量 || 10;
      
      console.log(`✅ 用户列表更新成功: ${用户列表.length} 条记录，总计 ${分页配置.total} 条`);
    } else {
      throw new Error(superAdminService.getMessage(response) || '获取用户列表失败');
    }
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error);
    // 失败时清空数据
    数据源.value = [];
    分页配置.total = 0;
    message.error('获取用户列表失败，请稍后重试');
  }
};

// 获取用户列表 - 简化版本，用于刷新按钮
const 获取用户列表 = () => {
  获取用户列表数据();
};

// 判断登录状态颜色
const 判断登录状态颜色 = (最后登录时间) => {
  if (!最后登录时间) return 'default';
  
  const 现在 = dayjs();
  const 登录时间 = dayjs(最后登录时间);
  const 天数差 = 现在.diff(登录时间, 'day');
  
  if (天数差 <= 1) return 'green';      // 1天内 - 绿色（活跃）
  if (天数差 <= 7) return 'blue';       // 7天内 - 蓝色（正常）
  if (天数差 <= 30) return 'orange';    // 30天内 - 橙色（不活跃）
  return 'red';                          // 30天以上 - 红色（很久未登录）
};

// {{ AURA-X: Add - 添加调试日志，确保排序参数正确传递. Approval: 寸止(ID:1721062800). }}
const 处理表格变化 = (pag, filters, sorter) => {
  console.log('🔄 表格变化事件:', { pag, filters, sorter });
  获取用户列表数据({
    page: pag.current,
    pageSize: pag.pageSize,
    sortField: sorter.field,
    sortOrder: sorter.order,
    filters: filters,
  });
};

const 处理搜索 = () => {
  分页配置.current = 1;
  获取用户列表数据();
};

// 重置搜索表单
const 重置搜索表单 = () => {
  搜索关键词.value = '';
  搜索表单状态.状态 = undefined;
  分页配置.current = 1;
  获取用户列表数据();
};

// 导出用户数据
const 导出用户数据 = async () => {
  try {
    message.info('正在导出用户数据...');
    // 这里可以调用导出API
    message.success('用户数据导出成功');
  } catch (error) {
    message.error('导出失败: ' + error.message);
  }
};

// 查看用户详情 - 已改为抽屉形式
const 查看用户详情 = (用户) => {
  显示用户详情(用户);
};

// ==================== 模态框状态管理 ====================

// 新增用户模态框状态
const addModalVisible = ref(false);
const addModalConfirmLoading = ref(false);
const addModalFormRef = ref();
const initialAddModalFormState = () => ({
  昵称: '',
  email: '',
  密码: '',
  phone: '',
});
const addModalFormState = reactive(initialAddModalFormState());

const 显示添加模态框 = () => {
  Object.assign(addModalFormState, initialAddModalFormState()); // 重置表单
  addModalVisible.value = true;
};

const 处理添加模态框确认 = async () => {
  try {
    await addModalFormRef.value.validate();
    addModalConfirmLoading.value = true;
    const formDataToSubmit = { ...addModalFormState };
    
    const result = await 创建用户(formDataToSubmit);
    if (result) {
      message.success('用户添加成功');
      addModalVisible.value = false;
      获取用户列表数据();
    }
  } catch (error) {
    console.error('新增用户错误:', error);
    if (!(error && error.errorFields)) {
      // 错误处理已在组合式函数中处理
    }
  } finally {
    addModalConfirmLoading.value = false;
  }
};

const 处理添加模态框取消 = () => {
  addModalVisible.value = false;
};

// 删除用户
const 处理删除 = async (userId) => {
  // 使用统一的API调用，自动处理错误和成功提示
  const result = await 删除用户(userId);
  
  if (result) {
    // 成功后刷新列表
    获取用户列表数据();
  }
};

// 工具函数已在上方定义，这里删除重复代码

// 编辑用户 Modal 相关状态和逻辑
const editModalVisible = ref(false);
const editModalConfirmLoading = ref(false);
const editModalFormRef = ref();
const initialEditModalFormState = () => ({
  id: null, // 用于存储用户id
  昵称: '',
  email: '',
  phone: '',
  is_admin: false,
});
const editModalFormState = reactive(initialEditModalFormState());

const 编辑用户 = (record) => {
  // 填充表单数据
  editModalFormState.id = record.id;
  editModalFormState.昵称 = record.昵称 || record.用户名 || '';
  editModalFormState.email = record.邮箱 || record.email || '';
  editModalFormState.phone = record.手机号 || record.phone || ''; // 确保 phone 有默认值
  editModalFormState.is_admin = record.is_admin || false;
  editModalVisible.value = true;
  // console.log('编辑用户:', record) // 保留用于调试，后续可移除
}

const 处理编辑模态框确认 = async () => {
  try {
    await editModalFormRef.value.validate();
    editModalConfirmLoading.value = true;
    const { id, ...formDataToSubmit } = editModalFormState;

    // 使用新的API结构，直接传递数据给后端
    const apiPayload = {
      用户id: id,
      用户名: formDataToSubmit.昵称,
      邮箱: formDataToSubmit.email,
      手机号: formDataToSubmit.phone,
      is_admin: formDataToSubmit.is_admin
    };

    // 使用统一的API调用，自动处理错误和成功提示
    const result = await 更新用户(apiPayload);
    
    if (result) {
      // 成功后关闭弹窗并刷新列表
      editModalVisible.value = false;
      获取用户列表数据();
    }
  } catch (error) {
    // 表单验证错误由 antd form 自动处理
    if (!(error && error.errorFields)) {
      console.error('编辑用户错误:', error);
    }
  } finally {
    editModalConfirmLoading.value = false;
  }
};

const 处理编辑模态框取消 = () => {
  editModalVisible.value = false;
};

// ==================== 批量操作功能 ====================

// 批量删除用户
const 批量删除用户 = async () => {
  if (选中行键.value.length === 0) {
    message.warning('请先选择要删除的用户');
    return;
  }
  
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${选中行键.value.length} 个用户吗？此操作不可恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 这里可以调用批量删除API
        message.success(`成功删除 ${选中行键.value.length} 个用户`);
        选中行键.value = [];
        获取用户列表数据();
      } catch (error) {
        message.error('批量删除失败: ' + error.message);
      }
    }
  });
};

// 批量启用/禁用用户
const 批量切换用户状态 = async (目标状态) => {
  if (选中行键.value.length === 0) {
    message.warning('请先选择要操作的用户');
    return;
  }
  
  const 操作文本 = 目标状态 === 'active' ? '启用' : '禁用';
  
  Modal.confirm({
    title: `确认批量${操作文本}`,
    content: `确定要${操作文本}选中的 ${选中行键.value.length} 个用户吗？`,
    okText: `确认${操作文本}`,
    cancelText: '取消',
    onOk: async () => {
      try {
        // 这里可以调用批量状态更新API
        message.success(`成功${操作文本} ${选中行键.value.length} 个用户`);
        选中行键.value = [];
        获取用户列表数据();
      } catch (error) {
        message.error(`批量${操作文本}失败: ` + error.message);
      }
    }
  });
};

// ==================== 单个用户操作 ====================

// 切换用户状态
const 切换用户状态 = async (用户) => {
  const 新状态 = 用户.status === 'active' ? 'banned' : 'active';
  const 操作文本 = 新状态 === 'active' ? '启用' : '禁用';

  Modal.confirm({
    title: `确认${操作文本}用户`,
    content: `确定要${操作文本}用户 "${用户.昵称 || 用户.用户名 || `用户${用户.id}`}" 吗？`,
    okText: `确认${操作文本}`,
    cancelText: '取消',
    onOk: async () => {
      try {
        // 调用更新用户状态API
        const 更新数据 = {
          用户id: 用户.id,
          status: 新状态
        };
        
        const result = await 更新用户(更新数据);
        if (result) {
          message.success(`用户${操作文本}成功`);
          获取用户列表数据();
        }
      } catch (error) {
        message.error(`${操作文本}用户失败: ` + error.message);
      }
    }
  });
};

// 重置用户密码
const 重置用户密码 = async (用户) => {
  Modal.confirm({
    title: '确认重置密码',
    content: `确定要重置用户 "${用户.昵称 || 用户.用户名 || `用户${用户.id}`}" 的密码吗？新密码将通过邮件发送给用户。`,
    okText: '确认重置',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 这里可以调用重置密码API
        message.success('密码重置成功，新密码已发送到用户邮箱');
      } catch (error) {
        message.error('重置密码失败: ' + error.message);
      }
    }
  });
};

// 删除单个用户
const 删除单个用户 = async (用户) => {
  Modal.confirm({
    title: '确认删除用户',
    content: `确定要删除用户 "${用户.昵称 || 用户.用户名 || `用户${用户.id}`}" 吗？此操作不可恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      const result = await 删除用户(用户.id);
      if (result) {
        获取用户列表数据();
      }
    }
  });
};

// 处理用户操作菜单点击
const 处理用户操作菜单 = (menuInfo, 用户) => {
  const { key } = menuInfo;
  
  switch (key) {
    case 'reset-password':
      重置用户密码(用户);
      break;
    case 'toggle-status':
      切换用户状态(用户);
      break;
    case 'delete':
      删除单个用户(用户);
      break;
    default:
      console.warn('未知的菜单操作:', key);
  }
};

// 处理批量操作菜单点击
const 处理批量操作菜单 = (menuInfo) => {
  const { key } = menuInfo;
  
  switch (key) {
    case 'export-excel':
    case 'export-csv':
      导出用户数据();
      break;
    case 'batch-enable':
      批量切换用户状态('active');
      break;
    case 'batch-disable':
      批量切换用户状态('banned');
      break;
    case 'batch-delete':
      批量删除用户();
      break;
    default:
      console.warn('未知的批量操作:', key);
  }
};

// ==================== 用户详情抽屉 ====================

// 用户详情抽屉状态
const 用户详情抽屉可见 = ref(false)
const 当前查看用户 = ref(null)
const 用户详细统计 = ref({})
const 详情加载中 = ref(false)

// 用户详情标签页
const 详情活跃标签 = ref('basic')

// 登录历史分页配置
const 登录历史分页 = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 接口调用历史分页配置
const 接口调用历史分页 = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 登录历史数据
const 登录历史列表 = ref([])
const 接口调用历史列表 = ref([])

// 显示用户详情抽屉 - 增强版本，包含所有新功能
const 显示用户详情 = async (用户) => {
  console.log('显示用户详情:', 用户);
  当前查看用户.value = 用户;
  用户详情抽屉可见.value = true;
  详情活跃标签.value = 'basic';
  详情加载中.value = true;
  
  // 重置分页
  登录历史分页.current = 1;
  接口调用历史分页.current = 1;
  
  try {
    // 并行加载所有数据 - 包含新增的产品经理视角功能
    await Promise.all([
      加载用户详细统计(用户.id),
      loadInvitationStats(用户.id),
      loadPermissionDetails(用户.id),
      loadSecurityAudit(用户.id)
    ]);
  } catch (error) {
    console.error('加载用户详情失败:', error);
    message.error('加载用户详情失败');
  } finally {
    详情加载中.value = false;
  }
};

// 关闭用户详情抽屉
const 关闭用户详情 = () => {
  用户详情抽屉可见.value = false;
  当前查看用户.value = null;
  用户详细统计.value = {};
  登录历史列表.value = [];
  接口调用历史列表.value = [];
};

// 加载用户详细统计
const 加载用户详细统计 = async (用户id) => {
  try {
    详情加载中.value = true;
    
    const response = await superAdminService.getUserStatistics(用户id);
    if (superAdminService.isSuccess(response)) {
      用户详细统计.value = superAdminService.getData(response);
      console.log('✅ 用户详细统计加载成功:', 用户详细统计.value);
    } else {
      message.error('获取用户统计失败');
    }
  } catch (error) {
    console.error('❌ 加载用户详细统计失败:', error);
    message.error('加载用户统计失败');
  } finally {
    详情加载中.value = false;
  }
};

// 加载用户登录历史
const 加载用户登录历史 = async (页码 = 1) => {
  if (!当前查看用户.value) return;
  
  try {
    const params = {
      用户id: 当前查看用户.value.id,
      页码: 页码,
      每页数量: 登录历史分页.pageSize
    };
    
    const response = await superAdminService.getUserLoginHistoryPaged(params);
    if (superAdminService.isSuccess(response)) {
      const data = superAdminService.getData(response);
      登录历史列表.value = data.列表 || [];
      登录历史分页.total = data.总数 || 0;
      登录历史分页.current = data.页码 || 1;
      console.log('✅ 用户登录历史加载成功:', data);
    } else {
      message.error('获取登录历史失败');
    }
  } catch (error) {
    console.error('❌ 加载用户登录历史失败:', error);
    message.error('加载登录历史失败');
  }
};

// 加载用户接口调用历史
const 加载用户接口调用历史 = async (页码 = 1) => {
  if (!当前查看用户.value) return;
  
  try {
    const params = {
      用户id: 当前查看用户.value.id,
      页码: 页码,
      每页数量: 接口调用历史分页.pageSize
    };
    
    const response = await superAdminService.getUserApiHistory(params);
    if (superAdminService.isSuccess(response)) {
      const data = superAdminService.getData(response);
      接口调用历史列表.value = data.列表 || [];
      接口调用历史分页.total = data.总数 || 0;
      接口调用历史分页.current = data.页码 || 1;
      console.log('✅ 用户接口调用历史加载成功:', data);
    } else {
      message.error('获取接口调用历史失败');
    }
  } catch (error) {
    console.error('❌ 加载用户接口调用历史失败:', error);
    message.error('加载接口调用历史失败');
  }
};

// 监听详情标签页切换
const 处理详情标签切换 = (activeKey) => {
  详情活跃标签.value = activeKey;
  
  switch (activeKey) {
    case 'login':
      if (登录历史列表.value.length === 0) {
        加载用户登录历史();
      }
      break;
    case 'api':
      if (接口调用历史列表.value.length === 0) {
        加载用户接口调用历史();
      }
      break;
  }
};

// 处理登录历史分页变化
const 处理登录历史分页变化 = (页码) => {
  登录历史分页.current = 页码;
  加载用户登录历史(页码);
};

// 处理接口调用历史分页变化
const 处理接口调用历史分页变化 = (页码) => {
  接口调用历史分页.current = 页码;
  加载用户接口调用历史(页码);
};

// ==================== 新增产品经理视角功能 ====================

// 新增响应式数据
const userInvitationStats = ref(null);
const userPermissions = ref(null);
const userSecurityAudit = ref(null);

// 状态管理功能
const statusModalVisible = ref(false);
const statusLoading = ref(false);
const statusForm = reactive({
  新状态: '',
  备注: ''
});

// 批量操作功能
const batchModalVisible = ref(false);
const batchLoading = ref(false);
const batchForm = reactive({
  操作类型: '',
  参数: {
    状态: '',
    备注: ''
  }
});

// 邀约统计表格列
const invitationStatusColumns = [
  { title: '邀约状态', dataIndex: '邀约状态', key: '邀约状态' },
  { title: '数量', dataIndex: '数量', key: '数量' },
  { title: '百分比', dataIndex: '百分比', key: '百分比', render: (text) => `${text}%` }
];

const invitationTrendColumns = [
  { title: '日期', dataIndex: '日期', key: '日期' },
  { title: '邀约次数', dataIndex: '邀约次数', key: '邀约次数' },
  { title: '成功次数', dataIndex: '成功次数', key: '成功次数' }
];

// 会员权限信息表格列 - 统一显示，消除冗余
const memberPermissionColumns = [
  { title: '会员名称', dataIndex: '会员名称', key: '会员名称', width: 120 },
  { title: '会员状态', dataIndex: '会员状态', key: '会员状态', width: 80,
    customRender: ({ text }) => {
      const color = text === '有效' ? 'green' : 'red';
      return h('a-tag', { color }, text);
    }
  },
  { title: '开通时间', dataIndex: '开通时间', key: '开通时间', width: 150 },
  { title: '到期时间', dataIndex: '到期时间', key: '到期时间', width: 150 },
  { title: '剩余天数', dataIndex: '剩余天数', key: '剩余天数', width: 80,
    customRender: ({ text }) => {
      const color = text > 0 ? 'green' : 'red';
      return h('span', { style: { color } }, `${text}天`);
    }
  },
  { title: '每月费用', dataIndex: ['会员费用', '每月费用'], key: '每月费用', width: 80,
    customRender: ({ record }) => `¥${record.会员费用?.每月费用 || 0}`
  },
  { title: '每月算力点', dataIndex: ['会员权益', '每月算力点'], key: '每月算力点', width: 100,
    customRender: ({ record }) => `${record.会员权益?.每月算力点 || 0}点`
  }
];

// 权限详情表格列 - 展示具体权限
const permissionDetailColumns = [
  { title: '权限名称', dataIndex: '权限名称', key: '权限名称', width: 150 },
  { title: '权限描述', dataIndex: '权限描述', key: '权限描述' },
  { title: '权限状态', dataIndex: '权限状态', key: '权限状态', width: 80,
    customRender: ({ text }) => {
      const color = text === '有效' ? 'green' : 'red';
      return h('a-tag', { color }, text);
    }
  }
];

// 安全审计表格列
const ipColumns = [
  { title: 'IP地址', dataIndex: 'ip地址', key: 'ip地址' },
  { title: '登录次数', dataIndex: '登录次数', key: '登录次数' },
  { title: '最后使用时间', dataIndex: '最后使用时间', key: '最后使用时间' },
  { title: '首次使用时间', dataIndex: '首次使用时间', key: '首次使用时间' }
];

const recentLoginColumns = [
  { title: '登录时间', dataIndex: '登陆时间', key: '登陆时间' },
  { title: 'IP地址', dataIndex: 'IP地址', key: 'IP地址' },
  { title: '上次IP', dataIndex: '上次IP', key: '上次IP' }
];

// 加载邀约统计数据
const loadInvitationStats = async (用户id) => {
  try {
    const response = await superAdminService.getUserInvitationStats(用户id);
    if (superAdminService.isSuccess(response)) {
      userInvitationStats.value = superAdminService.getData(response);
    }
  } catch (error) {
    console.error('加载邀约统计失败:', error);
  }
};

// 加载权限详情
const loadPermissionDetails = async (用户id) => {
  try {
    const response = await superAdminService.getUserPermissionDetails(用户id);
    if (superAdminService.isSuccess(response)) {
      userPermissions.value = superAdminService.getData(response);
    }
  } catch (error) {
    console.error('加载权限详情失败:', error);
  }
};

// 加载安全审计
const loadSecurityAudit = async (用户id) => {
  try {
    const response = await superAdminService.getUserSecurityAudit(用户id);
    if (superAdminService.isSuccess(response)) {
      userSecurityAudit.value = superAdminService.getData(response);
    }
  } catch (error) {
    console.error('加载安全审计失败:', error);
  }
};

// 计算成功率
const calculateSuccessRate = (基础统计) => {
  if (!基础统计 || !基础统计.总邀约次数) return 0;
  return ((基础统计.成功邀约次数 || 0) / 基础统计.总邀约次数 * 100);
};

// 显示状态管理对话框
const showStatusModal = () => {
  statusForm.新状态 = '';
  statusForm.备注 = '';
  statusModalVisible.value = true;
};

// 更新用户状态
const updateUserStatus = async () => {
  try {
    statusLoading.value = true;
    await superAdminService.updateUserStatus(
      当前查看用户.value.id, 
      statusForm.新状态, 
      statusForm.备注
    );
    message.success('用户状态更新成功');
    statusModalVisible.value = false;
    // 刷新用户列表和详情
    获取用户列表数据();
    if (当前查看用户.value) {
      加载用户详细统计(当前查看用户.value.id);
    }
  } catch (error) {
    message.error('状态更新失败: ' + error.message);
  } finally {
    statusLoading.value = false;
  }
};

// 批量操作
const executeBatchOperation = async () => {
  try {
    batchLoading.value = true;
    await superAdminService.batchOperateUsers(
      选中行键.value,
      batchForm.操作类型,
      batchForm.参数
    );
    message.success('批量操作完成');
    batchModalVisible.value = false;
    获取用户列表数据();
  } catch (error) {
    message.error('批量操作失败: ' + error.message);
  } finally {
    batchLoading.value = false;
  }
};

// 导出用户详情
const exportUserDetail = async () => {
  try {
    const response = await superAdminService.exportUserData('详细统计', {
      用户id: [当前查看用户.value.id]
    });
    message.success('用户详情导出成功');
  } catch (error) {
    message.error('导出失败: ' + error.message);
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  if (!当前查看用户.value) return;
  
  try {
    await Promise.all([
      加载用户详细统计(当前查看用户.value.id),
      loadInvitationStats(当前查看用户.value.id),
      loadPermissionDetails(当前查看用户.value.id),
      loadSecurityAudit(当前查看用户.value.id)
    ]);
    message.success('数据刷新完成');
  } catch (error) {
    message.error('数据刷新失败');
  }
};

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'resetPassword':
      // 重置密码逻辑
      break;
    case 'sendNotification':
      // 发送通知逻辑
      break;
    case 'viewSecurity':
      详情活跃标签.value = 'security';
      break;
  }
};



onMounted(() => {
  获取用户列表数据();
});

</script>

<style scoped>
.user-management-container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px 24px;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header-left h1 {
  color: white;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 8px 0 0 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.stats-summary {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-summary :deep(.ant-statistic-title) {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin-bottom: 4px;
}

.add-user-btn {
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  transition: all 0.3s ease;
}

.add-user-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* 搜索区域样式 */
.search-section {
  margin: 0 24px 24px 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.search-section :deep(.ant-card-head) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.search-section :deep(.ant-card-head-title) {
  font-weight: 600;
  color: #495057;
}

.search-form-container {
  padding: 8px 0;
}

.search-form :deep(.ant-form-item-label) {
  font-weight: 500;
  color: #495057;
}

.search-form :deep(.ant-input) {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.search-form :deep(.ant-input:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.search-form :deep(.ant-select) {
  border-radius: 8px;
}

.search-actions {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* 表格区域样式 */
.table-section {
  margin: 0 24px 24px 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.table-section :deep(.ant-card-head) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.table-section :deep(.ant-card-head-title) {
  font-weight: 600;
  color: #495057;
}

.user-table {
  border-radius: 12px;
  overflow: hidden;
}

.user-table :deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 16px;
}

.user-table :deep(.ant-table-tbody > tr) {
  transition: all 0.3s ease;
}

.user-table :deep(.ant-table-tbody > tr:hover) {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.user-table :deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.user-id {
  font-size: 12px;
  color: #6c757d;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.contact-info .email,
.contact-info .phone {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #495057;
}

.contact-info .email :deep(.anticon),
.contact-info .phone :deep(.anticon) {
  color: #6c757d;
}

/* {{ AURA-X: Modify - 简化最后登录列样式，IP(归属地)格式. Approval: 寸止(ID:1721062800). }} */
.login-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.login-time {
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.login-ip {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
}

.no-login {
  font-size: 13px;
  color: #adb5bd;
  font-style: italic;
}

.login-status-tag {
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
}

.status-tag {
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-info .time-ago {
  font-size: 12px;
  color: #6c757d;
}

/* IP地址列样式 */
.ip-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ip-info .ip-address {
  font-weight: 500;
  color: #1890ff;
}

.ip-info .ip-location {
  font-size: 12px;
  color: #6c757d;
}

.no-ip {
  color: #d9d9d9;
  font-style: italic;
}

/* 操作按钮样式 */
.action-btn {
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 分页样式 */
.user-table :deep(.ant-pagination) {
  margin: 24px 0 0 0;
  padding: 0 16px;
}

.user-table :deep(.ant-pagination-item) {
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.user-table :deep(.ant-pagination-item-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.user-table :deep(.ant-pagination-item-active a) {
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }
  
  .search-section,
  .table-section {
    margin: 0 16px 16px 16px;
  }
  
  .header-left h1 {
    font-size: 24px;
  }
  
  .search-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-actions :deep(.ant-space) {
    flex-direction: column;
    width: 100%;
  }
  
  .search-actions :deep(.ant-space-item) {
    width: 100%;
  }
  
  .search-actions :deep(.ant-btn) {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .user-table :deep(.ant-table-tbody > tr > td) {
    padding: 12px 8px;
  }
  
  .contact-info {
    gap: 4px;
  }
  
  .contact-info .email,
  .contact-info .phone {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-section,
.table-section {
  animation: fadeInUp 0.6s ease-out;
}

.search-section {
  animation-delay: 0.1s;
}

.table-section {
  animation-delay: 0.2s;
}

/* 加载状态优化 */
.user-table :deep(.ant-spin-container) {
  min-height: 400px;
}

.user-table :deep(.ant-empty) {
  padding: 60px 0;
}

.user-table :deep(.ant-empty-description) {
  color: #6c757d;
}

/* 搜索区域样式优化 */
.search-container {
  padding: 16px 0;
}

.search-container .ant-input-search {
  border-radius: 8px;
}

.search-container .ant-select {
  border-radius: 8px;
}

.search-container .ant-btn {
  border-radius: 8px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .search-container .ant-col {
    margin-bottom: 12px;
  }

  .search-container .ant-space {
    width: 100%;
    justify-content: center;
  }
}
</style>