"""
团队成员数据操作模块
负责处理团队成员相关的数据库操作
"""

from datetime import datetime
from typing import Optional, Dict, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器

# 从工具模块导入记录团队操作日志函数
from 工具.团队工具 import 记录团队操作日志
# 导入权限初始化函数
from 数据.团队权限数据 import 初始化负责人权限, 初始化成员权限


async def 异步更新成员角色和权限(
    团队id: int,
    目标用户id: int,
    新角色: str,
    操作人ID: int
) -> Dict[str, Any]:
    """
    更新成员的角色，并根据新角色重新初始化其权限。
    只允许团队创建者执行此操作。
    同时更新团队表中的团队负责人id字段。
    """
    try:
        # 1. 验证操作人是否为团队创建者
        创建者检查SQL = "SELECT 创建人id, 团队负责人id FROM 团队表 WHERE id = $1"
        团队信息 = await 异步连接池实例.执行查询(创建者检查SQL, (团队id,))
        if not 团队信息 or 团队信息[0]["创建人id"] != 操作人ID:
            return {"success": False, "message": "只有团队创建者才能修改成员角色"}

        # 2. 检查目标用户是否为创建者，创建者角色不能被修改
        if 目标用户id == 团队信息[0]["创建人id"]:
            return {"success": False, "message": "不能修改团队创建者的角色"}

        # 3. 验证新角色是否有效
        if 新角色 not in ["团队负责人", "成员"]:
            return {"success": False, "message": f"无效的角色: {新角色}"}
        
        # 4. 更新角色
        更新角色SQL = "UPDATE 用户团队关联表 SET 职位 = $1 WHERE 用户id = $2 AND 团队id = $3"
        更新结果 = await 异步连接池实例.执行更新(更新角色SQL, (新角色, 目标用户id, 团队id))

        if not 更新结果:
            return {"success": False, "message": "更新角色失败，可能用户不在团队中"}
        
        # 5. 根据新角色更新团队负责人id
        当前团队负责人id = 团队信息[0].get("团队负责人id")
        
        if 新角色 == "团队负责人":
            # 设置为新负责人
            更新负责人SQL = "UPDATE 团队表 SET 团队负责人id = $1 WHERE id = $2"
            await 异步连接池实例.执行更新(更新负责人SQL, (目标用户id, 团队id))
            数据库日志器.info(f"更新团队负责人id: 团队id={团队id}, 新负责人ID={目标用户id}")
        elif 新角色 == "成员" and 当前团队负责人id == 目标用户id:
            # 如果是撤销负责人，且当前负责人就是该用户，则清空负责人ID
            清空负责人SQL = "UPDATE 团队表 SET 团队负责人id = NULL WHERE id = $1"
            await 异步连接池实例.执行更新(清空负责人SQL, (团队id,))
            数据库日志器.info(f"清空团队负责人id: 团队id={团队id}, 原负责人ID={目标用户id}")
            
        # 6. 根据新角色更新权限
        if 新角色 == "团队负责人":
            权限更新成功 = await 初始化负责人权限(团队id, 目标用户id, 操作人ID)
        else: # 新角色为 "成员"
            权限更新成功 = await 初始化成员权限(团队id, 目标用户id, 操作人ID)
            
        if not 权限更新成功:
            # 即使权限更新失败，角色更新也已成功，需要记录并返回警告
            错误信息 = f"角色已更新为'{新角色}'，但权限初始化失败，请手动检查权限配置。"
            错误日志器.error(f"角色权限更新部分失败: {错误信息}, 团队id={团队id}, 用户id={目标用户id}")
            return {"success": False, "message": 错误信息}
            
        # 7. 记录操作日志
        await 记录团队操作日志(
            团队id=团队id,
            操作人ID=操作人ID,
            操作类型="角色变更",
            操作内容=f"将用户(ID:{目标用户id})的角色变更为'{新角色}'"
        )
        
        数据库日志器.info(f"成功更新成员角色和权限: 团队id={团队id}, 用户id={目标用户id}, 新角色={新角色}")
        return {"success": True, "message": "成员角色和权限已成功更新"}

    except Exception as e:
        错误日志器.error(f"更新成员角色和权限异常: 团队id={团队id}, 目标用户id={目标用户id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"更新角色权限时发生未知错误: {str(e)}"}


async def 加入团队(
    用户id: int,
    团队id: int,
    职位: Optional[str] = None,
    邀请人id: Optional[int] = None,
    审批人ID: Optional[int] = None
) -> Dict[str, Any]:
    """用户加入团队"""
    try:
        # 检查用户是否已在团队中
        检查SQL = "SELECT 状态 FROM 用户团队关联表 WHERE 用户id = $1 AND 团队id = $2"
        已有记录 = await 异步连接池实例.执行查询(检查SQL, (用户id, 团队id))
        
        if 已有记录:
            当前状态 = 已有记录[0]["状态"]
            if 当前状态 == "正常":
                return {"success": False, "message": "用户已在团队中"}
            elif 当前状态 == "已移除":
                # 重新激活成员
                更新SQL = """
                UPDATE 用户团队关联表 
                SET 状态 = $1, 职位 = $2, 离开时间 = NULL, 加入时间 = $3
                WHERE 用户id = $4 AND 团队id = $5
                """
                await 异步连接池实例.执行更新(
                    更新SQL, 
                    ("正常", 职位 or "成员", datetime.now(), 用户id, 团队id)
                )
                
                # 为重新加入的成员初始化权限
                await 初始化新成员权限(团队id, 用户id, 职位 or "成员", 邀请人id or 审批人ID)
                
                数据库日志器.info(f"重新激活团队成员: 用户id={用户id}, 团队id={团队id}")
                return {"success": True, "message": "成功重新加入团队"}
        
        # 新增团队成员记录，确保加入时间不为空
        插入SQL = """
        INSERT INTO 用户团队关联表 
        (用户id, 团队id, 职位, 状态, 邀请人id, 审批人ID, 加入时间) 
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        """
        
        参数 = (用户id, 团队id, 职位 or "成员", "正常", 邀请人id, 审批人ID, datetime.now())
        插入结果 = await 异步连接池实例.执行插入(插入SQL, 参数)
        
        if 插入结果:
            # 为新成员初始化权限
            await 初始化新成员权限(团队id, 用户id, 职位 or "成员", 邀请人id or 审批人ID)
            
            # 更新团队成员数
            await 更新团队成员数统计(团队id)
            
            数据库日志器.info(f"用户加入团队成功: 用户id={用户id}, 团队id={团队id}")
            
            # 记录团队操作日志
            await 记录团队操作日志(
                团队id=团队id,
                操作人ID=用户id,
                操作类型="加入团队",
                操作内容=f"加入团队，职位：{职位 or '成员'}",
                备注=f"邀请人id：{邀请人id or '自主加入'}"
            )
            
            return {"success": True, "message": "成功加入团队"}
        else:
            return {"success": False, "message": "加入团队失败"}
            
    except Exception as e:
        错误日志器.error(f"加入团队异常: 用户id={用户id}, 团队id={团队id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"加入团队失败: {str(e)}"}


async def 初始化新成员权限(团队id: int, 用户id: int, 职位: str, 授权人ID: Optional[int] = None) -> bool:
    """为新加入的团队成员初始化权限"""
    try:
        # 导入权限初始化函数
        from 数据.团队权限数据 import 初始化负责人权限, 初始化成员权限
        
        # 获取团队创建人信息作为授权人
        if not 授权人ID:
            团队信息SQL = "SELECT 创建人id FROM 团队表 WHERE id = $1"
            团队信息 = await 异步连接池实例.执行查询(团队信息SQL, (团队id,))
            if 团队信息:
                授权人ID = 团队信息[0]["创建人id"]
            else:
                授权人ID = 用户id  # 如果找不到创建人，使用用户自己
        
        # 根据职位初始化不同的权限
        if 职位 in ["创建者", "团队负责人"]:
            初始化成功 = await 初始化负责人权限(团队id, 用户id, 授权人ID)
        else:
            初始化成功 = await 初始化成员权限(团队id, 用户id, 授权人ID)
        
        if 初始化成功:
            数据库日志器.info(f"为新成员初始化权限成功: 团队id={团队id}, 用户id={用户id}, 职位={职位}")
        else:
            错误日志器.warning(f"为新成员初始化权限失败: 团队id={团队id}, 用户id={用户id}, 职位={职位}")
        
        return 初始化成功
        
    except Exception as e:
        错误日志器.error(f"初始化新成员权限异常: 团队id={团队id}, 用户id={用户id}, 错误={e}", exc_info=True)
        return False


async def 获取团队成员列表(
    团队id: int,
    页码: int = 1,
    每页数量: int = 10,
    搜索关键词: Optional[str] = None,
    成员状态: Optional[str] = None,
    角色筛选: Optional[str] = None,
    当前用户id: Optional[int] = None
) -> Dict[str, Any]:
    """获取团队成员列表"""
    try:
        # 基础查询SQL
        基础SQL = """
        SELECT 
            ut.用户id, ut.团队id, ut.职位, ut.状态, ut.加入时间,
            COALESCE(u.昵称, u.手机号, '') as 昵称,
            u.手机号,
            u.邮箱,
            CASE WHEN ut.用户id = $1 THEN 1 ELSE 0 END as 是否为当前用户
        FROM 用户团队关联表 ut
        LEFT JOIN 用户表 u ON ut.用户id = u.id
        WHERE ut.团队id = $2
        """
        
        # 构建WHERE条件和参数
        WHERE条件 = []
        查询参数 = [当前用户id, 团队id]
        参数索引 = 3

        if 成员状态:
            WHERE条件.append(f"ut.状态 = $1")
            查询参数.append(成员状态)
            参数索引 += 1
        else:
            WHERE条件.append(f"ut.状态 != $2")
            查询参数.append("已移除")
            参数索引 += 1

        # 角色筛选支持
        if 角色筛选:
            WHERE条件.append(f"ut.职位 = $3")
            查询参数.append(角色筛选)
            参数索引 += 1

        if 搜索关键词:
            WHERE条件.append(f"(COALESCE(u.昵称, u.手机号, '') LIKE $1 OR u.手机号 LIKE $5)")
            查询参数.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
            参数索引 += 2
        
        # 添加WHERE条件
        if WHERE条件:
            基础SQL += " AND " + " AND ".join(WHERE条件)
        
        # 添加排序（创建者排第一，然后按加入时间排序）
        基础SQL += """
        ORDER BY 
            CASE WHEN ut.职位 = '创建者' THEN 1 
                 WHEN ut.职位 = '团队负责人' THEN 2 
                 WHEN ut.职位 = '管理员' THEN 3 
                 ELSE 4 END,
            ut.加入时间 ASC
        """
        
        # 分页查询
        分页SQL = 基础SQL + f" LIMIT $1 OFFSET $7"
        偏移量 = (页码 - 1) * 每页数量
        查询参数.extend([每页数量, 偏移量])
        
        # 执行查询
        成员列表 = await 异步连接池实例.执行查询(分页SQL, tuple(查询参数))
        
        # 查询总数 - 使用与分页查询相同的条件
        计数查询参数 = []
        计数SQL = "SELECT COUNT(*) AS 总数 FROM 用户团队关联表 ut LEFT JOIN 用户表 u ON ut.用户id = u.id WHERE ut.团队id = $1"
        计数查询参数.append(团队id)
        参数索引 = 2

        if 成员状态:
            计数SQL += f" AND ut.状态 = $6"
            计数查询参数.append(成员状态)
            参数索引 += 1
        else:
            计数SQL += f" AND ut.状态 != $7"
            计数查询参数.append("已移除")
            参数索引 += 1

        if 角色筛选:
            计数SQL += f" AND ut.职位 = $8"
            计数查询参数.append(角色筛选)
            参数索引 += 1

        if 搜索关键词:
            计数SQL += f" AND (COALESCE(u.昵称, u.手机号, '') LIKE $1 OR u.手机号 LIKE $10)"
            计数查询参数.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
            参数索引 += 2
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(计数查询参数))
        总数 = 总数结果[0]["总数"] if 总数结果 else 0
        
        # 格式化成员数据
        格式化成员列表 = []
        for 成员 in 成员列表:
            格式化成员列表.append({
                "用户id": 成员["用户id"],
                "昵称": 成员["昵称"],
                "手机号": 成员["手机号"],
                "邮箱": 成员["邮箱"],
                "角色": 成员["职位"],
                "状态": 成员["状态"],
                "加入时间": 成员["加入时间"],
                "是否为当前用户": bool(成员["是否为当前用户"])
            })
        
        数据库日志器.info(f"获取团队成员列表成功: 团队id={团队id}, 页码={页码}, 总数={总数}")
        
        return {
            "成员列表": 格式化成员列表,
            "总数": 总数,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数 + 每页数量 - 1) // 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"获取团队成员列表失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {
            "成员列表": [],
            "总数": 0,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": 0
        }


async def 踢出团队成员(
    团队id: int,
    被踢出用户id: int,
    操作人ID: int,
    踢出原因: Optional[str] = None
) -> Dict[str, Any]:
    """踢出团队成员"""
    try:
        # 检查是否为创建者，创建者不能被踢出
        成员检查SQL = "SELECT 职位 FROM 用户团队关联表 WHERE 用户id = $1 AND 团队id = $2"
        成员信息 = await 异步连接池实例.执行查询(成员检查SQL, (被踢出用户id, 团队id))
        
        if not 成员信息:
            return {"success": False, "message": "用户不在团队中"}
        
        if 成员信息[0]["职位"] == "创建者":
            return {"success": False, "message": "不能踢出团队创建者"}
        
        # 更新成员状态为已移除
        更新SQL = """
        UPDATE 用户团队关联表 
        SET 状态 = $1, 离开时间 = $2
        WHERE 用户id = $3 AND 团队id = $4
        """
        
        更新成功 = await 异步连接池实例.执行更新(
            更新SQL, 
            ("已移除", datetime.now(), 被踢出用户id, 团队id)
        )
        
        if 更新成功:
            # 更新团队成员数
            await 更新团队成员数统计(团队id)
            
            数据库日志器.info(f"踢出团队成员成功: 被踢用户={被踢出用户id}, 团队id={团队id}, 操作人={操作人ID}")
            
            # 记录团队操作日志
            await 记录团队操作日志(
                团队id=团队id,
                操作人ID=操作人ID,
                操作类型="踢出成员",
                操作内容=f"踢出团队成员(ID:{被踢出用户id})",
                备注=f"踢出原因：{踢出原因 or '无'}"
            )
            
            return {"success": True, "message": "团队成员已踢出"}
        else:
            return {"success": False, "message": "踢出失败，成员可能不存在"}
            
    except Exception as e:
        错误日志器.error(f"踢出团队成员失败: 团队id={团队id}, 被踢用户={被踢出用户id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"踢出团队成员失败: {str(e)}"}


async def 获取用户团队列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 10,
    团队关系类型: Optional[str] = None,
    搜索关键词: Optional[str] = None,
    公司ID: Optional[int] = None
) -> Dict[str, Any]:
    """获取用户的团队列表"""
    try:
        # 基础查询SQL
        基础SQL = """
        SELECT
            t.id as 团队id,
            t.团队名称, t.团队代码, t.团队描述, t.团队状态,
            t.最大成员数, t.当前成员数, t.创建时间,
            t.公司ID, c.公司名称, c.公司简称,
            ut.职位 as 我的角色,
            ut.状态 as 我的状态,
            ut.加入时间,
            CASE WHEN t.创建人id = $1 THEN 1 ELSE 0 END as 是否创建者,
            CASE WHEN t.团队负责人id = $2 OR ut.职位 IN ('团队负责人', '管理员') THEN 1 ELSE 0 END as 可否管理
        FROM 用户团队关联表 ut
        JOIN 团队表 t ON ut.团队id = t.id AND t.团队状态 != '解散'
        LEFT JOIN 公司表 c ON t.公司ID = c.id
        WHERE ut.用户id = $3 AND ut.状态 IN ('正常', '暂停')
        """
        
        # 构建WHERE条件和参数 - 修复参数顺序
        查询参数 = [用户id, 用户id, 用户id]

        # 计数SQL的基础部分
        计数SQL基础 = """
        FROM 用户团队关联表 ut
        JOIN 团队表 t ON ut.团队id = t.id AND t.团队状态 != '解散'
        LEFT JOIN 公司表 c ON t.公司ID = c.id
        WHERE ut.用户id = $1 AND ut.状态 IN ('正常', '暂停')
        """

        # 构建动态条件 - 修复团队关系类型逻辑
        动态条件 = ""
        参数索引 = 4  # 前面已经有3个参数：用户id, 用户id, 用户id

        if 公司ID:
            动态条件 += f" AND t.公司ID = ${参数索引}"
            查询参数.append(公司ID)
            参数索引 += 1

        if 团队关系类型:
            if 团队关系类型 == "created":
                # 只包含创建的团队
                动态条件 += f" AND t.创建人id = ${参数索引}"
                查询参数.append(用户id)
                参数索引 += 1
            elif 团队关系类型 == "managed":
                # 管理的团队：负责人或管理员，但排除创建者避免重复
                动态条件 += f" AND (t.团队负责人id = ${参数索引} OR ut.职位 = ${参数索引 + 1}) AND t.创建人id != ${参数索引 + 2}"
                查询参数.extend([用户id, "管理员", 用户id])
                参数索引 += 3
            elif 团队关系类型 == "joined":
                # 参与的团队：普通成员，排除创建者和负责人
                动态条件 += f" AND ut.职位 = ${参数索引} AND t.创建人id != ${参数索引 + 1} AND t.团队负责人id != ${参数索引 + 2}"
                查询参数.extend(["成员", 用户id, 用户id])
                参数索引 += 3
            elif 团队关系类型 == "all":
                # 所有团队，不添加额外条件
                pass
        
        if 搜索关键词:
            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL参数占位符最佳实践 }}
            动态条件 += f" AND (t.团队名称 LIKE ${参数索引} OR t.团队代码 LIKE ${参数索引 + 1})"
            查询参数.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
            参数索引 += 2
            
        # 组合最终的SQL
        完整基础SQL = 基础SQL + 动态条件
        计数SQL = "SELECT COUNT(*) as total " + 计数SQL基础 + 动态条件

        # 查询总数 - 使用独立的参数列表避免混乱
        计数查询参数 = [用户id]
        if 公司ID:
            计数查询参数.append(公司ID)
        if 团队关系类型 and 团队关系类型 != "all":
            if 团队关系类型 == "created":
                计数查询参数.append(用户id)
            elif 团队关系类型 == "managed":
                计数查询参数.extend([用户id, "管理员", 用户id])
            elif 团队关系类型 == "joined":
                计数查询参数.extend(["成员", 用户id, 用户id])
        if 搜索关键词:
            计数查询参数.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])

        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(计数查询参数))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 分页查询
        分页SQL = 完整基础SQL + f" ORDER BY ut.加入时间 DESC LIMIT ${参数索引} OFFSET ${参数索引 + 1}"
        偏移量 = (页码 - 1) * 每页数量
        查询参数.extend([每页数量, 偏移量])

        团队列表 = await 异步连接池实例.执行查询(分页SQL, tuple(查询参数))
        
        数据库日志器.info(f"获取用户团队列表成功: 用户id={用户id}, 总数={总数}")
        
        return {
            "团队列表": 团队列表,
            "总数": 总数,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数 + 每页数量 - 1) // 每页数量
        }

    except Exception as e:
        错误日志器.error(f"获取用户团队列表失败: 用户id={用户id}, 错误={e}", exc_info=True)
        return {
            "团队列表": [],
            "总数": 0,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": 0
        }


async def 更新团队成员数统计(团队id: int) -> bool:
    """更新团队的当前成员数统计"""
    try:
        # 查询实际成员数
        计数SQL = "SELECT COUNT(*) as count FROM 用户团队关联表 WHERE 团队id = $1 AND 状态 = $2"
        结果 = await 异步连接池实例.执行查询(计数SQL, (团队id, "正常"))
        实际成员数 = 结果[0]["count"] if 结果 else 0
        
        # 更新团队表中的成员数
        更新SQL = "UPDATE 团队表 SET 当前成员数 = $1, 更新时间 = $2 WHERE id = $3"
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (实际成员数, datetime.now(), 团队id))
        
        if 影响行数 > 0:
            数据库日志器.info(f"更新团队成员数统计成功: 团队id={团队id}, 成员数={实际成员数}")
            return True
        else:
            数据库日志器.warning(f"更新团队成员数统计失败: 团队id={团队id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"更新团队成员数统计异常: 团队id={团队id}, 错误={e}", exc_info=True)
        return False


async def 获取用户团队权限信息(团队id: int, 用户id: int) -> Dict[str, Any]:
    """获取用户在团队中的权限信息"""
    try:
        SQL = """
        SELECT 职位, 状态, 加入时间
        FROM 用户团队关联表
        WHERE 团队id = $1 AND 用户id = $2
        """

        结果 = await 异步连接池实例.执行查询(SQL, (团队id, 用户id))

        if not 结果:
            return {"在团队中": False, "职位": None, "状态": None}

        用户信息 = 结果[0]
        return {
            "在团队中": True,
            "职位": 用户信息["职位"],
            "状态": 用户信息["状态"],
            "加入时间": 用户信息["加入时间"],
            "是否正常成员": 用户信息["状态"] == "正常",
            "用户id": 用户id
        }

    except Exception as e:
        错误日志器.error(f"获取用户团队权限信息失败: 团队id={团队id}, 用户id={用户id}, 错误={e}")
        return {"在团队中": False, "职位": None, "状态": None}


async def 获取用户默认团队id(用户id: int) -> Optional[int]:
    """获取用户的默认团队id（第一个正常状态的团队）"""
    try:
        SQL = """
        SELECT ut.团队id
        FROM 用户团队关联表 ut
        INNER JOIN 团队表 t ON ut.团队id = t.id
        WHERE ut.用户id = $1 AND ut.状态 = '正常' AND t.团队状态 = '正常'
        ORDER BY ut.加入时间 DESC
        LIMIT 1
        """

        结果 = await 异步连接池实例.执行查询(SQL, (用户id,))

        if 结果:
            团队id = 结果[0]["团队id"]
            数据库日志器.info(f"获取用户默认团队id成功: 用户id={用户id}, 团队id={团队id}")
            return 团队id
        else:
            数据库日志器.info(f"用户 {用户id} 未加入任何正常状态的团队")
            return None

    except Exception as e:
        错误日志器.error(f"获取用户默认团队id失败: 用户id={用户id}, 错误={e}")
        return None