"""
LangChain RAG测试统一工具类
整合RAG测试的通用逻辑，减少代码冗余
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional

# 导入统一日志系统
from 日志 import 错误日志器

RAG测试工具日志器 = 错误日志器


class RAG测试工具:
    """RAG测试统一工具类"""

    @staticmethod
    async def 验证智能体RAG配置(智能体id: int) -> Dict[str, Any]:
        """验证智能体RAG配置"""
        try:
            from 服务.LangChain_智能体服务 import LangChain智能体服务实例

            # 获取智能体配置
            智能体配置 = await LangChain智能体服务实例.获取智能体配置(智能体id)
            if not 智能体配置:
                return {
                    "success": False,
                    "error": f"智能体 {智能体id} 不存在或配置无效",
                    "error_code": "AGENT_NOT_FOUND",
                }

            if not 智能体配置.get("启用RAG"):
                return {
                    "success": False,
                    "error": f"智能体 {智能体id} 未启用RAG功能",
                    "error_code": "RAG_NOT_ENABLED",
                }

            知识库列表 = 智能体配置.get("知识库列表", [])
            if not 知识库列表:
                return {
                    "success": False,
                    "error": f"智能体 {智能体id} 未关联任何知识库",
                    "error_code": "NO_KNOWLEDGE_BASE",
                }

            return {
                "success": True,
                "智能体信息": 智能体配置,
                "知识库数量": len(知识库列表),
            }

        except Exception as e:
            RAG测试工具日志器.error(f"验证智能体RAG配置失败: {str(e)}")
            return {
                "success": False,
                "error": f"验证智能体配置时发生错误: {str(e)}",
                "error_code": "VALIDATION_ERROR",
            }

    @staticmethod
    async def 获取智能体关联配置(智能体id: int) -> Dict[str, Any]:
        """获取智能体关联配置"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            查询SQL = """
            SELECT
                权重,
                检索策略,
                最大检索数量,
                相似度阈值,
                启用查询优化,
                查询优化策略,
                查询优化模型id,
                查询优化提示词
            FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND 状态 = 'active'
            ORDER BY 权重 DESC
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (智能体id,))

            if 结果:
                配置 = 结果[0]

                # 构建查询优化配置
                查询优化配置 = {}
                if 配置.get("启用查询优化"):
                    查询优化配置 = {
                        "启用": bool(配置.get("启用查询优化", False)),
                        "优化策略": 配置.get("查询优化策略", "rewrite"),
                        "优化模型id": 配置.get("查询优化模型id"),
                        "提示词模板": 配置.get("查询优化提示词", ""),
                    }

                # 构建完整配置
                完整配置 = {
                    "权重": 配置.get("权重", 1.0),
                    "检索策略": 配置.get("检索策略", "similarity"),
                    "最大检索数量": 配置.get("最大检索数量", 5),
                    "相似度阈值": 配置.get("相似度阈值", 0.7),
                    "查询优化配置": 查询优化配置,
                    "嵌入模型id": None,  # RAG测试工具暂不支持嵌入模型id配置
                }

                RAG测试工具日志器.info(
                    f"🔍 智能体 {智能体id} 关联配置: 最大检索数量={完整配置['最大检索数量']}, "
                    f"相似度阈值={完整配置['相似度阈值']}, 检索策略={完整配置['检索策略']}, "
                    f"查询优化={'启用' if 查询优化配置.get('启用', False) else '禁用'}"
                )
                return 完整配置
            else:
                RAG测试工具日志器.warning(
                    f"⚠️ 智能体 {智能体id} 未找到关联配置，使用默认值"
                )
                return {
                    "最大检索数量": 5,
                    "相似度阈值": 0.7,
                    "检索策略": "similarity",
                    "查询优化配置": {},
                }

        except Exception as e:
            RAG测试工具日志器.error(f"获取智能体关联配置失败: {str(e)}")
            return {
                "最大检索数量": 5,
                "相似度阈值": 0.7,
                "检索策略": "similarity",
                "查询优化配置": {},
            }

    @staticmethod
    def 构建检索参数(
        关联配置: Dict[str, Any], 传入参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构建统一的检索参数"""
        # 基础参数（从关联配置获取）
        基础参数 = {
            "最大检索数量": max(1, min(20, 关联配置.get("最大检索数量", 5))),
            "相似度阈值": max(0.0, min(1.0, 关联配置.get("相似度阈值", 0.7))),
            "检索策略": 关联配置.get("检索策略", "similarity"),
            "嵌入模型id": 关联配置.get("嵌入模型id"),
        }

        # 如果有传入参数，进行合并和转换
        if 传入参数:
            # 支持多种参数格式（兼容旧版本API）
            if "k" in 传入参数:
                基础参数["最大检索数量"] = max(
                    1, min(20, 传入参数.get("k", 基础参数["最大检索数量"]))
                )
            if "score_threshold" in 传入参数:
                基础参数["相似度阈值"] = max(
                    0.0,
                    min(1.0, 传入参数.get("score_threshold", 基础参数["相似度阈值"])),
                )
            if "search_type" in 传入参数:
                基础参数["检索策略"] = 传入参数.get("search_type", 基础参数["检索策略"])

            # 直接覆盖同名参数（前端传入的参数优先级最高）
            for key in ["最大检索数量", "相似度阈值", "检索策略", "嵌入模型id"]:
                if key in 传入参数 and 传入参数[key] is not None:
                    基础参数[key] = 传入参数[key]

            # 记录参数覆盖情况
            覆盖的参数 = {
                k: v for k, v in 传入参数.items() if k in 基础参数 and v is not None
            }
            if 覆盖的参数:
                print(f"🔧 前端参数覆盖数据库配置: {覆盖的参数}")

        return 基础参数

    @staticmethod
    async def 执行RAG检索(
        智能体id: int, 查询文本: str, 检索参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行统一的RAG检索"""
        开始时间 = time.time()

        try:
            from 服务.LangChain_知识库服务 import LangChain知识库服务实例

            # 确保知识库服务已初始化
            if not LangChain知识库服务实例.已初始化:
                await LangChain知识库服务实例.初始化()

            # 执行检索
            检索结果 = await LangChain知识库服务实例.智能体检索测试(
                智能体id, 查询文本, 检索参数
            )

            结束时间 = time.time()
            检索耗时 = 结束时间 - 开始时间

            if 检索结果.get("success"):
                检索数据 = 检索结果.get("data", {})
                return {
                    "success": True,
                    "检索结果": 检索数据.get("检索结果", []),
                    "检索耗时": 检索耗时,
                    "检索参数": 检索参数,
                    "结果数量": len(检索数据.get("检索结果", [])),
                }
            else:
                return {
                    "success": False,
                    "error": 检索结果.get("error", "检索失败"),
                    "检索耗时": 检索耗时,
                    "检索参数": 检索参数,
                }

        except Exception as e:
            结束时间 = time.time()
            检索耗时 = 结束时间 - 开始时间

            RAG测试工具日志器.error(f"RAG检索执行失败: {str(e)}")
            return {
                "success": False,
                "error": f"检索执行异常: {str(e)}",
                "检索耗时": 检索耗时,
                "检索参数": 检索参数,
            }

    @staticmethod
    def 生成会话ID(前缀: str = "test") -> str:
        """生成统一格式的会话ID"""
        return f"{前缀}_{int(time.time())}"

    @staticmethod
    def 创建错误响应(错误信息: str, 错误代码: str = "UNKNOWN_ERROR") -> Dict[str, Any]:
        """创建统一的错误响应"""
        return {
            "success": False,
            "error": 错误信息,
            "error_code": 错误代码,
            "timestamp": datetime.now().isoformat(),
        }

    @staticmethod
    def 创建成功响应(数据: Any, 消息: str = "操作成功") -> Dict[str, Any]:
        """创建统一的成功响应"""
        return {
            "success": True,
            "data": 数据,
            "message": 消息,
            "timestamp": datetime.now().isoformat(),
        }

    @staticmethod
    def 构建RAG增强消息(原始消息: str, 检索结果: List[Dict[str, Any]]) -> str:
        """构建RAG增强的用户消息"""
        if not 检索结果:
            return 原始消息

        # 提取有效的文档内容
        上下文片段 = []
        for i, 文档 in enumerate(检索结果[:5], 1):  # 限制最多5个文档
            内容 = 文档.get("文档内容", "") or 文档.get("content", "")
            if 内容 and len(内容.strip()) > 10:
                上下文片段.append(f"[知识片段{i}]\n{内容.strip()}")

        if not 上下文片段:
            return 原始消息

        # 构建增强消息
        return f"""基于以下上下文信息回答问题：

上下文信息：
{chr(10).join(上下文片段)}

问题：{原始消息}

请基于上下文信息提供准确、完整的回答。如果上下文信息不足以回答问题，请明确说明。"""
