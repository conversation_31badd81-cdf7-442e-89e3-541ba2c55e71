import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../store/user'

// 路由懒加载
const Login = () => import('../views/Login.vue')
const Register = () => import('../views/Register.vue')
const ForgotPassword = () => import('../views/ForgotPassword.vue')
const CrmLayout = () => import('../layouts/CrmLayout.vue')
const WorkspaceDashboard = () => import('../views/WorkspaceDashboard.vue')
const WorkspaceTest = () => import('../views/WorkspaceTest.vue')
const TrendChartTest = () => import('../views/TrendChartTest.vue')


// 达人管理相关页面
const TalentLayout = () => import('../layouts/TalentLayout.vue')
const MyTalents = () => import('../views/talent/MyTalents.vue')
const TeamTalents = () => import('../views/talent/TeamTalents.vue')
const TalentPool = () => import('../views/talent/TalentPool.vue')
const LeadsCenter = () => import('../views/talent/LeadsCenter.vue')

const FriendManagement = () => import('../views/FriendManagement.vue')
const StoreLayout = () => import('../layouts/StoreLayout.vue')
const StoreManagement = () => import('../views/StoreManagement.vue')
const KnowledgeBaseCreation = () => import('../views/KnowledgeBaseCreation.vue')
const MembershipManagement = () => import('../views/membership/MembershipManagement.vue')
const ActivationCenter = () => import('../views/membership/ActivationCenter.vue')
const TeamLayout = () => import('../layouts/TeamLayout.vue')
const TeamOverview = () => import('../views/team/MyTeams.vue')
const TeamDetail = () => import('../views/team/TeamDetail.vue')
const TeamDashboard = () => import('../views/team/TeamDashboard.vue')
// PersonalCenter组件已删除，个人中心功能已整合到系统设置中
const SystemSettings = () => import('../views/system/SystemSettings.vue')
const NotificationCenter = () => import('../views/NotificationCenter.vue')
const InvitationAccept = () => import('../views/InvitationAccept.vue')
const NotFound = () => import('../views/NotFound.vue')

// 好友管理子页面
const FriendLayout = () => import('../layouts/FriendLayout.vue')
const FriendOverview = () => import('../views/friend/FriendOverview.vue')
const FriendWechatAccounts = () => import('../views/friend/FriendWechatAccounts.vue')
const FriendList = () => import('../views/friend/FriendList.vue')
const FriendProgressManagement = () => import('../views/friend/FriendProgressManagement.vue')


const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      hideInMenu: true
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      title: '忘记密码',
      hideInMenu: true
    }
  },
  {
    path: '/invitation/:token',
    name: 'InvitationAccept',
    component: InvitationAccept,
    meta: {
      title: '邀请确认',
      hideInMenu: true
    }
  },

  {
    path: '/',
    component: CrmLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: WorkspaceDashboard,
        meta: {
          title: '工作台',
          icon: 'DashboardOutlined'
        }
      },

      {
        path: 'workspace-test',
        name: 'WorkspaceTest',
        component: WorkspaceTest,
        meta: {
          title: '工作台测试',
          icon: 'ExperimentOutlined',
          hideInMenu: true
        }
      },
      {
        path: 'trend-chart-test',
        name: 'TrendChartTest',
        component: TrendChartTest,
        meta: {
          title: '趋势图表测试',
          icon: 'LineChartOutlined',
          hideInMenu: true
        }
      },
      {
        path: 'talent',
        name: 'TalentManagement',
        component: TalentLayout,
        redirect: '/talent/my-talents',
        meta: {
          title: '达人管理',
          icon: 'UserOutlined'
        },
        children: [
          {
            path: 'my-talents',
            name: 'MyTalents',
            component: MyTalents,
            meta: {
              title: '我的达人',
              parent: '达人管理'
            }
          },
          {
            path: 'team-talents',
            name: 'TeamTalents',
            component: TeamTalents,
            meta: {
              title: '团队达人',
              parent: '达人管理'
            }
          },
          {
            path: 'talent-pool',
            name: 'TalentPool',
            component: TalentPool,
            meta: {
              title: '达人公海',
              parent: '达人管理'
            }
          },
          {
            path: 'leads-center',
            name: 'LeadsCenter',
            component: LeadsCenter,
            meta: {
              title: '线索大全',
              parent: '达人管理'
            }
          },
          {
            path: 'contact-info',
            name: 'ContactInfo',
            component: () => import('../views/talent/ContactInfo.vue'),
            meta: {
              title: '联系方式',
              parent: '达人管理'
            }
          }
        ]
      },
      {
        path: 'friend',
        component: FriendLayout,
        redirect: '/friend/overview',
        meta: {
          title: '好友管理',
          icon: 'TeamOutlined'
        },
        children: [
          {
            path: 'overview',
            name: 'FriendOverview',
            component: FriendOverview,
            meta: {
              title: '概览',
              parent: '好友管理'
            }
          },
          {
            path: 'wechat-accounts',
            name: 'FriendWechatAccounts',
            component: FriendWechatAccounts,
            meta: {
              title: '微信账号',
              parent: '好友管理'
            }
          },
          {
            path: 'friend-list',
            name: 'FriendList',
            component: FriendList,
            meta: {
              title: '好友列表',
              parent: '好友管理'
            }
          },
          {
            path: 'progress-management',
            name: 'FriendProgressManagement',
            component: FriendProgressManagement,
            meta: {
              title: '对接进度',
              parent: '好友管理'
            }
          },
          {
            path: 'auto-add',
            name: 'AutoAddFriends',
            component: () => import('../views/friend/AutoAddFriends.vue'),
            meta: {
              title: '自动添加',
              parent: '好友管理'
            }
          }

        ]
      },
      {
        path: 'store',
        name: 'StoreManagement',
        component: StoreLayout,
        redirect: '/store/store-info',
        meta: {
          title: '内容管理',
          icon: 'AppstoreOutlined'
        },
        children: [
          {
            path: 'store-info',
            name: 'StoreInfo',
            component: () => import('../views/store/StoreInfo.vue'),
            meta: {
              title: '店铺信息',
              parent: '内容管理'
            }
          },
          {
            path: 'product-management',
            name: 'ProductManagement',
            component: () => import('../views/store/ProductManagement.vue'),
            meta: {
              title: '产品管理',
              parent: '内容管理'
            }
          },
          {
            path: 'douyin-products',
            name: 'DouyinProducts',
            component: () => import('../views/store/DouyinProducts.vue'),
            meta: {
              title: '抖音商品',
              parent: '内容管理'
            }
          },
          {
            path: 'sample-management',
            name: 'SampleManagement',
            component: () => import('../views/store/SampleManagement.vue'),
            meta: {
              title: '样品管理',
              parent: '内容管理'
            }
          },
          {
            path: 'knowledge-management',
            name: 'KnowledgeManagement',
            component: () => import('../views/store/KnowledgeManagement.vue'),
            meta: {
              title: '知识库管理',
              parent: '内容管理'
            }
          },
          {
            path: 'order-management',
            name: 'OrderManagement',
            component: () => import('../views/store/OrderManagement.vue'),
            meta: {
              title: '订单管理',
              parent: '内容管理'
            }
          }
        ]
      },
      {
        path: 'knowledge-creation',
        name: 'KnowledgeBaseCreation',
        component: KnowledgeBaseCreation,
        meta: {
          title: '知识库创建',
          icon: 'DatabaseOutlined'
        }
      },

      {
        path: 'team',
        name: 'TeamManagement',
        component: TeamLayout,
        redirect: '/team/dashboard',
        meta: {
          title: '团队管理',
          icon: 'ApartmentOutlined'
        },
        children: [
          {
            path: 'dashboard',
            name: 'TeamDashboard',
            component: TeamDashboard,
            meta: {
              title: '数据看板',
              parent: '团队管理'
            }
          },
          {
            path: 'overview',
            name: 'TeamOverview',
            component: TeamOverview,
            meta: {
              title: '团队总览',
              parent: '团队管理'
            }
          },

          {
            path: 'detail/:teamId',
            name: 'TeamDetail',
            component: TeamDetail,
            meta: {
              title: '团队详情',
              parent: '团队管理'
            }
          }
        ]
      },
      {
        path: 'membership',
        name: 'MembershipManagement',
        component: MembershipManagement,
        meta: {
          title: '会员中心',
          icon: 'CrownOutlined'
        }
      },
      {
        path: 'activation',
        name: 'ActivationCenter',
        component: ActivationCenter,
        meta: {
          title: '激活中心',
          icon: 'KeyOutlined'
        }
      },
      {
        path: 'personal',
        name: 'PersonalCenter',
        redirect: '/settings?tab=personal', // 重定向到系统设置的个人信息页面
        meta: {
          title: '个人中心',
          icon: 'UserOutlined',
          hideInMenu: true // 不在侧边栏显示，通过用户下拉菜单访问
        }
      },
      {
        path: 'notifications',
        name: 'NotificationCenter',
        component: NotificationCenter,
        meta: {
          title: '通知中心',
          icon: 'BellOutlined',
          hideInMenu: true // 不在侧边栏显示，通过通知图标访问
        }
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: {
          title: '系统设置',
          icon: 'SettingOutlined',
          hideInMenu: true // 不在侧边栏显示，通过用户下拉菜单访问
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 更新页面标题
  document.title = to.meta.title ? `${to.meta.title} - 灵邀AI达人管家` : '灵邀AI达人管家'

  // 检查是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!userStore.isAuthenticated) {
      // 未认证，跳转到登录页
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 已认证，验证token有效性
      try {
        const isValid = await userStore.validateLogin()
        if (isValid) {
          // token有效，允许访问
          next()
        } else {
          // token无效，跳转到登录页
          next({
            name: 'Login',
            query: { redirect: to.fullPath }
          })
        }
      } catch (error) {
        console.error('路由守卫验证登录状态失败:', error)
        // 验证失败，跳转到登录页
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
      }
    }
  } else if (['Login', 'Register', 'ForgotPassword'].includes(to.name) && userStore.isAuthenticated) {
    // 已认证用户访问登录相关页面，重定向到工作台
    next({ name: 'Dashboard' })
  } else {
    // 不需要认证的页面，直接访问
    next()
  }
})

export default router 