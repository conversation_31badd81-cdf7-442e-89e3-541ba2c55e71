"""
LangChain RAG测试服务

功能：
1. 智能体RAG端到端测试
2. 检索功能专项测试
3. 生成功能专项测试
4. RAG评估和分析
5. 性能指标统计
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from 日志 import 应用日志器 as RAG测试日志器
from 日志 import 错误日志器

# 导入相关服务
from 服务.LangChain_智能体服务 import LangChain智能体服务实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例


class LangChainRAG测试服务:
    """LangChain RAG测试服务 - 基于Context7最佳实践"""

    def __init__(self):
        self.已初始化 = False
        self.测试统计 = {
            "总测试次数": 0,
            "成功次数": 0,
            "失败次数": 0,
            "平均耗时": 0,
        }

    async def 初始化(self):
        """初始化RAG测试服务"""
        try:
            RAG测试日志器.info("🔧 初始化LangChain RAG测试服务...")

            # 确保依赖服务已初始化
            if not LangChain智能体服务实例.已初始化:
                await LangChain智能体服务实例.初始化()

            if not LangChain知识库服务实例.已初始化:
                await LangChain知识库服务实例.初始化()

            self.已初始化 = True
            RAG测试日志器.info("✅ LangChain RAG测试服务初始化成功")
            return True

        except Exception as e:
            错误日志器.error(f"❌ LangChain RAG测试服务初始化失败: {str(e)}")
            return False

    async def 执行RAG专项测试(
        self,
        智能体id: int,
        测试查询: str,
        测试模式: str = "complete",
        检索参数: Optional[Dict[str, Any]] = None,
        生成参数: Optional[Dict[str, Any]] = None,
        评估指标: Optional[List[str]] = None,
        基准答案: Optional[str] = None,
        详细分析: bool = True,
        会话id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        执行智能体RAG专项测试

        Args:
            智能体id: 智能体id
            测试查询: 测试查询文本
            测试模式: complete/retrieval_only/generation_only
            检索参数: 检索参数配置
            生成参数: 生成参数配置
            评估指标: 评估指标列表
            基准答案: 基准答案用于评估
            详细分析: 是否返回详细分析
            会话id: 会话ID
        """
        开始时间 = time.time()

        try:
            RAG测试日志器.info(
                f"🧪 开始RAG专项测试 - 智能体: {智能体id}, 模式: {测试模式}"
            )

            # 验证智能体
            验证结果 = await self._验证智能体RAG配置(智能体id)
            if not 验证结果["success"]:
                # 传递错误码和详细信息
                return {
                    "success": False,
                    "message": 验证结果.get("error", "智能体RAG配置验证失败"),
                    "error_code": 验证结果.get("error_code", "VALIDATION_ERROR"),
                    "data": None,
                }

            智能体信息 = 验证结果["智能体信息"]

            # 生成测试会话ID
            if not 会话id:
                会话id = f"rag_test_{int(time.time())}"

            # 获取查询优化配置
            查询优化配置 = await self._获取智能体查询优化配置(智能体id)

            # 初始化测试结果
            测试结果 = {
                "基本信息": {
                    "智能体id": 智能体id,
                    "智能体名称": 智能体信息["智能体名称"],
                    "测试查询": 测试查询,
                    "测试模式": 测试模式,
                    "会话ID": 会话id,
                    "测试时间": datetime.now().isoformat(),
                    "查询优化": {
                        "启用": 查询优化配置.get("启用", False),
                        "优化策略": 查询优化配置.get("优化策略", "未配置"),
                        "优化模型id": 查询优化配置.get("优化模型id", "未配置"),
                    },
                },
                "查询优化阶段": {},
                "检索阶段": {},
                "生成阶段": {},
                "评估结果": {},
                "性能指标": {},
                "错误信息": None,
            }

            # 执行测试流程
            if 测试模式 in ["complete", "retrieval_only"]:
                检索结果 = await self._执行检索测试(智能体id, 测试查询, 检索参数)
                测试结果["检索阶段"] = 检索结果

            if 测试模式 in ["complete", "generation_only"]:
                # 获取检索上下文
                检索上下文 = []
                if 测试模式 == "complete" and "检索阶段" in 测试结果:
                    检索上下文 = 测试结果["检索阶段"].get("检索结果", [])

                生成结果 = await self._执行生成测试(
                    智能体id, 测试查询, 检索上下文, 生成参数, 会话id
                )
                测试结果["生成阶段"] = 生成结果

            # 执行评估
            if 评估指标:
                评估结果 = await self._执行RAG评估(测试结果, 评估指标, 基准答案)
                测试结果["评估结果"] = 评估结果

            # 计算性能指标
            结束时间 = time.time()
            测试结果["性能指标"] = self._计算性能指标(测试结果, 开始时间, 结束时间)

            # 简化结果（如果需要）
            if not 详细分析:
                测试结果 = self._简化测试结果(测试结果)

            # 更新统计
            self._更新测试统计(True, 结束时间 - 开始时间)

            RAG测试日志器.info(
                f"✅ RAG专项测试完成 - 耗时: {测试结果['性能指标']['总耗时']}s"
            )

            return {"success": True, "data": 测试结果, "message": "RAG专项测试完成"}

        except Exception as e:
            结束时间 = time.time()
            self._更新测试统计(False, 结束时间 - 开始时间)

            错误日志器.error(f"❌ RAG专项测试失败: {str(e)}", exc_info=True)
            return self._创建错误结果("RAG专项测试失败")

    def _创建错误结果(self, message: str) -> Dict[str, Any]:
        """创建统一的错误返回结果"""
        return {"success": False, "message": message}

    async def _验证智能体RAG配置(self, 智能体id: int) -> Dict[str, Any]:
        """验证智能体RAG配置"""
        from 服务.LangChain_RAG测试工具 import RAG测试工具

        return await RAG测试工具.验证智能体RAG配置(智能体id)

    async def _执行检索测试(
        self, 智能体id: int, 查询文本: str, 检索参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """执行检索测试"""
        try:
            # 使用统一工具执行RAG检索
            from 服务.LangChain_RAG测试工具 import RAG测试工具

            # 获取关联配置并构建检索参数
            关联配置 = await RAG测试工具.获取智能体关联配置(智能体id)
            统一检索参数 = RAG测试工具.构建检索参数(关联配置, 检索参数)

            # 执行检索
            检索结果 = await RAG测试工具.执行RAG检索(智能体id, 查询文本, 统一检索参数)

            # 转换为测试结果格式
            if 检索结果.get("success"):
                return {
                    "success": True,
                    "检索结果": 检索结果.get("检索结果", []),
                    "检索耗时": 检索结果.get("检索耗时", 0),
                    "检索参数": 统一检索参数,
                    "结果数量": 检索结果.get("结果数量", 0),
                }
            else:
                return {
                    "success": False,
                    "error": 检索结果.get("error", "检索失败"),
                    "检索耗时": 检索结果.get("检索耗时", 0),
                    "检索参数": 统一检索参数,
                }

        except Exception as e:
            RAG测试日志器.error(f"检索测试失败: {str(e)}")
            return {
                "success": False,
                "error": f"检索测试异常: {str(e)}",
                "检索耗时": 0,
                "检索参数": 检索参数 or {},
            }

    def _计算检索统计(self, 检索结果列表: List, 总相似度分数: float) -> Dict[str, Any]:
        """计算检索统计信息"""
        if not 检索结果列表:
            return {
                "检索数量": 0,
                "平均相似度": 0,
                "最高相似度": 0,
                "最低相似度": 0,
                "相似度标准差": 0,
            }

        平均相似度 = round(总相似度分数 / len(检索结果列表), 4)
        分数列表 = [float(score) for _, score in 检索结果列表]

        return {
            "检索数量": len(检索结果列表),
            "平均相似度": 平均相似度,
            "最高相似度": round(max(分数列表), 4),
            "最低相似度": round(min(分数列表), 4),
            "相似度标准差": self._计算标准差(分数列表),
        }

    def _计算标准差(self, 数值列表: List[float]) -> float:
        """计算标准差"""
        if not 数值列表:
            return 0.0

        import math

        平均值 = sum(数值列表) / len(数值列表)
        方差 = sum((x - 平均值) ** 2 for x in 数值列表) / len(数值列表)
        标准差 = math.sqrt(方差)

        return round(标准差, 4)

    def _更新测试统计(self, 成功: bool, 耗时: float):
        """更新测试统计"""
        self.测试统计["总测试次数"] += 1
        if 成功:
            self.测试统计["成功次数"] += 1
        else:
            self.测试统计["失败次数"] += 1

        # 更新平均耗时
        总耗时 = self.测试统计["平均耗时"] * (self.测试统计["总测试次数"] - 1) + 耗时
        self.测试统计["平均耗时"] = round(总耗时 / self.测试统计["总测试次数"], 3)

    def 获取测试统计(self) -> Dict[str, Any]:
        """获取测试统计信息"""
        return {
            "服务状态": "运行中" if self.已初始化 else "未初始化",
            "测试统计": self.测试统计.copy(),
            "成功率": round(
                self.测试统计["成功次数"] / max(1, self.测试统计["总测试次数"]) * 100, 2
            ),
        }

    async def _执行生成测试(
        self,
        智能体id: int,
        查询文本: str,
        检索上下文: List[Dict[str, Any]],
        生成参数: Optional[Dict[str, Any]] = None,
        会话id: str = None,
    ) -> Dict[str, Any]:
        """执行生成测试"""
        开始时间 = time.time()

        try:
            # 构建RAG提示词
            上下文文本 = ""
            if 检索上下文:
                上下文片段 = []
                for i, 文档 in enumerate(检索上下文[:5]):  # 限制上下文数量
                    内容 = 文档.get("文档内容", "")
                    if 内容:
                        上下文片段.append(f"[文档{i + 1}] {内容}")
                上下文文本 = "\n\n".join(上下文片段)

            # 构建增强的用户消息
            if 上下文文本:
                增强消息 = f"""基于以下上下文信息回答问题：

上下文信息：
{上下文文本}

问题：{查询文本}

请基于上下文信息提供准确、完整的回答。如果上下文信息不足以回答问题，请明确说明。"""
            else:
                增强消息 = 查询文本

            # 调用智能体进行生成
            生成结果 = await LangChain智能体服务实例.智能体对话(
                智能体id=智能体id,
                用户表id=0,  # 管理员测试
                用户消息=增强消息,
                会话id=会话id or f"rag_gen_test_{int(time.time())}",
                测试模式=True,
                自定义变量=生成参数.get("自定义变量") if 生成参数 else None,
            )

            结束时间 = time.time()
            生成耗时 = round(结束时间 - 开始时间, 3)

            if 生成结果.get("status") == 100:
                生成数据 = 生成结果.get("data", {})
                智能体回复 = 生成数据.get("智能体回复", "")

                # 分析生成质量
                生成分析 = {
                    "回复长度": len(智能体回复),
                    "使用上下文": bool(上下文文本),
                    "上下文数量": len(检索上下文),
                    "令牌消耗": 生成数据.get("令牌消耗", 0),
                    "处理时长": 生成数据.get("处理时长", 0),
                }

                RAG测试日志器.info(
                    f"✅ 生成测试完成 - 耗时: {生成耗时}s, 回复长度: {len(智能体回复)}"
                )

                return {
                    "成功": True,
                    "耗时": 生成耗时,
                    "智能体回复": 智能体回复,
                    "生成分析": 生成分析,
                    "使用的上下文": 上下文文本[:1000] + "..."
                    if len(上下文文本) > 1000
                    else 上下文文本,
                    "令牌消耗": 生成数据.get("令牌消耗", 0),
                    "原始查询": 查询文本,
                    "增强消息": 增强消息[:500] + "..."
                    if len(增强消息) > 500
                    else 增强消息,
                }
            else:
                错误信息 = 生成结果.get("message", "生成失败")
                RAG测试日志器.error(f"❌ 智能体生成失败: {错误信息}")

                return {
                    "成功": False,
                    "错误": 错误信息,
                    "耗时": 生成耗时,
                    "智能体回复": "",
                    "生成分析": {},
                    "使用的上下文": 上下文文本,
                    "令牌消耗": 0,
                }

        except Exception as e:
            结束时间 = time.time()
            生成耗时 = round(结束时间 - 开始时间, 3)

            RAG测试日志器.error(f"❌ 生成测试异常: {str(e)}")
            return {
                "成功": False,
                "错误": str(e),
                "耗时": 生成耗时,
                "智能体回复": "",
                "生成分析": {},
                "使用的上下文": "",
                "令牌消耗": 0,
            }

    async def _执行RAG评估(
        self,
        测试结果: Dict[str, Any],
        评估指标: List[str],
        基准答案: Optional[str] = None,
    ) -> Dict[str, Any]:
        """执行RAG评估"""
        try:
            评估结果 = {}

            # 检索相关性评估
            if "relevance" in 评估指标 and "检索阶段" in 测试结果:
                检索阶段 = 测试结果["检索阶段"]
                if 检索阶段.get("成功"):
                    检索统计 = 检索阶段.get("检索统计", {})
                    平均相似度 = 检索统计.get("平均相似度", 0)

                    相关性评分 = min(100, max(0, int(平均相似度 * 100)))

                    评估结果["检索相关性"] = {
                        "评分": 相关性评分,
                        "等级": self._获取评分等级(相关性评分),
                        "平均相似度": 平均相似度,
                        "检索数量": 检索统计.get("检索数量", 0),
                    }

            # 生成准确性评估
            if "accuracy" in 评估指标 and "生成阶段" in 测试结果:
                生成阶段 = 测试结果["生成阶段"]
                if 生成阶段.get("成功"):
                    智能体回复 = 生成阶段.get("智能体回复", "")
                    准确性评分 = self._评估回复准确性(智能体回复, 基准答案)

                    评估结果["生成准确性"] = {
                        "评分": 准确性评分,
                        "等级": self._获取评分等级(准确性评分),
                        "回复长度": len(智能体回复),
                        "有基准答案": bool(基准答案),
                    }

            # 完整性评估
            if "completeness" in 评估指标:
                完整性评分 = self._评估回复完整性(测试结果)

                评估结果["回复完整性"] = {
                    "评分": 完整性评分,
                    "等级": self._获取评分等级(完整性评分),
                    "检索成功": 测试结果.get("检索阶段", {}).get("成功", False),
                    "生成成功": 测试结果.get("生成阶段", {}).get("成功", False),
                }

            # 连贯性评估
            if "coherence" in 评估指标 and "生成阶段" in 测试结果:
                生成阶段 = 测试结果["生成阶段"]
                if 生成阶段.get("成功"):
                    智能体回复 = 生成阶段.get("智能体回复", "")
                    连贯性评分 = self._评估回复连贯性(智能体回复)

                    评估结果["回复连贯性"] = {
                        "评分": 连贯性评分,
                        "等级": self._获取评分等级(连贯性评分),
                        "句子数量": len(智能体回复.split("。")) if 智能体回复 else 0,
                    }

            # 计算综合评分
            所有评分 = []
            for _, 指标数据 in 评估结果.items():
                if isinstance(指标数据, dict) and "评分" in 指标数据:
                    所有评分.append(指标数据["评分"])

            综合评分 = round(sum(所有评分) / len(所有评分), 1) if 所有评分 else 0

            评估结果["综合评估"] = {
                "综合评分": 综合评分,
                "综合等级": self._获取评分等级(综合评分),
                "评估指标数": len(评估指标),
                "完成指标数": len(所有评分),
            }

            RAG测试日志器.info(f"✅ RAG评估完成 - 综合评分: {综合评分}")

            return {
                "成功": True,
                "评估结果": 评估结果,
                "评估指标": 评估指标,
                "基准答案": 基准答案[:100] + "..."
                if 基准答案 and len(基准答案) > 100
                else 基准答案,
            }

        except Exception as e:
            RAG测试日志器.error(f"❌ RAG评估失败: {str(e)}")
            return {
                "成功": False,
                "错误": str(e),
                "评估结果": {},
                "评估指标": 评估指标,
            }

    def _计算性能指标(
        self, 测试结果: Dict[str, Any], 开始时间: float, 结束时间: float
    ) -> Dict[str, Any]:
        """计算性能指标"""
        检索阶段 = 测试结果.get("检索阶段", {})
        生成阶段 = 测试结果.get("生成阶段", {})

        return {
            "总耗时": round(结束时间 - 开始时间, 3),
            "检索耗时": 检索阶段.get("检索耗时", 检索阶段.get("耗时", 0)),
            "生成耗时": 生成阶段.get("耗时", 0),
            "令牌消耗": 生成阶段.get("令牌消耗", 0),
        }

    def _简化测试结果(self, 测试结果: Dict[str, Any]) -> Dict[str, Any]:
        """简化测试结果"""
        简化结果 = {
            "基本信息": 测试结果.get("基本信息", {}),
            "性能指标": 测试结果.get("性能指标", {}),
        }

        # 简化检索阶段
        if "检索阶段" in 测试结果:
            检索阶段 = 测试结果["检索阶段"]
            简化结果["检索摘要"] = {
                "成功": 检索阶段.get("成功", False),
                "耗时": 检索阶段.get("耗时", 0),
                "结果数量": len(检索阶段.get("检索结果", [])),
                "平均相似度": 检索阶段.get("检索统计", {}).get("平均相似度", 0),
            }

        # 简化生成阶段
        if "生成阶段" in 测试结果:
            生成阶段 = 测试结果["生成阶段"]
            简化结果["生成摘要"] = {
                "成功": 生成阶段.get("成功", False),
                "耗时": 生成阶段.get("耗时", 0),
                "回复长度": len(生成阶段.get("智能体回复", "")),
                "令牌消耗": 生成阶段.get("令牌消耗", 0),
            }

        # 简化评估结果
        if "评估结果" in 测试结果:
            评估结果 = 测试结果["评估结果"]
            if 评估结果.get("成功"):
                简化结果["评估摘要"] = 评估结果.get("评估结果", {}).get("综合评估", {})

        return 简化结果

    def _获取评分等级(self, 评分: float) -> str:
        """根据评分获取等级"""
        if 评分 >= 90:
            return "优秀"
        elif 评分 >= 80:
            return "良好"
        elif 评分 >= 70:
            return "中等"
        elif 评分 >= 60:
            return "及格"
        else:
            return "不及格"

    def _评估回复准确性(self, 智能体回复: str, 基准答案: Optional[str] = None) -> int:
        """评估回复准确性"""
        if not 智能体回复:
            return 0

        基础评分 = min(70, len(智能体回复) // 10)  # 每10个字符1分，最高70分

        if 基准答案:
            # 简单的关键词匹配评估
            智能体关键词 = set(智能体回复.split())
            基准关键词 = set(基准答案.split())

            if 基准关键词:
                匹配度 = len(智能体关键词 & 基准关键词) / len(基准关键词)
                相似度评分 = int(匹配度 * 30)
                return min(100, 基础评分 + 相似度评分)

        # 内容质量评估
        质量评分 = 0
        if "。" in 智能体回复 or "!" in 智能体回复 or "？" in 智能体回复:
            质量评分 += 10
        if len(智能体回复) > 50:
            质量评分 += 10
        if "根据" in 智能体回复 or "基于" in 智能体回复:
            质量评分 += 10

        return min(100, 基础评分 + 质量评分)

    def _评估回复完整性(self, 测试结果: Dict[str, Any]) -> int:
        """评估回复完整性"""
        评分 = 0

        if 测试结果.get("检索阶段", {}).get("成功"):
            评分 += 40
            检索数量 = len(测试结果["检索阶段"].get("检索结果", []))
            if 检索数量 >= 3:
                评分 += 10

        if 测试结果.get("生成阶段", {}).get("成功"):
            评分 += 40
            回复长度 = len(测试结果["生成阶段"].get("智能体回复", ""))
            if 回复长度 >= 100:
                评分 += 10

        return min(100, 评分)

    def _评估回复连贯性(self, 智能体回复: str) -> int:
        """评估回复连贯性"""
        if not 智能体回复:
            return 0

        评分 = 50  # 基础分

        句子列表 = [s.strip() for s in 智能体回复.split("。") if s.strip()]
        if len(句子列表) >= 2:
            评分 += 20

        连接词 = [
            "因此",
            "所以",
            "但是",
            "然而",
            "另外",
            "此外",
            "首先",
            "其次",
            "最后",
        ]
        if any(词 in 智能体回复 for 词 in 连接词):
            评分 += 15

        if len(set(句子列表)) == len(句子列表):
            评分 += 15

        return min(100, 评分)

    async def _获取智能体查询优化配置(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体的查询优化配置"""
        try:
            from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例

            # 使用新的数据层方法获取完整配置
            知识库配置列表 = await LangChain智能体数据层实例.获取智能体关联知识库配置(
                智能体id
            )

            if 知识库配置列表:
                # 取第一个知识库的查询优化配置（按权重排序）
                主配置 = 知识库配置列表[0]
                查询优化配置 = 主配置.get("查询优化配置", {})

                RAG测试日志器.debug(f"智能体 {智能体id} 查询优化配置: {查询优化配置}")
                return 查询优化配置
            else:
                RAG测试日志器.debug(f"智能体 {智能体id} 未关联知识库或未配置查询优化")
                return {}

        except Exception as e:
            RAG测试日志器.error(f"获取智能体查询优化配置失败: {str(e)}")
            return {}


# 创建全局服务实例
LangChainRAG测试服务实例 = LangChainRAG测试服务()
