"""
用户数据操作模块
整合所有用户相关的数据库操作，统一使用PostgreSQL语法
包含用户基础操作、权限管理、管理员功能等
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 安全日志器, 数据库日志器, 系统日志器, 错误日志器

# ==================== 用户基础操作 ====================


async def 创建用户(手机号: str, 哈希密码: str) -> int:
    """
    创建用户（返回用户id）

    Args:
        手机号: 用户手机号
        哈希密码: 加密后的密码

    Returns:
        创建的用户id

    Raises:
        ConnectionError: 数据库连接异常
        Exception: 其他创建异常
    """
    try:
        插入SQL = """
        INSERT INTO 用户表 (手机号, password) 
        VALUES ($1, $2) 
        RETURNING id
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(插入SQL, 手机号, 哈希密码)
            用户id = 结果["id"] if 结果 else None

            if 用户id is None:
                raise Exception("创建用户失败，未返回用户id")

            数据库日志器.info(f"用户创建成功: ID={用户id}, 手机号={手机号}")
            return 用户id

    except Exception as e:
        错误日志器.error(f"创建用户异常 (电话: {手机号}): {str(e)}")
        raise


async def 创建半注册用户(手机号: str) -> int:
    """
    创建半注册用户（仅用于邀请未注册用户）
    创建状态为"未注册"的用户记录，只包含手机号和用户id

    Args:
        手机号: 手机号码，必须是11位数字

    Returns:
        int: 创建的用户id，如果失败则抛出异常

    Raises:
        ValueError: 手机号格式不正确
        Exception: 数据库操作异常
    """
    try:
        # 验证手机号格式
        if not 手机号 or len(手机号) != 11 or not 手机号.isdigit():
            raise ValueError(f"手机号格式不正确: {手机号}")

        插入SQL = """
        INSERT INTO 用户表 (手机号, 状态) 
        VALUES ($1, '未注册') 
        RETURNING id
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(插入SQL, 手机号)
            用户id = 结果["id"] if 结果 else None

            if 用户id is None:
                raise Exception("创建半注册用户失败，未返回用户id")

            数据库日志器.info(f"半注册用户创建成功: ID={用户id}, 手机号={手机号}")
            return 用户id

    except Exception as e:
        错误日志器.error(f"创建半注册用户异常 (电话: {手机号}): {str(e)}")
        raise


async def 根据手机号获取用户信息(手机号: str) -> Optional[Dict[str, Any]]:
    """
    根据手机号获取用户信息

    Args:
        手机号: 用户手机号

    Returns:
        用户信息字典，不存在返回None
    """
    try:
        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        查询SQL = """
        SELECT id, 昵称, 手机号, password, 状态, 每日邀约次数,
               每日快递查询次数, 算力值, 可创建团队数, 是否自动审核,
               level, experience_points, 邀请人, 代理类型表id,
               created_at, is_admin
        FROM 用户表
        WHERE 手机号 = $1
        """

        结果 = await 异步连接池实例.执行单行查询(查询SQL, (手机号,))

        if 结果:
            数据库日志器.debug(f"根据手机号获取用户信息成功: {手机号}")
            return 结果
        else:
            数据库日志器.debug(f"手机号对应的用户不存在: {手机号}")
            return None

    except Exception as e:
        错误日志器.error(f"根据手机号获取用户信息失败: {str(e)}")
        return None


async def 根据用户id获取用户信息(用户id: int) -> Optional[Dict[str, Any]]:
    """
    根据用户id获取用户信息

    Args:
        用户id: 用户的唯一标识

    Returns:
        用户信息字典，不存在返回None
    """
    try:
        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        查询SQL = """
        SELECT id, 昵称, 手机号, password, 状态, 每日邀约次数,
               每日快递查询次数, 算力值, 可创建团队数, 是否自动审核,
               level, experience_points, 邀请人, 代理类型表id,
               created_at, is_admin
        FROM 用户表
        WHERE id = $1
        """

        结果 = await 异步连接池实例.执行单行查询(查询SQL, (用户id,))

        if 结果:
            数据库日志器.debug(f"获取用户信息成功: ID={用户id}")
            return 结果
        else:
            数据库日志器.warning(f"用户不存在: ID={用户id}")
            return None

    except Exception as e:
        错误日志器.error(f"获取用户信息失败: {str(e)}")
        return None


async def 用户是否存在_电话(电话: str) -> bool:
    """
    检查用户是否存在（通过电话）

    Args:
        电话: 用户手机号

    Returns:
        用户是否存在
    """
    try:
        查询SQL = "SELECT 1 FROM 用户表 WHERE 手机号 = $1 LIMIT 1"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 电话)
            return 结果 is not None

    except Exception as e:
        错误日志器.error(f"检查用户是否存在失败: {str(e)}")
        return False


async def 用户是否存在_id(用户id: int) -> bool:
    """
    检查用户是否存在（通过ID）

    Args:
        用户id: 用户id

    Returns:
        用户是否存在
    """
    try:
        查询SQL = "SELECT 1 FROM 用户表 WHERE id = $1 LIMIT 1"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户id)
            return 结果 is not None

    except Exception as e:
        错误日志器.error(f"检查用户是否存在失败: {str(e)}")
        return False


async def 用户是否已注册_电话(电话: str) -> bool:
    """
    检查用户是否已完成注册（通过电话）

    Args:
        电话: 用户手机号

    Returns:
        用户是否已注册
    """
    try:
        查询SQL = """
        SELECT 1 FROM 用户表 
        WHERE 手机号 = $1 AND (状态 IS NULL OR 状态 != '未注册') 
        LIMIT 1
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 电话)
            return 结果 is not None

    except Exception as e:
        错误日志器.error(f"检查用户是否已注册失败: {str(e)}")
        return False


async def 获取用户密码哈希(用户id: int) -> Optional[str]:
    """
    获取用户密码哈希值

    Args:
        用户id: 用户id

    Returns:
        密码哈希值或None
    """
    try:
        查询SQL = "SELECT password FROM 用户表 WHERE id = $1"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户id)
            return 结果["password"] if 结果 else None

    except Exception as e:
        错误日志器.error(f"获取用户密码哈希失败: {str(e)}")
        return None


async def 更新用户密码(用户id: int, 新密码哈希: str) -> bool:
    """
    更新用户密码

    Args:
        用户id: 用户id
        新密码哈希: 新的密码哈希值

    Returns:
        是否更新成功
    """
    try:
        更新SQL = "UPDATE 用户表 SET password = $1 WHERE id = $2"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 新密码哈希, 用户id)
            影响行数 = int(结果.split()[-1]) if 结果.startswith("UPDATE") else 0

            if 影响行数 > 0:
                数据库日志器.info(f"用户密码更新成功: ID={用户id}")
                return True
            else:
                数据库日志器.warning(f"用户密码更新失败，用户可能不存在: ID={用户id}")
                return False

    except Exception as e:
        错误日志器.error(f"更新用户密码失败: {str(e)}")
        return False


async def 更新用户昵称(用户id: int, 新昵称: str) -> bool:
    """
    更新用户昵称

    Args:
        用户id: 用户id
        新昵称: 新的昵称

    Returns:
        是否更新成功
    """
    try:
        更新SQL = "UPDATE 用户表 SET 昵称 = $1 WHERE id = $2"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 新昵称, 用户id)
            影响行数 = int(结果.split()[-1]) if 结果.startswith("UPDATE") else 0

            if 影响行数 > 0:
                数据库日志器.info(f"用户昵称更新成功: ID={用户id}, 新昵称={新昵称}")
                return True
            else:
                数据库日志器.warning(f"用户昵称更新失败，用户可能不存在: ID={用户id}")
                return False

    except Exception as e:
        错误日志器.error(f"更新用户昵称失败: {str(e)}")
        return False


# ==================== 用户业务数据操作 ====================


async def 增加用户邀约次数(用户id: int, 增加数量: int) -> bool:
    """
    异步增加用户的每日邀约次数。如果原次数为NULL，则视为0再增加。

    Args:
        用户id: 用户id
        增加数量: 要增加的次数

    Returns:
        是否成功
    """
    try:
        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        # 使用 COALESCE 处理 NULL 值，确保其被视为 0
        更新SQL = """
        UPDATE 用户表
        SET 每日邀约次数 = COALESCE(每日邀约次数, 0) + $1
        WHERE id = $2
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, (增加数量, 用户id))

        if 影响行数 > 0:
            数据库日志器.info(
                f"成功为用户id {用户id} 增加 {增加数量} 次每日邀约次数。"
            )
            return True
        else:
            数据库日志器.warning(
                f"尝试为用户id {用户id} 增加邀约次数，但可能用户不存在。影响行数: {影响行数}"
            )
            return False

    except Exception as e:
        错误日志器.error(f"异步为用户id {用户id} 增加邀约次数异常: {str(e)}")
        return False


async def 更新用户算力值(用户id: int, 新算力值: int) -> bool:
    """
    更新用户算力值

    Args:
        用户id: 用户id
        新算力值: 新的算力值

    Returns:
        是否更新成功
    """
    try:
        更新SQL = "UPDATE 用户表 SET 算力值 = $1 WHERE id = $2"

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 新算力值, 用户id)
            影响行数 = int(结果.split()[-1]) if 结果.startswith("UPDATE") else 0

            if 影响行数 > 0:
                数据库日志器.info(
                    f"用户算力值更新成功: ID={用户id}, 新算力值={新算力值}"
                )
                return True
            else:
                数据库日志器.warning(f"用户算力值更新失败，用户可能不存在: ID={用户id}")
                return False

    except Exception as e:
        错误日志器.error(f"更新用户算力值失败: {str(e)}")
        return False


async def 获取用户权限_id(用户id: int) -> Optional[Dict[str, Any]]:
    """
    获取用户权限信息

    Args:
        用户id: 用户id

    Returns:
        用户权限信息字典或None
    """
    try:
        查询SQL = """
        SELECT u.id, u.昵称, u.手机号, u.每日邀约次数,
               u.每日快递查询次数, u.算力值, u.可创建团队数, u.是否自动审核,
               u.level, u.experience_points, u.is_admin
        FROM 用户表 u
        WHERE u.id = $1
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户id)

            if 结果:
                权限信息 = dict(结果)
                数据库日志器.debug(f"获取用户权限信息成功: ID={用户id}")
                return 权限信息
            else:
                数据库日志器.warning(f"用户权限信息不存在: ID={用户id}")
                return None

    except Exception as e:
        错误日志器.error(f"获取用户权限信息失败: {str(e)}")
        return None


async def 获取用户昵称_id(用户id: int) -> Optional[str]:
    """获取用户昵称"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT 昵称 FROM 用户表 WHERE id = $1", (用户id,)
        )
        return 结果[0]["昵称"] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户昵称失败: {str(e)}")
        return None


async def 获取用户邀请人id(用户id: int) -> Optional[int]:
    """获取用户邀请人id"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT 邀请人 FROM 用户表 WHERE id = $1", (用户id,)
        )
        return 结果[0]["邀请人"] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户邀请人id失败: {str(e)}")
        return None


async def 获取用户含邀约上限(用户id: int) -> Optional[Dict[str, Any]]:
    """
    获取用户信息包含邀约上限

    Args:
        用户id: 用户id

    Returns:
        包含邀约上限的用户信息字典或None
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, 每日邀约次数, 算力值, level,
               experience_points, 可创建团队数, 是否自动审核
        FROM 用户表
        WHERE id = $1
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户id)

            if 结果:
                用户信息 = dict(结果)
                数据库日志器.debug(f"获取用户含邀约上限信息成功: ID={用户id}")
                return 用户信息
            else:
                数据库日志器.warning(f"用户不存在: ID={用户id}")
                return None

    except Exception as e:
        错误日志器.error(f"获取用户含邀约上限信息失败: {str(e)}")
        return None


async def 获取用户今日成功邀约次数(用户id: int) -> int:
    """
    获取用户今日成功邀约次数

    Args:
        用户id: 用户id

    Returns:
        今日成功邀约次数
    """
    try:
        查询SQL = """
        SELECT COUNT(*) as 今日邀约次数
        FROM 用户客户邀请记录表
        WHERE 邀请人id = $1
        AND DATE(FROM_TIMESTAMP(邀请时间)) = CURRENT_DATE
        AND 状态 = '成功'
        """

        async with 异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户id)
            return 结果["今日邀约次数"] if 结果 else 0

    except Exception as e:
        错误日志器.error(f"获取用户今日成功邀约次数失败: {str(e)}")
        return 0


async def 获取用户昵称和邀约次数(用户id: int) -> Optional[Dict[str, Any]]:
    """
    获取用户昵称和邀约次数信息

    Args:
        用户id: 用户id

    Returns:
        包含昵称和邀约次数的字典或None
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 每日邀约次数
        FROM 用户表
        WHERE id = $1
        """

        结果 = await 异步连接池实例.执行单行查询(查询SQL, (用户id,))

        if 结果:
            数据库日志器.debug(f"获取用户昵称和邀约次数成功: ID={用户id}")
            return 结果
        else:
            数据库日志器.warning(f"用户不存在: ID={用户id}")
            return None

    except Exception as e:
        错误日志器.error(f"获取用户昵称和邀约次数失败: {str(e)}")
        return None


# ==================== JWT认证相关 ====================


async def 获取JWT认证信息(用户id: int) -> Dict[str, Any]:
    """
    获取用户JWT认证所需的完整信息

    Args:
        用户id: 用户id

    Returns:
        包含用户信息、会员信息、权限信息的字典
    """
    try:
        # 获取用户基本信息
        用户信息_sql = """
        SELECT id, 手机号, 昵称, is_admin, level, experience_points,
               算力值, 每日邀约次数, 可创建团队数, 状态
        FROM 用户表
        WHERE id = $1
        """
        用户结果 = await 异步连接池实例.执行查询(用户信息_sql, (用户id,))

        if not 用户结果:
            return {}

        用户信息 = 用户结果[0]

        # 获取会员信息
        会员信息_sql = """
        SELECT m.id as 会员id, m.名称 as 会员名称, m.每月算力点, m.可创建团队数,
               m.创建团队默认人数上限, um.到期时间
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = $1 AND (um.到期时间 IS NULL OR um.到期时间 > NOW())
        ORDER BY um.开通时间 DESC
        LIMIT 1
        """
        会员结果 = await 异步连接池实例.执行查询(会员信息_sql, (用户id,))

        # 获取权限列表（通过会员获取权限）
        权限信息_sql = """
        SELECT DISTINCT p.id as 权限id, p.名称 as 权限名称, p.描述 as 权限描述
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        JOIN 会员_权限_关联表 mp ON m.id = mp.会员id
        JOIN 权限表 p ON mp.权限id = p.id
        WHERE um.用户id = $1 AND (um.到期时间 IS NULL OR um.到期时间 > NOW())
        """
        权限结果 = await 异步连接池实例.执行查询(权限信息_sql, (用户id,))

        # 构建返回数据
        jwt_info = {
            "id": 用户信息["id"],
            "手机号": 用户信息["手机号"],
            "昵称": 用户信息["昵称"] or "",
            "is_admin": bool(用户信息.get("is_admin", False)),
            "会员信息": {
                "会员id": None,
                "会员名称": "免费用户",
                "到期时间": None,
                "算力点": 用户信息.get("算力值", 0),
                "可创建团队数": 用户信息.get("可创建团队数", 0),
                "创建团队默认人数上限": 0,
            },
            "权限信息": [],
        }

        # 填充会员信息
        if 会员结果:
            会员信息 = 会员结果[0]
            # 处理到期时间，确保JSON序列化兼容
            到期时间 = 会员信息["到期时间"]
            if isinstance(到期时间, datetime):
                到期时间 = 到期时间.isoformat()

            jwt_info["会员信息"] = {
                "会员id": 会员信息["会员id"],
                "会员名称": 会员信息["会员名称"],
                "到期时间": 到期时间,
                "算力点": 会员信息["每月算力点"],
                "可创建团队数": 会员信息["可创建团队数"],
                "创建团队默认人数上限": 会员信息["创建团队默认人数上限"],
            }

        # 填充权限信息
        if 权限结果:
            jwt_info["权限信息"] = [
                {"权限名称": p["权限名称"], "权限描述": p["权限描述"]} for p in 权限结果
            ]

        数据库日志器.debug(f"获取JWT认证信息成功: 用户id={用户id}")
        return jwt_info

    except Exception as e:
        错误日志器.error(f"获取用户JWT认证信息异常: 用户id={用户id}, 错误={str(e)}")
        # 返回最基本的用户信息，确保JWT能正常生成
        try:
            基本信息_sql = "SELECT id, 手机号, 昵称, is_admin FROM 用户表 WHERE id = $1"
            基本结果 = await 异步连接池实例.执行查询(基本信息_sql, (用户id,))
            if 基本结果:
                基本信息 = 基本结果[0]
                return {
                    "id": 基本信息["id"],
                    "手机号": 基本信息["手机号"],
                    "昵称": 基本信息["昵称"],
                    "is_admin": bool(基本信息.get("is_admin", False)),
                    "会员信息": {
                        "会员id": None,
                        "会员名称": "免费用户",
                        "到期时间": None,
                        "算力点": 0,
                        "可创建团队数": 0,
                        "创建团队默认人数上限": 0,
                    },
                    "权限信息": [],
                }
        except Exception:
            pass
        return {}


# ==================== 邀约日志记录 ====================


async def 记录邀约日志(
    邀请人id: int,
    被邀请人手机号: str,
    邀约类型: str,
    邀约状态: str,
    备注: Optional[str] = None,
) -> bool:
    """
    记录邀约日志

    Args:
        邀请人id: 邀请人用户id
        被邀请人手机号: 被邀请人手机号
        邀约类型: 邀约类型
        邀约状态: 邀约状态
        备注: 备注信息

    Returns:
        是否记录成功
    """
    try:
        插入SQL = """
        INSERT INTO 用户客户邀请记录表 (邀请人id, 被邀请人手机号, 邀约类型, 状态, 邀请时间, 备注)
        VALUES ($1, $2, $3, $4, $5, $6)
        """

        当前时间戳 = int(time.time())

        async with 异步连接池实例.获取连接() as 连接:
            await 连接.execute(
                插入SQL, 邀请人id, 被邀请人手机号, 邀约类型, 邀约状态, 当前时间戳, 备注
            )

        数据库日志器.info(
            f"邀约日志记录成功: 邀请人id={邀请人id}, 被邀请人={被邀请人手机号}"
        )
        return True

    except Exception as e:
        错误日志器.error(f"记录邀约日志失败: {str(e)}")
        return False


# ==================== 管理员功能 ====================


async def 异步验证管理员(
    用户名: str, 密码: str, 客户端IP: Optional[str] = None
) -> Dict[str, Any]:
    """
    验证管理员登录

    Args:
        用户名: 管理员用户名（手机号）
        密码: 密码
        客户端IP: 客户端IP地址

    Returns:
        验证结果字典
    """
    try:
        # 根据手机号查询用户信息
        查询SQL = """
        SELECT id, 手机号, password, is_admin, 昵称, 状态
        FROM 用户表
        WHERE 手机号 = $1
        """
        结果 = await 异步连接池实例.执行查询(查询SQL, (用户名,))

        if not 结果:
            return {"成功": False, "消息": "用户不存在"}

        用户信息 = dict(结果[0])

        # 检查是否为管理员
        if not 用户信息.get("is_admin", False):
            return {"成功": False, "消息": "权限不足，非管理员用户"}

        # 验证密码
        if 密码 != 用户信息.get("password"):
            return {"成功": False, "消息": "密码错误"}

        # 检查用户状态
        用户状态 = 用户信息.get("状态")
        if 用户状态 != "正常":
            if 用户状态 is None:
                return {"成功": False, "消息": "账户状态未设置，请联系管理员激活账户"}
            elif 用户状态 == "禁用":
                return {"成功": False, "消息": "账户已被禁用，请联系管理员"}
            elif 用户状态 == "未注册":
                return {"成功": False, "消息": "账户尚未完成注册，请先完成注册流程"}
            else:
                return {"成功": False, "消息": f"账户状态异常：{用户状态}，请联系管理员"}

        # 记录管理员登录
        if 客户端IP:
            await 记录管理员登录(用户信息["id"], 客户端IP)

        return {
            "成功": True,
            "用户id": 用户信息["id"],
            "用户名": 用户信息.get("昵称", 用户名),
            "消息": "验证成功",
        }

    except Exception as e:
        错误日志器.error(f"验证管理员登录失败: 用户名={用户名}, 错误={str(e)}")
        return {"成功": False, "消息": "验证过程中发生错误"}


async def 异步验证管理员权限(用户id: int) -> bool:
    """
    验证用户是否为管理员（仅检查权限）

    Args:
        用户id: 用户id

    Returns:
        是否为管理员
    """
    try:
        查询SQL = "SELECT is_admin FROM 用户表 WHERE id = $1"
        结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        if 结果:
            return bool(结果[0].get("is_admin", False))
        return False

    except Exception as e:
        错误日志器.error(f"验证管理员权限失败: 用户id={用户id}, 错误={str(e)}")
        return False


async def 异步检查管理员权限(用户id: int) -> Dict[str, Any]:
    """
    检查管理员权限详情

    Args:
        用户id: 用户id

    Returns:
        权限详情字典
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, is_admin, 状态
        FROM 用户表
        WHERE id = $1
        """
        结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        if 结果:
            用户信息 = dict(结果[0])
            return {
                "success": True,
                "is_admin": bool(用户信息.get("is_admin", False)),
                "用户信息": 用户信息,
            }
        else:
            return {"success": False, "message": "用户不存在"}

    except Exception as e:
        错误日志器.error(f"检查管理员权限失败: 用户id={用户id}, 错误={str(e)}")
        return {"success": False, "message": f"检查权限失败: {str(e)}"}


async def 记录管理员登录(用户id: int, 登录IP: Optional[str] = None) -> bool:
    """
    记录管理员登录日志（使用用户登陆记录表）

    Args:
        用户id: 管理员用户id
        登录IP: 登录IP地址

    Returns:
        是否记录成功
    """
    try:
        插入SQL = """
        INSERT INTO 用户登陆记录表 (用户id, 登陆时间, ip地址)
        VALUES ($1, CURRENT_TIMESTAMP, $2)
        """

        await 异步连接池实例.执行更新(插入SQL, (用户id, 登录IP))
        数据库日志器.info(f"记录管理员登录成功: 用户id={用户id}, IP={登录IP}")
        return True

    except Exception as e:
        错误日志器.error(f"记录管理员登录失败: 用户id={用户id}, 错误={str(e)}")
        return False


async def 异步更新用户(用户id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
    """
    更新用户信息（管理员功能）

    Args:
        用户id: 用户id
        更新数据: 要更新的数据字典

    Returns:
        更新结果
    """
    try:
        if not 更新数据:
            return {"success": False, "message": "没有要更新的数据"}

        # 构建更新SQL
        字段列表 = []
        参数列表 = []
        参数索引 = 1

        允许字段 = [
            "昵称",
            "手机号",
            "邮箱",
            "状态",
            "每日邀约次数",
            "算力值",
            "可创建团队数",
            "is_admin",
            "level",
        ]

        for 字段名, 值 in 更新数据.items():
            if 字段名 in 允许字段:
                字段列表.append(f'"{字段名}" = ${参数索引}')
                参数列表.append(值)
                参数索引 += 1

        if not 字段列表:
            return {"success": False, "message": "没有有效的更新字段"}

        # 添加更新时间
        字段列表.append("updated_at = CURRENT_TIMESTAMP")

        更新SQL = f"""
        UPDATE 用户表
        SET {", ".join(字段列表)}
        WHERE id = ${参数索引}
        """
        参数列表.append(用户id)

        影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(参数列表))

        if 影响行数 > 0:
            数据库日志器.info(f"更新用户成功: 用户id={用户id}")
            return {"success": True, "message": "更新成功"}
        else:
            return {"success": False, "message": "用户不存在或没有变更"}

    except Exception as e:
        错误日志器.error(f"更新用户失败: 用户id={用户id}, 错误={str(e)}")
        return {"success": False, "message": f"更新失败: {str(e)}"}


async def 获取用户列表(
    页码: int = 1,
    每页数量: int = 10,
    搜索关键词: Optional[str] = None,
    邮箱筛选: Optional[str] = None,
    手机号筛选: Optional[str] = None,
    排序字段: Optional[str] = None,
    排序顺序: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户列表，支持分页、筛选、排序

    Args:
        页码: 页码，从1开始
        每页数量: 每页数量
        搜索关键词: 用户名筛选条件
        邮箱筛选: 邮箱筛选条件
        手机号筛选: 手机号筛选条件
        排序字段: 排序字段
        排序顺序: 排序顺序（asc/desc）

    Returns:
        包含用户列表和分页信息的字典
    """
    try:
        # 构建WHERE条件
        where_conditions = []
        params = []
        param_count = 0

        if 搜索关键词:
            param_count += 1
            where_conditions.append("昵称 ILIKE ${param_count}")
            params.append(f"%{搜索关键词}%")

        if 邮箱筛选:
            param_count += 1
            where_conditions.append("邮箱 ILIKE ${param_count}")
            params.append(f"%{邮箱筛选}%")

        if 手机号筛选:
            param_count += 1
            where_conditions.append("手机号 ILIKE ${param_count}")
            params.append(f"%{手机号筛选}%")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 验证排序字段
        valid_sort_fields = ["id", "昵称", "手机号", "邮箱", "created_at", "level"]
        if 排序字段 not in valid_sort_fields:
            排序字段 = "created_at"

        # 验证排序顺序
        排序顺序 = 排序顺序.upper() if 排序顺序 else "DESC"
        if 排序顺序 not in ["ASC", "DESC"]:
            排序顺序 = "DESC"

        # 计算偏移量
        offset = (页码 - 1) * 每页数量

        # 查询总数
        count_sql = f"SELECT COUNT(*) as total FROM 用户表 WHERE {where_clause}"
        count_result = await 异步连接池实例.执行查询(count_sql, tuple(params))
        总数量 = count_result[0]["total"] if count_result else 0

        # 查询数据
        data_sql = f"""
        SELECT
            id, 昵称, 手机号, 邮箱, level, is_admin, 状态,
            created_at, 每日邀约次数, 算力值, 可创建团队数
        FROM 用户表
        WHERE {where_clause}
        ORDER BY {排序字段} {排序顺序}
        LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}
        """
        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数占位符文档 }}
        params.extend([每页数量, offset])

        用户列表 = await 异步连接池实例.执行查询(data_sql, tuple(params))

        系统日志器.info(f"获取用户列表成功: 页码={页码}, 总数={总数量}")

        return {
            "用户列表": 用户列表,
            "总数": 总数量,
            "当前页": 页码,
            "每页数量": 每页数量,
            "总页数": (总数量 + 每页数量 - 1) // 每页数量,
        }

    except Exception as e:
        错误日志器.error(f"获取用户列表失败: {str(e)}")
        raise


async def 获取用户总数() -> int:
    """
    获取用户总数

    Returns:
        用户总数
    """
    try:
        查询SQL = "SELECT COUNT(*) as total FROM 用户表"
        结果 = await 异步连接池实例.执行查询(查询SQL)
        return 结果[0]["total"] if 结果 else 0

    except Exception as e:
        错误日志器.error(f"获取用户总数失败: {str(e)}")
        return 0


async def 获取今日新增用户数() -> int:
    """
    获取今日新增用户数

    Returns:
        今日新增用户数
    """
    try:
        查询SQL = """
        SELECT COUNT(*) as today_count
        FROM 用户表
        WHERE DATE(created_at) = CURRENT_DATE
        """
        结果 = await 异步连接池实例.执行查询(查询SQL)
        return 结果[0]["today_count"] if 结果 else 0

    except Exception as e:
        错误日志器.error(f"获取今日新增用户数失败: {str(e)}")
        return 0


async def 添加用户(
    用户名: str,
    邮箱: str,
    密码: str,
    手机号: Optional[str] = "",
    用户类型: str = "普通用户",
) -> Dict[str, Any]:
    """添加新用户"""
    try:
        # 检查用户名是否已存在
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE 昵称 = $1", (用户名,)
        )
        if 结果:
            return {"success": False, "message": "用户名已存在"}

        # 检查邮箱是否已存在
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE 邮箱 = $1", (邮箱,)
        )
        if 结果:
            return {"success": False, "message": "邮箱已存在"}

        # 如果提供了手机号，检查是否已存在
        if 手机号 and 手机号.strip():
            结果 = await 异步连接池实例.执行查询(
                "SELECT id FROM 用户表 WHERE 手机号 = $1", (手机号,)
            )
            if 结果:
                return {"success": False, "message": "手机号已存在"}
        else:
            手机号 = None

        # 插入新用户
        插入查询 = """
        INSERT INTO 用户表 (昵称, 邮箱, password, 手机号, created_at)
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        RETURNING id
        """

        插入结果 = await 异步连接池实例.执行查询(插入查询, (用户名, 邮箱, 密码, 手机号))

        if 插入结果:
            用户id = 插入结果[0]["id"]
            系统日志器.info(f"成功添加用户: {用户名}, ID: {用户id}")
            return {"success": True, "userId": 用户id}
        else:
            return {"success": False, "message": "添加用户失败"}

    except Exception as e:
        错误日志器.error(f"添加用户失败: {str(e)}")
        return {"success": False, "message": f"添加用户失败: {str(e)}"}


async def 获取用户信息(用户id: int) -> Optional[Dict[str, Any]]:
    """获取单个用户详细信息"""
    try:
        用户结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id,
                COALESCE(昵称, '') as 昵称,
                COALESCE(邮箱, email, '') as 邮箱,
                COALESCE(手机号, phone, '') as 手机号,
                COALESCE(is_admin, 0) as is_admin,
                COALESCE(level, 1) as level,
                邀请人,
                created_at,
                COALESCE(状态, 'active') as 状态,
                COALESCE(每日邀约次数, 30) as 每日邀约次数
            FROM 用户表
            WHERE id = $1
            """,
            (用户id,),
        )

        if not 用户结果:
            系统日志器.warning(f"用户不存在: 用户id={用户id}")
            return None

        用户 = 用户结果[0]

        # 处理日期格式
        创建时间 = ""
        if 用户.get("created_at"):
            if isinstance(用户["created_at"], datetime):
                创建时间 = 用户["created_at"].strftime("%Y-%m-%d %H:%M:%S")
            else:
                创建时间 = str(用户["created_at"])

        用户信息 = {
            "id": 用户["id"],
            "昵称": 用户["昵称"],
            "邮箱": 用户["邮箱"],
            "手机号": 用户["手机号"],
            "is_admin": bool(用户["is_admin"]),
            "level": 用户["level"],
            "邀请人": 用户["邀请人"],
            "创建时间": 创建时间,
            "状态": 用户["状态"],
            "每日邀约次数": 用户["每日邀约次数"],
        }

        系统日志器.info(
            f"获取用户信息成功: 用户id={用户id}, 昵称={用户信息.get('昵称')}"
        )
        return 用户信息
    except Exception as e:
        错误日志器.error(f"获取用户信息失败: 用户id={用户id}, 错误={e}", exc_info=True)
        return None


async def 获取用户关联店铺列表(
    用户id: int, 页码: int = 1, 每页数量: int = 10
) -> Dict[str, Any]:
    """
    获取用户关联的店铺列表

    Args:
        用户id: 用户id
        页码: 页码
        每页数量: 每页数量

    Returns:
        店铺列表和总数
    """
    try:
        # 查询总数
        计数SQL = """
        SELECT COUNT(*) as total
        FROM 用户店铺关联表
        WHERE 用户id = $1
        """
        总数结果 = await 异步连接池实例.执行查询(计数SQL, (用户id,))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = """
        SELECT s.*, us.关联时间
        FROM 用户店铺关联表 us
        JOIN 店铺表 s ON us.店铺ID = s.id
        WHERE us.用户id = $1
        ORDER BY us.关联时间 DESC
        LIMIT $2 OFFSET $3
        """

        店铺列表 = await 异步连接池实例.执行查询(查询SQL, (用户id, 每页数量, 偏移量))

        return {
            "success": True,
            "data": 店铺列表 or [],
            "total": 总数,
            "page": 页码,
            "page_size": 每页数量,
        }

    except Exception as e:
        错误日志器.error(f"获取用户关联店铺列表失败: 用户id={用户id}, 错误={str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户接口调用历史分页(
    用户id: int, 页码: int = 1, 每页数量: int = 20
) -> Dict[str, Any]:
    """
    获取用户接口调用历史（分页）

    Args:
        用户id: 用户id
        页码: 页码
        每页数量: 每页数量

    Returns:
        接口调用历史列表
    """
    try:
        # 查询总数
        计数SQL = """
        SELECT COUNT(*) as total
        FROM 接口调用日志表
        WHERE 用户id = $1
        """
        总数结果 = await 异步连接池实例.执行查询(计数SQL, (用户id,))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = """
        SELECT 接口路径, 请求方法, 响应状态码, 调用时间, 响应时间, IP地址
        FROM 接口调用日志表
        WHERE 用户id = $1
        ORDER BY 调用时间 DESC
        LIMIT $2 OFFSET $3
        """

        调用历史 = await 异步连接池实例.执行查询(查询SQL, (用户id, 每页数量, 偏移量))

        return {
            "success": True,
            "data": 调用历史 or [],
            "total": 总数,
            "page": 页码,
            "page_size": 每页数量,
        }

    except Exception as e:
        错误日志器.error(f"获取用户接口调用历史失败: 用户id={用户id}, 错误={str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户最后登录详情(用户id: int) -> Dict[str, Any]:
    """
    获取用户最后登录详情

    Args:
        用户id: 用户id

    Returns:
        最后登录详情
    """
    try:
        查询SQL = """
        SELECT 登陆时间, ip地址, NULL as 设备信息, NULL as 浏览器信息
        FROM 用户登陆记录表
        WHERE 用户id = $1
        ORDER BY 登陆时间 DESC
        LIMIT 1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        if 结果:
            return {"success": True, "data": dict(结果[0])}
        else:
            return {"success": True, "data": None, "message": "无登录记录"}

    except Exception as e:
        错误日志器.error(f"获取用户最后登录详情失败: 用户id={用户id}, 错误={str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 删除用户(用户id: int) -> Dict[str, Any]:
    """删除用户 - 完全删除用户及其所有关联数据"""
    try:
        # 检查用户是否存在
        用户结果 = await 异步连接池实例.执行查询(
            "SELECT id, 昵称 FROM 用户表 WHERE id = $1", (用户id,)
        )
        if not 用户结果:
            return {"success": False, "message": "用户不存在"}

        用户信息 = 用户结果[0]
        用户昵称 = 用户信息.get("昵称", f"用户{用户id}")

        系统日志器.info(f"开始删除用户及其关联数据: 用户id={用户id}, 昵称={用户昵称}")

        # 删除用户（CASCADE会自动删除关联数据）
        删除SQL = "DELETE FROM 用户表 WHERE id = $1"
        影响行数 = await 异步连接池实例.执行更新(删除SQL, (用户id,))

        if 影响行数 > 0:
            安全日志器.info(f"用户删除成功: 用户id={用户id}, 昵称={用户昵称}")
            return {
                "success": True,
                "message": f"用户 {用户昵称} 及其所有关联数据已成功删除",
                "deleted_user_id": 用户id,
            }
        else:
            return {"success": False, "message": "删除用户失败，用户可能不存在"}

    except Exception as e:
        错误日志器.error(f"删除用户失败: 用户id={用户id}, 错误={e}", exc_info=True)
        return {"success": False, "message": f"删除用户时发生错误: {str(e)}"}


# ==================== 别名函数（向后兼容） ====================

# 为了保持向后兼容性，提供一些别名函数
异步创建用户 = 创建用户
异步获取用户_电话 = 根据手机号获取用户信息
异步获取用户_id = 根据用户id获取用户信息
异步用户是否存在_id = 用户是否存在_id
异步获取用户权限_id = 获取用户权限_id
异步获取JWT认证信息 = 获取JWT认证信息
异步更新用户密码 = 更新用户密码
异步获取用户列表 = 获取用户列表
异步获取用户总数 = 获取用户总数
异步获取今日新增用户数 = 获取今日新增用户数
异步增加用户邀约次数 = 增加用户邀约次数
异步更新用户算力值 = 更新用户算力值
异步用户是否存在_电话 = 用户是否存在_电话
异步用户是否已注册_电话 = 用户是否已注册_电话
异步获取用户含邀约上限 = 获取用户含邀约上限
异步获取用户今日成功邀约次数 = 获取用户今日成功邀约次数
异步获取用户昵称和邀约次数 = 获取用户昵称和邀约次数
异步更新用户昵称 = 更新用户昵称
异步获取用户密码哈希 = 获取用户密码哈希
异步记录邀约日志 = 记录邀约日志
异步添加用户 = 添加用户
异步获取用户信息 = 获取用户信息
异步删除用户 = 删除用户

# 管理员功能别名
# 这些别名将在函数定义后设置


# ==================== 管理员专用函数实现 ====================


async def 获取用户登录历史分页(
    用户id: int, 页码: int = 1, 每页数量: int = 20
) -> Dict[str, Any]:
    """获取用户登录历史（分页）"""
    try:
        计数SQL = "SELECT COUNT(*) as total FROM 用户登陆记录表 WHERE 用户id = $1"
        总数结果 = await 异步连接池实例.执行查询(计数SQL, (用户id,))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        偏移量 = (页码 - 1) * 每页数量
        查询SQL = """
        SELECT 登陆时间, ip地址, NULL as 设备信息, NULL as 浏览器信息, '成功' as 登录状态
        FROM 用户登陆记录表 WHERE 用户id = $1
        ORDER BY 登陆时间 DESC LIMIT $2 OFFSET $3
        """

        登录历史 = await 异步连接池实例.执行查询(查询SQL, (用户id, 每页数量, 偏移量))
        return {"success": True, "data": 登录历史 or [], "total": 总数}
    except Exception as e:
        错误日志器.error(f"获取用户登录历史失败: {str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户详细统计(用户id: int) -> Dict[str, Any]:
    """获取用户详细统计信息"""
    try:
        统计SQL = """
        SELECT
            (SELECT COUNT(*) FROM 接口日志表 WHERE 用户id = $1) as 接口调用次数,
            (SELECT COUNT(*) FROM 用户登陆记录表 WHERE 用户id = $1) as 登录次数,
            0 as 订单数量,
            0 as 消费金额
        """

        结果 = await 异步连接池实例.执行查询(统计SQL, (用户id,))
        统计数据 = dict(结果[0]) if 结果 else {}

        return {"success": True, "data": 统计数据}
    except Exception as e:
        错误日志器.error(f"获取用户详细统计失败: {str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户邀约统计(用户id: int) -> Dict[str, Any]:
    """获取用户邀约统计"""
    try:
        统计SQL = """
        SELECT
            COUNT(*) as 邀约总数,
            COUNT(CASE WHEN 状态 = '已接受' THEN 1 END) as 成功邀约数,
            COUNT(CASE WHEN DATE(创建时间) = CURRENT_DATE THEN 1 END) as 今日邀约数
        FROM 邀请记录表 WHERE 邀请人id = $1
        """

        结果 = await 异步连接池实例.执行查询(统计SQL, (用户id,))
        统计数据 = dict(结果[0]) if 结果 else {}

        return {"success": True, "data": 统计数据}
    except Exception as e:
        错误日志器.error(f"获取用户邀约统计失败: {str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户权限详情(用户id: int) -> Dict[str, Any]:
    """获取用户权限详情"""
    try:
        权限SQL = """
        SELECT DISTINCT p.id as 权限id, p.名称 as 权限名称, p.描述 as 权限描述, um.到期时间
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        JOIN 会员_权限_关联表 mp ON m.id = mp.会员id
        JOIN 权限表 p ON mp.权限id = p.id
        WHERE um.用户id = $1 AND (um.到期时间 IS NULL OR um.到期时间 > NOW())
        """

        权限列表 = await 异步连接池实例.执行查询(权限SQL, (用户id,))
        return {"success": True, "data": 权限列表 or []}
    except Exception as e:
        错误日志器.error(f"获取用户权限详情失败: {str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 获取用户安全审计(用户id: int) -> Dict[str, Any]:
    """获取用户安全审计信息"""
    try:
        审计SQL = """
        SELECT
            (SELECT COUNT(*) FROM 用户登陆记录表 WHERE 用户id = $1 AND 登陆时间 > NOW() - INTERVAL '7 days') as 近7天登录次数,
            (SELECT COUNT(DISTINCT ip地址) FROM 用户登陆记录表 WHERE 用户id = $1) as 使用IP数量,
            (SELECT 登陆时间 FROM 用户登陆记录表 WHERE 用户id = $1 ORDER BY 登陆时间 DESC LIMIT 1) as 最后登录时间
        """

        结果 = await 异步连接池实例.执行查询(审计SQL, (用户id,))
        审计数据 = dict(结果[0]) if 结果 else {}

        return {"success": True, "data": 审计数据}
    except Exception as e:
        错误日志器.error(f"获取用户安全审计失败: {str(e)}")
        return {"success": False, "message": f"查询失败: {str(e)}"}


async def 更新用户状态(用户id: int, 新状态: str) -> Dict[str, Any]:
    """更新用户状态"""
    try:
        更新SQL = (
            "UPDATE 用户表 SET 状态 = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2"
        )
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (新状态, 用户id))

        if 影响行数 > 0:
            return {"success": True, "message": "状态更新成功"}
        else:
            return {"success": False, "message": "用户不存在"}
    except Exception as e:
        错误日志器.error(f"更新用户状态失败: {str(e)}")
        return {"success": False, "message": f"更新失败: {str(e)}"}


async def 批量操作用户(
    用户id列表: List[int], 操作类型: str, 操作参数: Optional[Dict] = None
) -> Dict[str, Any]:
    """批量操作用户"""
    try:
        成功数量 = 0
        失败数量 = 0

        for 用户id in 用户id列表:
            try:
                if 操作类型 == "删除":
                    await 删除用户(用户id)
                elif 操作类型 == "更新状态":
                    状态 = 操作参数.get("状态", "正常") if 操作参数 else "正常"
                    await 更新用户状态(用户id, 状态)
                成功数量 += 1
            except Exception:
                失败数量 += 1

        return {"success": True, "成功数量": 成功数量, "失败数量": 失败数量}
    except Exception as e:
        错误日志器.error(f"批量操作用户失败: {str(e)}")
        return {"success": False, "message": f"操作失败: {str(e)}"}


async def 获取用户数据导出(用户id列表: Optional[List[int]] = None) -> Dict[str, Any]:
    """获取用户数据导出"""
    try:
        if 用户id列表:
            占位符 = ",".join(["$" + str(i + 1) for i in range(len(用户id列表))])
            查询SQL = f"""
            SELECT id, 昵称, 手机号, 邮箱, 状态, created_at, level
            FROM 用户表 WHERE id IN ({占位符})
            """
            用户数据 = await 异步连接池实例.执行查询(查询SQL, 用户id列表)
        else:
            查询SQL = "SELECT id, 昵称, 手机号, 邮箱, 状态, created_at, level FROM 用户表 LIMIT 1000"
            用户数据 = await 异步连接池实例.执行查询(查询SQL)

        return {"success": True, "data": 用户数据 or []}
    except Exception as e:
        错误日志器.error(f"获取用户数据导出失败: {str(e)}")
        return {"success": False, "message": f"导出失败: {str(e)}"}


async def 获取用户行为分析(用户id: int, 天数: int = 30) -> Dict[str, Any]:
    """获取用户行为分析"""
    try:
        分析SQL = """
        SELECT
            DATE(调用时间) as 日期,
            COUNT(*) as 接口调用次数,
            COUNT(DISTINCT 接口路径) as 使用功能数
        FROM 接口调用日志表
        WHERE 用户id = $1 AND 调用时间 > NOW() - INTERVAL $2
        GROUP BY DATE(调用时间)
        ORDER BY 日期 DESC
        """

        行为数据 = await 异步连接池实例.执行查询(分析SQL, (用户id, f"{天数} days"))
        return {"success": True, "data": 行为数据 or []}
    except Exception as e:
        错误日志器.error(f"获取用户行为分析失败: {str(e)}")
        return {"success": False, "message": f"分析失败: {str(e)}"}


async def 异步获取用户注册统计(时间范围: str = "7天") -> Dict[str, Any]:
    """获取用户注册统计"""
    try:
        if 时间范围 == "7天":
            天数 = 7
        elif 时间范围 == "30天":
            天数 = 30
        elif 时间范围 == "90天":
            天数 = 90
        else:
            天数 = 7

        统计SQL = f"""
        SELECT
            created_at::date as 注册日期,
            COUNT(*) as 注册数量
        FROM 用户表
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '{天数} days'
        GROUP BY created_at::date
        ORDER BY 注册日期 DESC
        """

        注册统计 = await 异步连接池实例.执行查询(统计SQL)

        # 计算总数
        总注册数SQL = f"""
        SELECT COUNT(*) as total
        FROM 用户表
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '{天数} days'
        """
        总数结果 = await 异步连接池实例.执行查询(总注册数SQL)
        总注册数 = 总数结果[0]["total"] if 总数结果 else 0

        return {
            "success": True,
            "data": {
                "daily_stats": 注册统计 or [],
                "total_registrations": 总注册数,
                "time_range": 时间范围,
            },
        }
    except Exception as e:
        错误日志器.error(f"获取用户注册统计失败: {str(e)}")
        return {"success": False, "message": f"统计失败: {str(e)}"}


# 兼容旧的函数名
Postgre_创建用户 = 创建用户
Postgre_创建半注册用户 = 创建半注册用户
Postgre_根据手机号获取用户信息 = 根据手机号获取用户信息
Postgre_根据用户id获取用户信息 = 根据用户id获取用户信息
Postgre_获取用户权限_id = 获取用户权限_id
Postgre_获取JWT认证信息 = 获取JWT认证信息
Postgre_更新用户密码 = 更新用户密码
Postgre_获取用户列表 = 获取用户列表
Postgre_获取用户总数 = 获取用户总数
Postgre_获取今日新增用户数 = 获取今日新增用户数
Postgre_增加用户邀约次数 = 增加用户邀约次数
Postgre_更新用户算力值 = 更新用户算力值
Postgre_用户是否存在_电话 = 用户是否存在_电话
Postgre_用户是否已注册_电话 = 用户是否已注册_电话
Postgre_获取用户含邀约上限 = 获取用户含邀约上限
Postgre_获取用户今日成功邀约次数 = 获取用户今日成功邀约次数
Postgre_获取用户昵称和邀约次数 = 获取用户昵称和邀约次数
Postgre_更新用户昵称 = 更新用户昵称
Postgre_获取用户密码哈希 = 获取用户密码哈希
Postgre_记录邀约日志 = 记录邀约日志

# 管理员功能别名 - 在函数定义后设置
异步获取用户关联店铺列表 = 获取用户关联店铺列表
异步获取用户接口调用历史分页 = 获取用户接口调用历史分页
异步获取用户最后登录详情 = 获取用户最后登录详情
异步获取用户登录历史分页 = 获取用户登录历史分页
异步获取用户详细统计 = 获取用户详细统计
异步获取用户邀约统计 = 获取用户邀约统计
异步获取用户权限详情 = 获取用户权限详情
异步获取用户安全审计 = 获取用户安全审计
异步更新用户状态 = 更新用户状态
异步批量操作用户 = 批量操作用户
异步获取用户数据导出 = 获取用户数据导出
异步获取用户行为分析 = 获取用户行为分析
