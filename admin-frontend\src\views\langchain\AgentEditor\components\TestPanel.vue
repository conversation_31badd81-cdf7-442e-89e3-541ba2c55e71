<template>
  <div :class="$style.rightSidebar">
    <!-- 测试面板头部 -->
    <div :class="$style.testHeader">
      <h3>智能体测试</h3>
      <a-switch 
        v-model:checked="realtimeTest"
        checked-children="实时" 
        un-checked-children="手动"
        size="small"
      />
    </div>

    <!-- 测试标签页 -->
    <a-tabs 
      v-model:activeKey="currentTestTab"
      :class="$style.testTabs"
      @change="handleTabChange"
    >
      <!-- 对话测试 -->
      <a-tab-pane key="chat" tab="💬 对话测试">
        <div :class="$style.chatTest">
          <!-- 对话消息列表 -->
          <div :class="$style.chatMessages">
            <div v-if="chatHistory.length === 0" style="text-align: center; color: #999; padding: 40px;">
              暂无对话记录，发送消息开始测试
            </div>
            <div 
              v-for="(msg, index) in chatHistory" 
              :key="index" 
              :class="[$style.chatMessage, $style[msg.role]]"
            >
              <div :class="$style.messageContent">{{ msg.content }}</div>
              <div :class="$style.messageTime">{{ msg.timestamp }}</div>
            </div>
          </div>

          <!-- 聊天输入区域 -->
          <div :class="$style.chatInput">
            <a-input
              v-model:value="testMessage"
              placeholder="输入测试消息..."
              @press-enter="sendTestMessage"
              :disabled="chatTesting"
            />
            <a-button 
              type="primary" 
              :loading="chatTesting"
              @click="sendTestMessage"
            >
              发送
            </a-button>
          </div>
        </div>
      </a-tab-pane>



      <!-- 工具测试 -->
      <a-tab-pane key="tools" tab="🔧 工具测试">
        <div :class="$style.toolsTest">
          <a-form layout="vertical">
            <a-form-item label="选择工具">
              <a-select
                v-model:value="selectedTool"
                placeholder="选择要测试的工具"
                :options="toolOptions"
              />
            </a-form-item>
            
            <a-form-item label="工具参数">
              <a-textarea
                v-model:value="toolParams"
                placeholder="输入工具参数 (JSON格式)..."
                :rows="4"
              />
            </a-form-item>
            
            <a-form-item>
              <a-button 
                type="primary" 
                :loading="toolTesting"
                @click="executeToolTest"
                block
              >
                执行工具测试
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 工具测试结果 -->
          <div v-if="toolResult" :class="[$style.testResult, toolResult.success ? $style.success : $style.error]">
            <h4>测试结果</h4>
            <pre>{{ JSON.stringify(toolResult.data, null, 2) }}</pre>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useAgentAPI } from '../composables/useAgentAPI'
import adminLangchainService from '@/services/adminLangchainService'

// 使用API
const { 测试智能体对话 } = useAgentAPI()

// Props
const props = defineProps({
  agentId: {
    type: [String, Number],
    default: null
  },
  enabledTools: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['test-message', 'tool-test'])

// 测试状态
const realtimeTest = ref(true)
const currentTestTab = ref('chat')

// 对话测试
const testMessage = ref('')
const chatTesting = ref(false)
const chatHistory = ref([])



// 工具测试
const selectedTool = ref('')
const toolParams = ref('')
const toolTesting = ref(false)
const toolResult = ref(null)

// 工具选项
const toolOptions = computed(() => {
  return props.enabledTools.map(tool => ({
    label: tool.显示名称,
    value: tool.工具名称
  }))
})

// 处理标签页切换
const handleTabChange = (key) => {
  currentTestTab.value = key
}

// 发送测试消息
const sendTestMessage = async () => {
  if (!testMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  if (!props.agentId) {
    message.warning('请先保存智能体后再进行测试')
    return
  }

  try {
    chatTesting.value = true

    // 添加用户消息到历史
    const userMessage = {
      role: 'user',
      content: testMessage.value,
      timestamp: new Date().toLocaleTimeString()
    }
    chatHistory.value.push(userMessage)

    // 调用真实API进行测试
    const response = await 测试智能体对话(props.agentId, testMessage.value, {
      保留历史: true,
      调试模式: false
    })

    // 添加助手回复到历史
    if (response) {
      const assistantMessage = {
        role: 'assistant',
        content: response.智能体回复 || response.回复内容 || response.content || '智能体回复异常',
        timestamp: new Date().toLocaleTimeString()
      }
      chatHistory.value.push(assistantMessage)
    }

    testMessage.value = ''

    // 触发父组件事件
    emit('test-message', {
      message: testMessage.value,
      agentId: props.agentId,
      response: response
    })

  } catch (error) {
    message.error('测试失败: ' + error.message)

    // 添加错误消息
    const errorMessage = {
      role: 'system',
      content: `测试失败: ${error.message}`,
      timestamp: new Date().toLocaleTimeString()
    }
    chatHistory.value.push(errorMessage)
  } finally {
    chatTesting.value = false
  }
}



// 执行工具测试
const executeToolTest = async () => {
  if (!selectedTool.value) {
    message.warning('请选择要测试的工具')
    return
  }

  try {
    toolTesting.value = true
    toolResult.value = null

    let params = {}
    if (toolParams.value.trim()) {
      try {
        params = JSON.parse(toolParams.value)
      } catch (parseError) {
        message.error('参数格式错误，请输入有效的JSON格式')
        return
      }
    }

    // 调用真实的工具测试API
    const response = await adminLangchainService.测试工具调用({
      工具名称: selectedTool.value,
      工具参数: params
    })

    if (response.success) {
      toolResult.value = {
        success: true,
        data: response.data
      }
    } else {
      toolResult.value = {
        success: false,
        error: response.message || '工具测试失败'
      }
    }

    // 触发父组件事件
    emit('tool-test', {
      toolName: selectedTool.value,
      params: params,
      agentId: props.agentId,
      result: toolResult.value
    })

  } catch (error) {
    toolResult.value = {
      success: false,
      message: error.message,
      timestamp: new Date().toLocaleTimeString()
    }
  } finally {
    toolTesting.value = false
  }
}

// 清空对话历史
const clearChatHistory = () => {
  chatHistory.value = []
}

// 导出方法供父组件使用
defineExpose({
  clearChatHistory
})
</script>

<style module>
/* 右侧测试区域 */
.rightSidebar {
  width: 350px;
  min-width: 350px;
  max-width: 350px;
  flex-shrink: 0;
  flex-grow: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 测试面板头部 */
.testHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.testHeader h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 测试标签页 */
.testTabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.testTabs :global(.ant-tabs-content-holder) {
  flex: 1;
  overflow: hidden;
}

.testTabs :global(.ant-tabs-tabpane) {
  height: 100%;
  overflow: hidden;
}

/* 对话测试 */
.chatTest {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chatMessages {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  margin-bottom: 16px;
  background: #fafafa;
}

.chatMessage {
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  max-width: 85%;
}

.chatMessage.user {
  background: #e6f7ff;
  margin-left: auto;
  text-align: right;
}

.chatMessage.assistant {
  background: #f6ffed;
  margin-right: auto;
}

.messageContent {
  word-wrap: break-word;
  white-space: pre-wrap;
}

.messageTime {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 聊天输入区域 */
.chatInput {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* RAG测试和工具测试 */
.ragTest,
.toolsTest {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.testResult {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
}

.testResult.success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.testResult.error {
  background: #fff2f0;
  border-color: #ffccc7;
}

.testResult h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.testResult pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 1000px) {
  .rightSidebar {
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
    max-height: 400px !important;
    order: 2 !important;
  }
  
  .chatMessages {
    max-height: 200px;
  }
  
  .ragTest,
  .toolsTest {
    padding: 12px;
  }
}
</style>
