<template>
  <div class="knowledge-base-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header
        :title="知识库详情?.知识库名称 || '知识库详情'"
        :sub-title="知识库详情?.知识库描述"
        @back="返回列表"
      >
        <template #extra>
          <a-space>
            <a-tag :color="getStatusColor(知识库详情?.知识库状态)">
              {{ getStatusText(知识库详情?.知识库状态) }}
            </a-tag>
            <a-tag :color="getVectorStatusColor(知识库详情?.知识库状态)">
              {{ getVectorStatusText(知识库详情?.知识库状态) }}
            </a-tag>
            <a-button type="primary" @click="保存知识库配置" :loading="保存中">
              <template #icon><SaveOutlined /></template>
              保存配置
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <a-spin :spinning="页面加载中">
      <div class="detail-content">
        <a-row :gutter="24">
          <!-- 左侧：基本信息和配置 -->
          <a-col :span="16">
            <a-card title="基本信息" class="info-card">
              <a-form :model="编辑表单" layout="vertical">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="知识库名称" name="知识库名称">
                      <a-input v-model:value="编辑表单.知识库名称" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="嵌入模型" name="嵌入模型">
                      <a-select
                        v-model:value="编辑表单.嵌入模型"
                        :loading="嵌入模型加载中"
                        placeholder="选择嵌入模型"
                        @focus="加载嵌入模型列表"
                      >
                        <a-select-option
                          v-for="model in 嵌入模型列表"
                          :key="model.id"
                          :value="model.id"
                        >
                          {{ model.显示名称 }} ({{ model.提供商 }})
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-form-item label="知识库描述" name="知识库描述">
                  <a-textarea v-model:value="编辑表单.知识库描述" :rows="3" />
                </a-form-item>

                <a-form-item label="配置信息" name="配置信息">
                  <a-textarea 
                    v-model:value="配置信息文本" 
                    :rows="8" 
                    placeholder="请输入JSON格式的配置信息"
                    @blur="验证配置信息"
                  />
                  <div v-if="配置验证错误" class="error-message">
                    {{ 配置验证错误 }}
                  </div>
                </a-form-item>
              </a-form>
            </a-card>

            <!-- 文档管理 -->
            <a-card title="文档管理" class="document-card">
              <template #extra>
                <a-space>
                  <a-button @click="刷新文档列表" size="small">
                    <template #icon><ReloadOutlined /></template>
                    刷新
                  </a-button>
                  <a-button @click="开启自动刷新" size="small" :type="自动刷新中 ? 'primary' : 'default'">
                    <template #icon><ReloadOutlined :spin="自动刷新中" /></template>
                    {{ 自动刷新中 ? '停止自动刷新' : '开启自动刷新' }}
                  </a-button>
                </a-space>
              </template>

              <!-- 拖放上传区域 -->
              <div class="upload-section">
                <a-upload-dragger
                  v-model:file-list="上传文件列表"
                  name="files"
                  multiple
                  :before-upload="() => false"
                  @change="处理文件选择"
                  @drop="处理文件拖放"
                  accept=".txt,.pdf,.docx,.doc,.md,.xlsx,.xls,.pptx,.ppt"
                  :show-upload-list="false"
                >
                  <p class="ant-upload-drag-icon">
                    <UploadOutlined style="font-size: 48px; color: #1890ff;" />
                  </p>
                  <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p class="ant-upload-hint">
                    支持单个或批量上传。支持格式：TXT、PDF、DOCX、DOC、MD、XLSX、XLS、PPTX、PPT
                  </p>
                </a-upload-dragger>

                <!-- 上传配置选项 -->
                <div class="upload-config-section">
                  <a-divider>
                    <span>上传配置</span>
                    <a-tag color="blue" style="margin-left: 8px">
                      使用嵌入模型: {{ getCurrentEmbeddingModelName() }}
                    </a-tag>
                  </a-divider>
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <a-form-item label="分块策略">
                        <div class="strategy-selector-container">
                          <!-- 智能推荐标签 -->
                          <div v-if="getRecommendedStrategy(上传文件列表)" class="recommended-tag">
                            <a-tag color="green" size="small">
                              <BulbOutlined /> 推荐：{{ getStrategyDisplayName(getRecommendedStrategy(上传文件列表)) }}
                            </a-tag>
                            <a-button
                              type="link"
                              size="small"
                              @click="() => applyRecommendedStrategy(上传文件列表, 上传配置, message)"
                            >
                              一键应用
                            </a-button>
                          </div>

                          <a-select
                            v-model:value="上传配置.chunk_strategy"
                            placeholder="选择分块策略"
                            @change="(strategy) => onStrategyChange(strategy, 上传配置, 上传文件列表)"
                            style="width: 100%;"
                          >
                            <a-select-option
                              v-for="strategy in getAvailableStrategies(上传文件列表)"
                              :key="strategy.value"
                              :value="strategy.value"
                            >
                              <div class="strategy-option-content">
                                <span>{{ strategy.icon }} {{ strategy.name }}</span>
                                <span v-if="strategy.recommended" class="recommended-badge">推荐</span>
                              </div>
                            </a-select-option>
                          </a-select>

                          <div class="strategy-description">
                            {{ getStrategyDescription(上传配置.chunk_strategy) }}
                          </div>
                        </div>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="分块大小">
                        <a-input-number
                          v-model:value="上传配置.chunk_size"
                          :min="getMinChunkSize()"
                          :max="8000"
                          :step="getChunkStep()"
                          placeholder="分块大小"
                          style="width: 100%"
                        />
                        <div class="config-hint">
                          <small>{{ getChunkSizeHint() }}</small>
                        </div>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="分块重叠">
                        <a-input-number
                          v-model:value="上传配置.chunk_overlap"
                          :min="0"
                          :max="800"
                          :step="100"
                          placeholder="分块重叠"
                          style="width: 100%"
                        />
                        <div class="config-hint">
                          <small>建议：分块大小的10-20%</small>
                        </div>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <!-- 智能配置建议 -->
                  <a-row style="margin-top: 16px">
                    <a-col :span="24">
                      <a-alert
                        :message="getConfigSuggestion()"
                        type="info"
                        show-icon
                        style="margin-bottom: 8px"
                      />
                      <div class="quick-config">
                        <span style="margin-right: 8px">快速配置：</span>
                        <a-button size="small" @click="setQuickConfig('small')" :type="getQuickConfigButtonType('small')">
                          {{ getQuickConfigLabel('small') }}
                        </a-button>
                        <a-button size="small" @click="setQuickConfig('medium')" style="margin-left: 8px" :type="getQuickConfigButtonType('medium')">
                          {{ getQuickConfigLabel('medium') }}
                        </a-button>
                        <a-button size="small" @click="setQuickConfig('large')" style="margin-left: 8px" :type="getQuickConfigButtonType('large')">
                          {{ getQuickConfigLabel('large') }}
                        </a-button>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>

              <!-- 文件列表和上传操作 -->
              <div v-if="上传文件列表.length > 0" class="upload-files-section">
                <a-divider>待上传文件 ({{ 上传文件列表.length }} 个)</a-divider>

                <div class="file-list">
                  <div v-for="(file, index) in 上传文件列表" :key="index" class="file-item">
                    <div class="file-info">
                      <div class="file-icon">
                        <FileTextOutlined :style="{ color: getFileTypeColor(getFileExtension(file.name)) }" />
                      </div>
                      <div class="file-details">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="file-meta">
                          <a-tag size="small" :color="getFileTypeColor(getFileExtension(file.name))">
                            {{ getFileExtension(file.name).toUpperCase() }}
                          </a-tag>
                          <span class="file-size">{{ formatFileSize(file.size) }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="file-actions">
                      <a-button type="text" size="small" danger @click="移除上传文件(file)">
                        <template #icon><DeleteOutlined /></template>
                      </a-button>
                    </div>
                  </div>
                </div>

                <!-- 上传进度显示 -->
                <div v-if="上传进度信息.显示" class="upload-progress-section">
                  <a-divider>上传进度</a-divider>
                  <div class="progress-container">
                    <div class="progress-header">
                      <span class="progress-title">正在处理文档...</span>
                      <span class="progress-status">{{ 上传进度信息.当前阶段 }}</span>
                    </div>
                    <a-progress
                      :percent="上传进度信息.百分比"
                      :status="上传进度信息.状态"
                      :show-info="true"
                      stroke-color="#1890ff"
                    />
                    <div class="progress-details">
                      <div class="progress-info">
                        <span>已处理: {{ 上传进度信息.当前文件 }}/{{ 上传进度信息.总文件数 }} 个文件</span>
                        <span v-if="上传进度信息.速度">上传速度: {{ 上传进度信息.速度 }}</span>
                      </div>
                      <div class="progress-time">
                        <span v-if="上传进度信息.预计剩余时间">预计剩余: {{ 上传进度信息.预计剩余时间 }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="upload-actions">
                  <a-space>
                    <a-button
                      type="primary"
                      @click="执行文档上传"
                      :loading="文档上传中"
                      :disabled="上传文件列表.length === 0"
                    >
                      <template #icon><UploadOutlined /></template>
                      上传 {{ 上传文件列表.length }} 个文件
                    </a-button>
                    <a-button @click="清空上传列表" :disabled="文档上传中">
                      <template #icon><ClearOutlined /></template>
                      清空列表
                    </a-button>
                    <a-button v-if="文档上传中" @click="取消上传" danger>
                      <template #icon><CloseOutlined /></template>
                      取消上传
                    </a-button>
                  </a-space>
                </div>
              </div>

              <a-table
                :columns="文档表格列"
                :data-source="文档列表"
                :loading="文档加载中"
                :pagination="文档分页"
                @change="处理文档表格变化"
                row-key="文档id"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="document-info">
                      <FileTextOutlined style="margin-right: 8px; color: #1890ff;" />
                      {{ record.文档名称 }}
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <div class="document-status">
                      <a-tag :color="getDocumentStatusColor(record.文档状态)">
                        {{ getDocumentStatusText(record.文档状态) }}
                      </a-tag>
                      <a-tag v-if="record.向量状态" :color="getVectorStatusColor(record.向量状态)" size="small" style="margin-left: 4px;">
                        {{ getVectorStatusText(record.向量状态) }}
                      </a-tag>
                    </div>
                  </template>

                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="预览文档(record)">预览</a-button>
                      <a-button type="link" size="small" danger @click="删除文档(record)">删除</a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </a-col>

          <!-- 右侧：统计信息和操作 -->
          <a-col :span="8">
            <a-card title="统计信息" class="stats-card">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="嵌入模型">
                  <a-tag color="blue">
                    {{ 知识库详情?.嵌入模型名称 || '未配置' }}
                    <span v-if="知识库详情?.嵌入模型提供商" style="font-size: 12px; color: #666; margin-left: 4px;">
                      ({{ 知识库详情.嵌入模型提供商 }})
                    </span>
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="创建者">
                  {{ 知识库详情?.创建者昵称 || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="文档数量">
                  {{ 知识库详情?.文档数量 || 0 }}
                </a-descriptions-item>
                <a-descriptions-item label="向量分块数">
                  {{ 知识库详情?.total_chunks || 0 }}
                </a-descriptions-item>
                <a-descriptions-item label="向量总数">
                  {{ 知识库详情?.total_vectors || 0 }}
                </a-descriptions-item>
                <a-descriptions-item label="存储类型">
                  {{ 知识库详情?.向量存储类型 || 'postgresql' }}
                </a-descriptions-item>
                <a-descriptions-item label="知识库ID">
                  {{ 知识库详情?.id || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="最后同步">
                  {{ 知识库详情?.last_sync_at ? new Date(知识库详情.last_sync_at).toLocaleString() : '未同步' }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ 知识库详情?.创建时间 ? new Date(知识库详情.创建时间).toLocaleString() : '-' }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>

            <a-card title="操作面板" class="actions-card">
              <a-space direction="vertical" style="width: 100%">
                <a-button
                  type="primary"
                  block
                  @click="向量化知识库"
                  :loading="向量化中"
                  :disabled="!文档列表.length"
                  title="重新向量化知识库中的所有文档"
                >
                  <template #icon><ThunderboltOutlined /></template>
                  批量向量化文档
                </a-button>

                <a-button block @click="显示检索测试">
                  <template #icon><SearchOutlined /></template>
                  检索测试
                </a-button>

                <a-button block @click="导出知识库配置">
                  <template #icon><ExportOutlined /></template>
                  导出配置
                </a-button>

                <a-popconfirm
                  title="确定要删除这个知识库吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="删除知识库"
                >
                  <a-button danger block>
                    <template #icon><DeleteOutlined /></template>
                    删除知识库
                  </a-button>
                </a-popconfirm>
              </a-space>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-spin>

    <!-- 检索测试对话框 -->
    <a-modal
      v-model:open="检索测试对话框可见"
      title="向量检索测试"
      width="800px"
      :footer="null"
    >
      <div class="retrieval-test">
        <a-form layout="vertical">
          <a-form-item label="检索查询">
            <a-input-search
              v-model:value="检索查询文本"
              placeholder="输入要检索的内容..."
              enter-button="检索"
              :loading="检索测试中"
              @search="执行检索测试"
            />
          </a-form-item>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大结果数">
                <a-input-number v-model:value="检索最大数量" :min="1" :max="20" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="相似度阈值">
                <a-input-number
                  v-model:value="相似度阈值"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  placeholder="0.0-1.0，0表示不过滤"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div v-if="检索结果.length > 0" class="search-results">
          <a-divider>检索结果 ({{ 检索结果.length }} 条)</a-divider>

          <!-- 检索统计信息 -->
          <div v-if="检索统计" class="search-stats" style="margin-bottom: 16px;">
            <a-space>
              <a-tag color="green">原始结果: {{ 检索统计.原始结果数量 }}</a-tag>
              <a-tag color="orange" v-if="检索统计.过滤掉的数量 > 0">
                过滤掉: {{ 检索统计.过滤掉的数量 }}
              </a-tag>
              <a-tag color="blue">最终结果: {{ 检索统计.最终结果数量 }}</a-tag>
              <a-tag color="purple" v-if="检索统计.相似度阈值 > 0">
                阈值: {{ 检索统计.相似度阈值 }}
              </a-tag>
            </a-space>
          </div>

          <div class="result-list">
            <div v-for="(result, index) in 检索结果" :key="index" class="result-item">
              <div class="result-header">
                <a-tag color="blue">相似度: {{ result.相似度分数 }}</a-tag>
                <a-tag color="gray" v-if="result.原始距离分数">
                  距离: {{ result.原始距离分数 }}
                </a-tag>
                <span class="document-name">{{ result.文档名称 }}</span>
              </div>
              <div class="result-content">{{ result.分块内容 }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {
    applyRecommendedStrategy,
    getAvailableStrategies,
    getFileSizeCategory,
    getFileSizeDescription,
    getRecommendedStrategy,
    getSmartQuickConfig,
    getStrategyDescription,
    getStrategyDisplayName,
    onStrategyChange,
    分块策略配置
} from '@/config/chunkStrategies'
import documentService from '@/services/documentService'
import knowledgeBaseService from '@/services/knowledgeBaseService'
import {
    BulbOutlined,
    ClearOutlined,
    CloseOutlined,
    DeleteOutlined,
    ExportOutlined,
    FileTextOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    ThunderboltOutlined,
    UploadOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, h, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const 页面加载中 = ref(true)
const 保存中 = ref(false)
const 向量化中 = ref(false)
const 文档加载中 = ref(false)
const 文档上传中 = ref(false)
const 检索测试中 = ref(false)
const 嵌入模型加载中 = ref(false)

const 知识库详情 = ref(null)
const 嵌入模型列表 = ref([])
const 文档列表 = ref([])
const 上传文件列表 = ref([])
const 检索结果 = ref([])

// 上传配置 (移除embedding_model，使用知识库配置的嵌入模型)
const 上传配置 = reactive({
  chunk_strategy: '智能递归分块',
  chunk_size: 2000,
  chunk_overlap: 400,
  auto_process: true
})

// 上传进度信息
const 上传进度信息 = reactive({
  显示: false,
  百分比: 0,
  状态: 'active',
  当前阶段: '准备上传',
  当前文件: 0,
  总文件数: 0,
  速度: '',
  预计剩余时间: ''
})

// 自动刷新相关
const 自动刷新中 = ref(false)
const 自动刷新定时器 = ref(null)

const 检索测试对话框可见 = ref(false)
const 检索查询文本 = ref('')
const 检索最大数量 = ref(5)
const 相似度阈值 = ref(0.5)
const 检索统计 = ref(null)

const 配置验证错误 = ref('')
const 配置信息文本 = ref('{}')

// 编辑表单
const 编辑表单 = reactive({
  知识库名称: '',
  知识库描述: '',
  嵌入模型: '',
  配置信息: {}
})

// 文档分页
const 文档分页 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 文档表格列
const 文档表格列 = [
  { title: '文档名称', key: 'name', width: 200 },
  { title: '类型', dataIndex: '文档类型', width: 80 },
  { title: '大小', dataIndex: '文档大小', width: 100, customRender: ({ text }) => formatFileSize(text) },
  { title: '状态', key: 'status', width: 160 },
  { title: '上传时间', dataIndex: '创建时间', width: 150, customRender: ({ text }) => new Date(text).toLocaleString() },
  { title: '操作', key: 'actions', width: 120 }
]

// 计算属性
const 知识库ID = computed(() => route.params.id)

// 工具方法
const getFileExtension = (filename) => {
  return filename.split('.').pop()?.toLowerCase() || 'unknown'
}

const getFileTypeColor = (fileType) => {
  const colorMap = {
    'txt': 'blue',
    'pdf': 'red',
    'docx': 'green',
    'doc': 'green',
    'md': 'purple',
    'xlsx': 'orange',
    'xls': 'orange',
    'pptx': 'cyan',
    'ppt': 'cyan',
    'unknown': 'default'
  }
  return colorMap[fileType?.toLowerCase()] || 'default'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 方法
const 返回列表 = () => {
  router.push('/langchain/knowledge-base')
}

const 加载知识库详情 = async () => {
  try {
    页面加载中.value = true

    if (!知识库ID.value) {
      message.error('知识库ID不存在，请检查URL参数')
      return
    }

    const 响应 = await knowledgeBaseService.getKnowledgeBaseDetail(知识库ID.value)

    if (响应.success) {
      知识库详情.value = 响应.data
      // 填充编辑表单
      编辑表单.知识库名称 = 知识库详情.value.知识库名称
      编辑表单.知识库描述 = 知识库详情.value.知识库描述
      编辑表单.嵌入模型 = Number(知识库详情.value.嵌入模型)
      编辑表单.配置信息 = 知识库详情.value.配置信息 || {}
      配置信息文本.value = JSON.stringify(编辑表单.配置信息, null, 2)
    } else {
      message.error(响应.error || '加载知识库详情失败')
    }
  } catch (error) {
    message.error('加载知识库详情失败')
  } finally {
    页面加载中.value = false
  }
}



const 加载文档列表 = async () => {
  try {
    文档加载中.value = true

    if (!知识库ID.value) {
      return
    }

    const 响应 = await knowledgeBaseService.getDocumentList(知识库ID.value, {
      页码: 文档分页.current,
      每页数量: 文档分页.pageSize
    })

    if (响应.success) {
      文档列表.value = 响应.data.文档列表 || []
      文档分页.total = 响应.data.总数量 || 0
    }
  } catch (error) {
    message.error('加载文档列表失败')
  } finally {
    文档加载中.value = false
  }
}

const 保存知识库配置 = async () => {
  try {
    保存中.value = true

    const 更新数据 = {
      知识库名称: 编辑表单.知识库名称,
      知识库描述: 编辑表单.知识库描述,
      嵌入模型: 编辑表单.嵌入模型,
      配置信息: 编辑表单.配置信息
    }

    const 响应 = await knowledgeBaseService.updateKnowledgeBase(知识库ID.value, 更新数据)

    if (响应.success) {
      message.success('保存成功')
      await 加载知识库详情()
    } else {
      message.error(响应.error || '保存失败')
    }
  } catch (error) {
    message.error('保存失败')
  } finally {
    保存中.value = false
  }
}

const 验证配置信息 = () => {
  try {
    编辑表单.配置信息 = JSON.parse(配置信息文本.value)
    配置验证错误.value = ''
  } catch (error) {
    配置验证错误.value = 'JSON格式错误: ' + error.message
  }
}

const 向量化知识库 = async () => {
  try {
    向量化中.value = true

    // 确认操作
    const 确认结果 = await new Promise((resolve) => {
      Modal.confirm({
        title: '批量向量化文档',
        content: `确定要重新向量化知识库中的所有 ${文档列表.value.length} 个文档吗？这将重新生成所有文档的向量数据。`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      })
    })

    if (!确认结果) {
      向量化中.value = false
      return
    }

    const 响应 = await knowledgeBaseService.vectorizeKnowledgeBase(知识库ID.value)

    if (响应.success) {
      message.success('批量向量化任务已启动，正在处理所有文档...')
      setTimeout(() => 加载知识库详情(), 2000)
    } else {
      message.error(响应.error || '批量向量化失败')
    }
  } catch (error) {
    message.error('批量向量化失败')
  } finally {
    向量化中.value = false
  }
}

const 处理文件选择 = (info) => {
  console.log('文件选择变化:', info)

  // 过滤支持的文件类型
  const 支持的文件类型 = ['txt', 'pdf', 'docx', 'doc', 'md', 'xlsx', 'xls', 'pptx', 'ppt']
  const 有效文件 = info.fileList.filter(file => {
    const 文件扩展名 = getFileExtension(file.name)
    const 是否支持 = 支持的文件类型.includes(文件扩展名)

    if (!是否支持) {
      message.warning(`不支持的文件格式: ${file.name}`)
      return false
    }

    // 检查文件大小 (限制为50MB)
    const 最大文件大小 = 50 * 1024 * 1024 // 50MB
    if (file.size > 最大文件大小) {
      message.warning(`文件过大: ${file.name} (最大支持50MB)`)
      return false
    }

    return true
  })

  上传文件列表.value = 有效文件

  if (有效文件.length !== info.fileList.length) {
    const 过滤数量 = info.fileList.length - 有效文件.length
    message.info(`已过滤 ${过滤数量} 个不支持的文件`)
  }
}

const 处理文件拖放 = (e) => {
  console.log('文件拖放事件:', e)
  // 拖放事件由 a-upload-dragger 自动处理
}

const 移除上传文件 = (file) => {
  const index = 上传文件列表.value.findIndex(item =>
    item.uid === file.uid || item.name === file.name
  )
  if (index > -1) {
    上传文件列表.value.splice(index, 1)
    console.log(`已移除文件: ${file.name}`)
  }
}

const 清空上传列表 = () => {
  上传文件列表.value = []
  上传进度信息.显示 = false
}

// 配置相关方法
const getConfigSuggestion = () => {
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)
  const sizeInMB = totalSize / (1024 * 1024)

  if (sizeInMB < 0.1) {
    return '小文件建议：分块大小 1000-1500，重叠 200-300'
  } else if (sizeInMB < 1) {
    return '中等文件建议：分块大小 2000-3000，重叠 400-600'
  } else {
    return '大文件建议：分块大小 3000-4000，重叠 600-800，使用递归分块策略'
  }
}

const setQuickConfig = (type) => {
  // 获取当前文件总大小
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)
  const currentStrategy = 上传配置.chunk_strategy

  // 根据文件大小和当前策略智能配置
  let config

  if (totalSize > 0) {
    // 基于实际文件大小的智能配置
    config = getSmartQuickConfig(totalSize, currentStrategy)
  } else {
    // 基于预设类型的通用配置
    const 策略配置 = 分块策略配置[currentStrategy] || 分块策略配置['智能递归分块']

    switch (type) {
      case 'small':
        config = {
          chunk_size: Math.max(策略配置.minSize, 500),
          chunk_overlap: 100,
          description: '小文件配置'
        }
        break
      case 'medium':
        config = {
          chunk_size: 策略配置.recommendedSize,
          chunk_overlap: Math.min(策略配置.recommendedSize * 0.2, 200),
          description: '中等文件配置'
        }
        break
      case 'large':
        config = {
          chunk_size: Math.min(策略配置.recommendedSize * 1.5, 策略配置.maxSize),
          chunk_overlap: Math.min(策略配置.recommendedSize * 0.3, 400),
          description: '大文件配置'
        }
        break
      default:
        config = {
          chunk_size: 策略配置.recommendedSize,
          chunk_overlap: Math.min(策略配置.recommendedSize * 0.2, 200),
          description: '默认配置'
        }
    }
  }

  if (config) {
    上传配置.chunk_size = config.chunk_size
    上传配置.chunk_overlap = config.chunk_overlap

    const typeLabel = type === 'small' ? '小' : type === 'medium' ? '中等' : '大'
    message.success(`已应用${typeLabel}文件配置：${config.description}`)
  }
}

// 获取当前嵌入模型名称
const getCurrentEmbeddingModelName = () => {
  // 优先使用后端返回的嵌入模型名称
  if (知识库详情.value?.嵌入模型名称) {
    const 提供商 = 知识库详情.value.嵌入模型提供商
    return 提供商 ? `${知识库详情.value.嵌入模型名称} (${提供商})` : 知识库详情.value.嵌入模型名称
  }

  // 如果后端没有返回名称，通过ID在模型列表中查找
  const 嵌入模型id = 知识库详情.value?.嵌入模型
  const 找到的模型 = 嵌入模型列表.value.find(model => model.id === 嵌入模型id)
  return 找到的模型 ? `${找到的模型.显示名称} (${找到的模型.提供商})` : '未配置'
}

// 加载嵌入模型列表
const 加载嵌入模型列表 = async () => {
  if (嵌入模型列表.value.length > 0) return // 已加载过

  try {
    嵌入模型加载中.value = true
    const 响应 = await knowledgeBaseService.getEmbeddingModels()

    if (响应.success) {
      嵌入模型列表.value = 响应.data.map(model => ({
        id: Number(model.id),
        显示名称: model.模型名称 || `${model.提供商}-${model.模型类型}`,
        提供商: model.提供商,
        状态: model.启用状态,
        模型名称: model.模型名称
      })).filter(model => model.状态 === 1)
    } else {
      message.error('加载嵌入模型列表失败: ' + 响应.error)
    }
  } catch (error) {
    message.error('加载嵌入模型列表失败')
  } finally {
    嵌入模型加载中.value = false
  }
}

const 执行文档上传 = async () => {
  if (上传文件列表.value.length === 0) {
    message.warning('请选择要上传的文件')
    return
  }

  // 验证知识库是否配置了嵌入模型
  if (!知识库详情.value?.嵌入模型) {
    message.warning('请先在基本信息中配置嵌入模型')
    return
  }

  try {
    文档上传中.value = true

    // 初始化进度信息
    Object.assign(上传进度信息, {
      显示: true,
      百分比: 10,
      状态: 'active',
      当前阶段: '上传文件中',
      当前文件: 0,
      总文件数: 上传文件列表.value.length,
      速度: '',
      预计剩余时间: ''
    })

    const files = 上传文件列表.value.map(item => item.originFileObj || item)
    console.log('🚀 开始上传文档:', files.length, '个文件', '配置:', 上传配置)

    const 开始时间 = Date.now()

    const 响应 = await knowledgeBaseService.uploadDocuments(知识库ID.value, files, {
      ...上传配置,
      embedding_model: 知识库详情.value.嵌入模型,
      onProgress: (progress) => {
        // 计算进度百分比 (10% - 90% 为上传阶段)
        const 进度百分比 = Math.min(90, 10 + (progress.loaded / progress.total * 80))
        const 当前阶段 = 进度百分比 < 30 ? '上传文件中' :
                      进度百分比 < 60 ? '解析文档内容' : '分块处理中'

        // 批量更新进度信息
        Object.assign(上传进度信息, {
          百分比: Math.round(进度百分比),
          当前文件: Math.ceil(progress.loaded / progress.total * 上传进度信息.总文件数),
          当前阶段,
          速度: progress.rate ? `${(progress.rate / 1024 / 1024).toFixed(2)} MB/s` : '',
          预计剩余时间: progress.rate && progress.total > progress.loaded ?
            `${Math.round((progress.total - progress.loaded) / progress.rate)}秒` : ''
        })
      }
    })

    // 更新进度：处理完成
    上传进度信息.当前阶段 = '向量化处理中'
    上传进度信息.百分比 = 95

    if (响应.success) {
      // 只有在接口成功返回时才显示100%
      上传进度信息.百分比 = 100
      上传进度信息.状态 = 'success'
      上传进度信息.当前阶段 = '处理完成'

      const 结果 = 响应.data
      const 处理时间 = ((Date.now() - 开始时间) / 1000).toFixed(1)

      message.success(`文档上传成功：${结果.成功数量} 个文件已上传并完成向量化 (耗时 ${处理时间}s)`)

      // 显示详细结果
      if (结果.失败数量 > 0) {
        message.warning(`${结果.失败数量} 个文件上传失败`)
      }

      上传文件列表.value = []

      // 延迟隐藏进度条，让用户看到完成状态
      setTimeout(() => {
        上传进度信息.显示 = false
      }, 2000)

      await 加载文档列表()
      await 加载知识库详情()

    } else {
      上传进度信息.状态 = 'exception'
      上传进度信息.当前阶段 = '上传失败'
      message.error(响应.error || '文档上传失败')

      // 失败时也延迟隐藏进度条
      setTimeout(() => {
        上传进度信息.显示 = false
      }, 3000)
    }
  } catch (error) {
    console.error('❌ 文档上传异常:', error)
    上传进度信息.状态 = 'exception'
    上传进度信息.当前阶段 = '上传异常'
    message.error('文档上传失败: ' + error.message)

    setTimeout(() => {
      上传进度信息.显示 = false
    }, 3000)
  } finally {
    文档上传中.value = false
  }
}

// 取消上传
const 取消上传 = () => {
  文档上传中.value = false
  Object.assign(上传进度信息, {
    显示: false,
    状态: 'exception',
    当前阶段: '已取消'
  })
  message.info('上传已取消')
}

const 刷新文档列表 = () => {
  加载文档列表()
}

const 开启自动刷新 = () => {
  if (自动刷新中.value) {
    // 停止自动刷新
    if (自动刷新定时器.value) {
      clearInterval(自动刷新定时器.value)
      自动刷新定时器.value = null
    }
    自动刷新中.value = false
    message.info('已停止自动刷新')
  } else {
    // 开启自动刷新
    自动刷新中.value = true
    自动刷新定时器.value = setInterval(() => {
      console.log('🔄 自动刷新文档列表')
      加载文档列表()
    }, 10000) // 每10秒刷新一次
    message.info('已开启自动刷新，每10秒更新一次')
  }
}

const 处理文档表格变化 = (pagination) => {
  文档分页.current = pagination.current
  文档分页.pageSize = pagination.pageSize
  加载文档列表()
}

const 预览文档 = async (record) => {
  try {
    console.log('👁️ 预览文档:', record)

    const 文档ID = record.文档id
    if (!文档ID) {
      message.error('文档ID不存在，无法预览')
      return
    }

    // 调用文档预览接口
    const 响应 = await documentService.previewDocument(文档ID, {
      最大长度: 5000,
      包含元数据: true
    })

    if (响应.success) {
      // 显示预览内容
      const 预览数据 = 响应.data
      Modal.info({
        title: `预览文档: ${预览数据.文档名称}`,
        content: h('div', [
          h('p', `文件类型: ${预览数据.文档类型}`),
          h('p', `文件大小: ${预览数据.文档大小} 字节`),
          h('p', `内容长度: ${预览数据.内容长度} 字符`),
          预览数据.是否截断 ? h('p', { style: 'color: orange' }, '内容已截断，仅显示前5000字符') : null,
          h('div', { style: 'margin-top: 16px' }, [
            h('strong', '文档内容:'),
            h('pre', {
              style: 'background: #f5f5f5; padding: 12px; border-radius: 4px; max-height: 400px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word;'
            }, 预览数据.预览内容)
          ])
        ]),
        width: 800,
        okText: '关闭'
      })
    } else {
      message.error(响应.error || '预览文档失败')
    }
  } catch (error) {
    console.error('❌ 预览文档异常:', error)
    message.error('预览文档失败')
  }
}

const 删除文档 = async (record) => {
  try {
    console.log('🗑️ 删除文档:', record)

    // 使用正确的字段名，后端返回的是 文档id (实际是文档uuid)
    const 文档ID = record.文档id
    if (!文档ID) {
      message.error('文档ID不存在，无法删除')
      return
    }

    const 响应 = await knowledgeBaseService.deleteDocument(文档ID)
    if (响应.success) {
      message.success('删除成功')
      await 加载文档列表()
      await 加载知识库详情()
    } else {
      message.error(响应.error || '删除失败')
    }
  } catch (error) {
    console.error('❌ 删除文档异常:', error)
    message.error('删除失败')
  }
}

const 显示检索测试 = () => {
  检索测试对话框可见.value = true
  // 清空之前的检索结果和统计
  检索结果.value = []
  检索统计.value = null
}

const 执行检索测试 = async () => {
  if (!检索查询文本.value.trim()) {
    message.warning('请输入检索内容')
    return
  }

  try {
    检索测试中.value = true
    const 响应 = await knowledgeBaseService.vectorSearch(知识库ID.value, {
      查询文本: 检索查询文本.value,
      最大数量: 检索最大数量.value,
      检索方式: 'postgresql', // PostgreSQL向量存储
      相似度阈值: 相似度阈值.value
    })

    if (响应.success) {
      检索结果.value = 响应.data.检索结果 || []
      检索统计.value = 响应.data.检索统计 || null

      if (检索结果.value.length === 0) {
        if (检索统计.value && 检索统计.value.过滤掉的数量 > 0) {
          message.info(`未找到相关内容，${检索统计.value.过滤掉的数量} 条结果被相似度阈值过滤`)
        } else {
          message.info('未找到相关内容')
        }
      } else {
        let 消息 = `找到 ${检索结果.value.length} 条相关内容`
        if (检索统计.value && 检索统计.value.过滤掉的数量 > 0) {
          消息 += `，过滤掉 ${检索统计.value.过滤掉的数量} 条低相似度结果`
        }
        message.success(消息)
      }
    } else {
      message.error(响应.error || '检索失败')
    }
  } catch (error) {
    message.error('检索失败')
  } finally {
    检索测试中.value = false
  }
}

const 导出知识库配置 = () => {
  const 配置数据 = {
    知识库信息: 知识库详情.value,
    配置信息: 编辑表单.配置信息
  }

  const blob = new Blob([JSON.stringify(配置数据, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `知识库配置_${知识库详情.value?.知识库名称}_${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const 删除知识库 = async () => {
  try {
    const 响应 = await knowledgeBaseService.deleteKnowledgeBase(知识库ID.value)
    if (响应.success) {
      message.success('删除成功')
      返回列表()
    } else {
      message.error(响应.error || '删除失败')
    }
  } catch (error) {
    message.error('删除失败')
  }
}



// 🔧 新增：根据文件大小智能计算分块参数
const getMinChunkSize = () => {
  // 获取当前选中文件的总大小
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)

  if (totalSize === 0) return 50 // 没有文件时的默认最小值

  // 根据文件大小智能调整最小分块大小
  if (totalSize < 1024) return 20        // 小于1KB：最小20字符
  if (totalSize < 5 * 1024) return 50    // 小于5KB：最小50字符
  if (totalSize < 50 * 1024) return 100  // 小于50KB：最小100字符
  if (totalSize < 500 * 1024) return 200 // 小于500KB：最小200字符
  return 500                             // 大文件：最小500字符
}

const getChunkStep = () => {
  const minSize = getMinChunkSize()
  return minSize < 100 ? 10 : (minSize < 500 ? 50 : 100)
}

const getChunkSizeHint = () => {
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)

  if (totalSize === 0) return '建议：根据文件大小自动调整'

  const sizeKB = totalSize / 1024
  if (sizeKB < 1) return '小文件：建议20-100字符分块'
  if (sizeKB < 5) return '小文件：建议50-200字符分块'
  if (sizeKB < 50) return '中等文件：建议100-500字符分块'
  if (sizeKB < 500) return '中等文件：建议200-1000字符分块'
  return '大文件：建议500-4000字符分块'
}

// ==================== 分块策略智能推荐系统 ====================
// 注意：分块策略配置已从 @/config/chunkStrategies 导入

// 注意：以下函数已从 @/config/chunkStrategies 导入：
// - getRecommendedStrategy
// - getAvailableStrategies
// - getStrategyDisplayName
// - applyRecommendedStrategy
// - onStrategyChange

const getStatusColor = (status) => {
  const colorMap = {
    // 英文状态值
    'active': 'green',
    'inactive': 'default',
    'processing': 'orange',
    'error': 'red',
    'building': 'blue',
    // 中文状态值
    '活跃': 'green',
    '未激活': 'default',
    '处理中': 'orange',
    '错误': 'red',
    '构建中': 'blue'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    // 英文状态值
    'active': '活跃',
    'inactive': '未激活',
    'processing': '处理中',
    'error': '错误',
    'building': '构建中',
    // 中文状态值（直接返回）
    '活跃': '活跃',
    '未激活': '未激活',
    '处理中': '处理中',
    '错误': '错误',
    '构建中': '构建中'
  }
  return textMap[status] || status || '未知'
}

const getVectorStatusColor = (status) => {
  const colorMap = {
    // 英文状态值
    'active': 'green',
    'inactive': 'default',
    'error': 'red',
    // 中文状态值
    '活跃': 'green',
    '未激活': 'default',
    '错误': 'red',
    '正常': 'green',
    '待处理': 'default',
    '处理中': 'processing',
    '已完成': 'success',
    '失败': 'error',
    '基本完成': 'warning',
    '部分失败': 'warning'
  }
  return colorMap[status] || 'default'
}

const getVectorStatusText = (status) => {
  const textMap = {
    // 英文状态值
    'active': '正常',
    'inactive': '未激活',
    'error': '错误',
    // 中文状态值（直接返回）
    '活跃': '正常',
    '未激活': '未激活',
    '错误': '错误',
    '正常': '正常',
    '待处理': '待向量化',
    '处理中': '向量化中',
    '已完成': '已向量化',
    '失败': '向量化失败',
    '基本完成': '基本完成',
    '部分失败': '部分失败'
  }
  return textMap[status] || status || '未知'
}

const getDocumentStatusColor = (status) => {
  const colorMap = {
    // 支持中文状态值
    '已处理': 'green',
    '已完成': 'green',
    '处理中': 'orange',
    '失败': 'red',
    '上传中': 'blue',
    // 兼容英文状态值
    'processed': 'green',
    'completed': 'green',
    'processing': 'orange',
    'failed': 'red',
    'uploading': 'blue'
  }
  return colorMap[status] || 'default'
}

const getDocumentStatusText = (status) => {
  const textMap = {
    // 支持中文状态值（直接显示）
    '已处理': '已处理',
    '已完成': '已完成',
    '处理中': '处理中',
    '失败': '失败',
    '上传中': '上传中',
    // 兼容英文状态值（转换为中文）
    'processed': '已处理',
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'uploading': '上传中'
  }
  return textMap[status] || '未知'
}





// 生命周期
onMounted(async () => {
  // 先加载嵌入模型列表，确保下拉框能正确显示
  await 加载嵌入模型列表()

  // 然后并行加载其他数据
  await Promise.all([
    加载知识库详情(),
    加载文档列表()
  ])
})

onUnmounted(() => {
  // 清理自动刷新定时器
  if (自动刷新定时器.value) {
    clearInterval(自动刷新定时器.value)
    自动刷新定时器.value = null
  }
})

// 监听配置信息变化
watch(配置信息文本, () => {
  验证配置信息()
})

// 获取快速配置按钮标签
const getQuickConfigLabel = (type) => {
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)
  const currentStrategy = 上传配置.chunk_strategy

  if (totalSize > 0) {
    // 基于实际文件大小显示
    const category = getFileSizeCategory(totalSize, currentStrategy)
    const sizeDescription = getFileSizeDescription(totalSize, currentStrategy)

    switch (type) {
      case 'small': return category === 'small' ? `${sizeDescription} (当前)` : '小文件配置'
      case 'medium': return category === 'medium' ? `${sizeDescription} (当前)` : '中等文件配置'
      case 'large': return category === 'large' ? `${sizeDescription} (当前)` : '大文件配置'
    }
  }

  // 默认标签
  switch (type) {
    case 'small': return '小文件 (< 100KB)'
    case 'medium': return '中等文件 (100KB - 1MB)'
    case 'large': return '大文件 (> 1MB)'
    default: return '配置'
  }
}

// 获取快速配置按钮类型
const getQuickConfigButtonType = (type) => {
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)
  const currentStrategy = 上传配置.chunk_strategy

  if (totalSize > 0) {
    const category = getFileSizeCategory(totalSize, currentStrategy)
    return category === type ? 'primary' : 'default'
  }

  return 'default'
}
</script>

<style scoped>
.knowledge-base-detail {
  padding: 0;
}

.page-header {
  background: white;
  margin-bottom: 16px;
}

.detail-content {
  padding: 0 24px;
}

.info-card,
.document-card,
.stats-card,
.actions-card {
  margin-bottom: 16px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.upload-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.upload-config-section {
  margin-top: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.config-hint {
  margin-top: 4px;
}

.config-hint small {
  color: #8c8c8c;
  font-size: 11px;
}

.quick-config {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.upload-progress-section {
  margin: 24px 0;
  padding: 16px;
  background: #f0f8ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
}

.progress-container {
  width: 100%;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-weight: 500;
  color: #262626;
}

.progress-status {
  color: #1890ff;
  font-size: 12px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.progress-info {
  display: flex;
  gap: 16px;
}

.progress-time {
  text-align: right;
}

.upload-actions {
  margin-top: 12px;
  text-align: right;
}

.upload-actions .ant-btn {
  margin-left: 8px;
}

.document-info {
  display: flex;
  align-items: center;
}

.document-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.document-status .ant-tag {
  margin: 0;
}

.search-results {
  margin-top: 16px;
}

.result-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-content {
  color: #333;
  line-height: 1.6;
  background: white;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.document-name {
  color: #666;
  font-size: 12px;
}

.upload-section {
  margin-bottom: 24px;
}

.upload-files-section {
  margin-top: 16px;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 8px;
  background: #fafafa;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 12px;
  font-size: 20px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-size {
  color: #8c8c8c;
  font-size: 12px;
}

.file-actions {
  margin-left: 12px;
}

.upload-actions {
  margin-top: 16px;
  text-align: center;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.ant-upload-dragger {
  border: 2px dashed #d9d9d9 !important;
  border-radius: 6px !important;
  background: #fafafa !important;
  transition: all 0.3s !important;
}

.ant-upload-dragger:hover {
  border-color: #1890ff !important;
  background: #f0f8ff !important;
}

.ant-upload-dragger.ant-upload-drag-hover {
  border-color: #1890ff !important;
  background: #e6f7ff !important;
}

.ant-upload-drag-icon {
  margin-bottom: 16px !important;
}

.ant-upload-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #262626 !important;
  margin-bottom: 8px !important;
}

.ant-upload-hint {
  color: #8c8c8c !important;
  font-size: 14px !important;
}

/* 分块策略选择器样式 */
.strategy-selector-container {
  width: 100%;
}

.recommended-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 10px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 12px;
}

.strategy-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.recommended-badge {
  background: #52c41a;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.strategy-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
  height: 32px; /* 固定高度，确保对齐 */
  display: flex;
  align-items: center;
}
</style>
