<template>
  <div class="product-management-module">
    <!-- 产品概览统计 -->
    <div class="overview-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="stat in productStats" :key="stat.key">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              :title="stat.title"
              :value="stat.value"
              :suffix="stat.suffix"
              :prefix="stat.icon ? h(stat.icon) : null"
              :value-style="{ color: stat.color }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 产品管理主区域 -->
    <a-card class="product-card" title="产品管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAddProduct">
            <plus-outlined />
            添加产品
          </a-button>
          <a-button @click="exportProducts">
            <download-outlined />
            导出
          </a-button>
          <a-button @click="refreshProducts">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索和筛选区域 -->
      <div class="filter-section">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8">
            <a-input-search
              v-model:value="searchText"
              placeholder="搜索产品名称"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-select
              v-model:value="filterCategory"
              placeholder="产品分类"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-select
              v-model:value="filterStatus"
              placeholder="产品状态"
              style="width: 100%"
              @change="handleFilter"
              allow-clear
            >
              <a-select-option value="1">正常</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="4">
            <a-button type="default" @click="resetFilters" block>
              重置筛选
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 产品列表 -->
      <a-table
        :columns="columns"
        :data-source="productList"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
        class="product-table"
      >
        <!-- 产品名称列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'productName'">
            <div class="product-name-cell">
              <strong>{{ record.产品名称 }}</strong>
              <div class="product-description">{{ record.产品描述 || '暂无描述' }}</div>
            </div>
          </template>
          
          <!-- 分类列 -->
          <template v-else-if="column.key === 'category'">
            <a-tag color="blue" v-if="record.产品分类">
              {{ record.产品分类 }}
            </a-tag>
            <span v-else class="text-gray">未分类</span>
          </template>

          <!-- 知识库状态列 -->
          <template v-else-if="column.key === 'knowledgeStatus'">
            <div class="knowledge-status-cell">
              <a-tag
                :color="record.知识库状态?.已提交 ? 'green' : 'orange'"
                style="margin-bottom: 4px;"
              >
                {{ record.知识库状态?.已提交 ? '已提交' : '未提交' }}
              </a-tag>
              <div v-if="record.知识库状态?.已提交" class="knowledge-details">
                <div class="text-small">
                  {{ record.知识库状态.文档数量 }}个文档
                </div>
                <div class="text-small text-gray" v-if="record.知识库状态.最新提交时间">
                  {{ formatDate(record.知识库状态.最新提交时间) }}
                </div>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.状态 === 1 ? 'green' : 'red'">
              {{ record.状态 === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createTime'">
            {{ formatDate(record.创建时间) }}
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewProduct(record)">
                <eye-outlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="editProduct(record)">
                <edit-outlined />
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="submitToKnowledge(record)"
                :title="record.知识库状态?.已提交 ? '重新提交到知识库' : '提交到知识库'"
              >
                <cloud-upload-outlined />
                {{ record.知识库状态?.已提交 ? '重新提交' : '提交' }}
              </a-button>
              <a-popconfirm
                title="确定要删除这个产品吗？"
                @confirm="删除产品(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" size="small" danger>
                  <delete-outlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑产品弹窗 -->
    <a-modal
      v-model:open="showAddModal"
      :title="editingProduct ? '编辑产品' : '添加产品'"
      width="1000px"
      @cancel="handleCancel"
      :body-style="{ padding: '20px' }"
      :footer="null"
    >
      <div class="product-modal-content">
        <!-- Tab页切换 -->
        <a-tabs v-model:activeKey="activeTab" style="margin-bottom: 24px;">
          <a-tab-pane key="smart" tab="智能识别">
            <!-- 智能识别Tab内容 -->
            <!-- 步骤指示器 -->
            <a-steps
              :current="currentStep"
              size="small"
              style="margin-bottom: 24px;"
              :items="[
                { title: '智能解析', description: '输入产品信息' },
                { title: '信息确认', description: '检查和编辑' },
                { title: '完成保存', description: '保存产品' }
              ]"
            />

            <!-- 第一步：智能解析 -->
            <div v-show="currentStep === 0" class="step-content">
          <a-card title="智能产品解析" size="small">
            <template #extra>
              <a-tooltip title="AI将自动解析产品信息">
                <question-circle-outlined />
              </a-tooltip>
            </template>

            <!-- 统一的输入界面 -->
            <div class="input-section">
              <!-- 文本输入区域 -->
              <div class="text-input-area">
                <div class="input-header">
                  <h4><edit-outlined /> 文本输入</h4>
                  <a-space size="small">
                    <a-tooltip title="清空内容">
                      <a-button size="small" @click="clearParseText">
                        <clear-outlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="粘贴剪贴板">
                      <a-button size="small" @click="pasteFromClipboard">
                        <copy-outlined />
                      </a-button>
                    </a-tooltip>
                  </a-space>
                </div>
                <a-textarea
                  v-model:value="parseText"
                  placeholder="请输入产品信息文本，例如：
产品名称：蓝牙耳机Pro
品牌：TechPro
型号：TP-BT001
价格：299元
功能：降噪、防水、长续航
适用场景：运动、通勤、办公"
                  :rows="8"
                  :maxlength="2000"
                  show-count
                  style="margin-bottom: 16px;"
                />
              </div>

              <!-- 分隔线 -->
              <a-divider>
                <span style="color: #666; font-size: 12px;">或者</span>
              </a-divider>

              <!-- 文件上传区域 -->
              <div class="file-upload-area">
                <div class="input-header">
                  <h4><cloud-upload-outlined /> 文件上传</h4>
                  <span style="color: #666; font-size: 12px;">支持 TXT、DOCX、XLSX、PPTX 等格式</span>
                </div>
                <div
                  class="upload-zone"
                  :class="{ 'drag-over': isDragOver, 'has-file': uploadedFile }"
                  @drop="handleFileDrop"
                  @dragover="handleDragOver"
                  @dragenter="handleDragEnter"
                  @dragleave="handleDragLeave"
                  @click="triggerFileUpload"
                >
                  <div v-if="!fileProcessing && !uploadedFile" class="upload-placeholder">
                    <cloud-upload-outlined style="font-size: 32px; color: #1890ff;" />
                    <p style="margin: 8px 0 4px; color: #333;">拖放文件到此处或点击选择文件</p>
                    <p style="margin: 0; color: #999; font-size: 12px;">文件将自动解析并填充到上方文本框</p>
                  </div>

                  <div v-else-if="fileProcessing" class="upload-processing">
                    <a-spin />
                    <p style="margin-left: 12px; color: #666;">{{ processingMessage }}</p>
                  </div>

                  <div v-else-if="uploadedFile" class="upload-success">
                    <check-circle-outlined style="color: #52c41a; font-size: 24px;" />
                    <div style="margin-left: 12px;">
                      <p style="margin: 0; color: #333; font-weight: 500;">{{ uploadedFile.name }}</p>
                      <p style="margin: 0; color: #666; font-size: 12px;">{{ formatFileSize(uploadedFile.size) }} | 解析完成</p>
                    </div>
                    <a-button size="small" type="text" @click.stop="clearUploadedFile">
                      <close-outlined />
                    </a-button>
                  </div>
                </div>
              </div>

              <!-- 解析按钮 -->
              <div class="parse-action" style="text-align: center; margin-top: 24px;">
                <a-button
                  type="primary"
                  size="large"
                  :loading="parsing"
                  :disabled="!parseText.trim()"
                  @click="handleParse"
                >
                  <robot-outlined />
                  {{ parsing ? '正在解析中...' : '开始AI解析' }}
                </a-button>
                <p style="margin-top: 8px; color: #666; font-size: 12px;">
                  AI将智能识别产品信息并自动分类整理
                </p>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 第二步：信息确认 -->
        <div v-show="currentStep === 1" class="step-content">
          <!-- 解析结果展示 -->
          <div v-if="parseResult" class="parse-result-summary">
            <a-alert
              :message="`解析成功：${parseResult.基础信息?.产品名称 || '未知产品'}`"
              type="success"
              show-icon
              style="margin-bottom: 16px;"
            />

            <!-- 缺失信息提醒 -->
            <div v-if="parseResult.缺失信息 && parseResult.缺失信息.length > 0" style="margin-bottom: 16px;">
              <a-alert
                message="需要补充的信息"
                type="warning"
                show-icon
                style="margin-bottom: 12px;"
              >
                <template #description>
                  <div>为了更好地推广产品，建议补充以下 <strong>{{ parseResult.缺失信息.length }}</strong> 项信息：</div>
                </template>
              </a-alert>

              <!-- 完整的缺失信息列表 -->
              <a-card size="small" title="完整缺失信息清单" class="missing-info-card">
                <template #extra>
                  <a-space>
                    <a-tag color="orange">{{ parseResult.缺失信息.length }} 项待补充</a-tag>
                    <a-button
                      size="small"
                      type="text"
                      @click="copyMissingInfo"
                      title="复制缺失信息列表"
                    >
                      <copy-outlined />
                      复制清单
                    </a-button>
                  </a-space>
                </template>

                <div style="max-height: 300px; overflow-y: auto;">
                  <a-list
                    :data-source="parseResult.缺失信息"
                    size="small"
                    :split="true"
                    class="missing-info-list"
                  >
                    <template #renderItem="{ item, index }">
                      <a-list-item style="padding: 8px 0;">
                        <a-list-item-meta>
                          <template #avatar>
                            <a-avatar
                              :style="{ backgroundColor: '#fa8c16', fontSize: '12px' }"
                              size="small"
                            >
                              {{ index + 1 }}
                            </a-avatar>
                          </template>
                          <template #description>
                            <div style="color: #333; line-height: 1.5;">{{ item }}</div>
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>

                <!-- 操作提示 -->
                <a-divider style="margin: 12px 0;" />
                <div style="text-align: center; color: #666; font-size: 12px;">
                  <exclamation-circle-outlined style="margin-right: 4px;" />
                  建议联系商家或查阅产品资料补充以上信息，以提高推广效果
                </div>
              </a-card>
            </div>
          </div>

          <!-- 产品信息表单 -->
          <a-card title="产品信息" size="small">
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="产品名称" required>
                    <a-input
                      v-model:value="formData.产品名称"
                      placeholder="请输入产品名称"
                      :maxlength="100"
                      show-count
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="产品分类">
                    <a-input
                      v-model:value="formData.产品分类"
                      placeholder="请输入产品分类"
                      :maxlength="50"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="产品描述">
                <a-textarea
                  v-model:value="formData.产品描述"
                  placeholder="请输入产品描述"
                  :rows="4"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>

              <a-form-item label="产品状态">
                <a-radio-group v-model:value="formData.状态">
                  <a-radio :value="1">正常</a-radio>
                  <a-radio :value="0">禁用</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 详细产品信息展示 -->
          <div v-if="parseResult && parseResult.产品信息" style="margin-top: 24px;">
            <a-divider>详细产品信息</a-divider>

            <a-collapse>
              <!-- 核心卖点 -->
              <a-collapse-panel
                v-if="parseResult.产品信息.核心卖点_达人视角 && parseResult.产品信息.核心卖点_达人视角.length > 0"
                key="selling-points"
                header="核心卖点"
              >
                <a-list
                  :data-source="parseResult.产品信息.核心卖点_达人视角"
                  size="small"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-typography-text>{{ item }}</a-typography-text>
                    </a-list-item>
                  </template>
                </a-list>
              </a-collapse-panel>

              <!-- 营销亮点 -->
              <a-collapse-panel
                v-if="parseResult.产品信息.营销亮点 && parseResult.产品信息.营销亮点.length > 0"
                key="marketing-highlights"
                header="营销亮点"
              >
                <a-list
                  :data-source="parseResult.产品信息.营销亮点"
                  size="small"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-typography-text>{{ item }}</a-typography-text>
                    </a-list-item>
                  </template>
                </a-list>
              </a-collapse-panel>

              <!-- 价格信息 -->
              <a-collapse-panel
                v-if="parseResult.产品信息.合作关键信息?.价格信息"
                key="price-info"
                header="价格信息"
              >
                <a-descriptions :column="2" size="small">
                  <a-descriptions-item
                    v-if="parseResult.产品信息.合作关键信息.价格信息.零售价"
                    label="零售价"
                  >
                    {{ parseResult.产品信息.合作关键信息.价格信息.零售价 }}
                  </a-descriptions-item>
                  <a-descriptions-item
                    v-if="parseResult.产品信息.合作关键信息.佣金政策?.佣金比例"
                    label="佣金比例"
                  >
                    {{ parseResult.产品信息.合作关键信息.佣金政策?.佣金比例 }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-collapse-panel>

              <!-- 关键词 -->
              <a-collapse-panel
                v-if="parseResult.产品信息.关键词 && parseResult.产品信息.关键词.length > 0"
                key="keywords"
                header="关键词"
              >
                <div>
                  <a-tag
                    v-for="keyword in parseResult.产品信息.关键词"
                    :key="keyword"
                    color="blue"
                    style="margin: 2px;"
                  >
                    {{ keyword }}
                  </a-tag>
                </div>
              </a-collapse-panel>

              <!-- 其他补充信息 -->
              <a-collapse-panel
                v-if="parseResult.产品信息.其他补充信息 && parseResult.产品信息.其他补充信息.length > 0"
                key="other-info"
                header="其他补充信息"
              >
                <a-list
                  :data-source="parseResult.产品信息.其他补充信息"
                  size="small"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-typography-text>{{ item }}</a-typography-text>
                    </a-list-item>
                  </template>
                </a-list>
              </a-collapse-panel>
            </a-collapse>
          </div>

          <!-- 步骤导航 -->
          <div style="text-align: center; margin-top: 24px;">
            <a-space>
              <a-button @click="currentStep = 0">
                <left-outlined />
                返回解析
              </a-button>
              <a-button
                type="primary"
                :disabled="!formData.产品名称"
                @click="currentStep = 2"
              >
                下一步
                <right-outlined />
              </a-button>
            </a-space>
          </div>
        </div>

        <!-- 第三步：完成保存 -->
        <div v-show="currentStep === 2" class="step-content">
          <a-result
            status="info"
            title="确认保存产品信息"
            :sub-title="`产品名称：${formData.产品名称}`"
          >
            <template #extra>
              <a-space>
                <a-button @click="currentStep = 1">返回编辑</a-button>
                <a-button type="primary" :loading="submitLoading" @click="handleSubmit">
                  确认保存
                </a-button>
              </a-space>
            </template>
          </a-result>
        </div>

        <!-- 隐藏的文件输入 -->
        <input
          ref="fileInput"
          type="file"
          accept=".txt,.csv,.json,.docx,.doc,.xlsx,.xls,.pptx,.ppt,.pdf"
          style="display: none"
          @change="handleFileSelect"
        />
          </a-tab-pane>

          <a-tab-pane key="manual" tab="手动输入">
            <!-- 手动输入Tab内容 -->
            <div class="manual-input-content">
              <!-- 产品基本信息 -->
              <a-card title="产品基本信息" size="small" style="margin-bottom: 16px;">
                <a-form layout="vertical">
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="产品名称" required>
                        <a-input
                          v-model:value="manualFormData.产品名称"
                          placeholder="请输入产品名称"
                          :maxlength="100"
                          show-count
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="产品分类">
                        <a-input
                          v-model:value="manualFormData.产品分类"
                          placeholder="请输入产品分类"
                          :maxlength="50"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-form-item label="产品描述">
                    <a-textarea
                      v-model:value="manualFormData.产品描述"
                      placeholder="请输入产品描述"
                      :rows="4"
                      :maxlength="500"
                      show-count
                    />
                  </a-form-item>
                </a-form>
              </a-card>

              <!-- 产品规格信息 -->
              <div style="margin-bottom: 16px;">
                <ProductSpecEditor
                  v-if="!isUpdatingForm"
                  :key="specEditorKey"
                  :value="manualFormData.产品规格"
                  @update:value="handleSpecUpdate"
                />
              </div>

              <!-- 产品详细信息 -->
              <a-card title="产品详细信息" size="small">
                <ProductInfoEditor
                  v-if="!isUpdatingForm"
                  :key="infoEditorKey"
                  :value="manualFormData.产品信息"
                  @update:value="handleInfoUpdate"
                />
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>

        <!-- 自定义Footer -->
        <div class="modal-footer">
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              v-if="activeTab === 'smart' && currentStep > 0"
              @click="currentStep--"
            >
              上一步
            </a-button>
            <a-button
              type="primary"
              :loading="submitLoading"
              @click="handleModalOk"
            >
              {{ getModalOkText() }}
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 产品详情抽屉 -->
    <a-drawer
      v-model:open="showDetailDrawer"
      title="产品详情"
      width="600px"
      :footer-style="{ textAlign: 'right' }"
    >
      <ProductDetailView
        v-if="selectedProduct"
        :product="selectedProduct"
        @edit="editProduct"
        @delete="删除产品"
      />
      <template #footer>
        <a-button @click="showDetailDrawer = false">关闭</a-button>
      </template>
    </a-drawer>

    <!-- 知识库选择弹窗 -->
    <a-modal
      v-model:open="showKnowledgeSelectModal"
      title="选择知识库"
      width="600px"
      :footer="null"
      @cancel="handleKnowledgeModalCancel"
    >
      <div class="knowledge-select-content">
        <div class="product-info">
          <h4>产品信息</h4>
          <div class="product-details" v-if="selectedProductForSubmit">
            <p><strong>产品名称：</strong>{{ selectedProductForSubmit.产品名称 }}</p>
            <p><strong>产品分类：</strong>{{ selectedProductForSubmit.产品分类 || '未分类' }}</p>
            <p><strong>产品描述：</strong>{{ selectedProductForSubmit.产品描述 || '暂无描述' }}</p>
          </div>
        </div>

        <a-divider />

        <div class="knowledge-base-selection">
          <h4>选择目标知识库</h4>
          <a-spin :spinning="loadingKnowledgeBases">
            <div class="knowledge-base-list">
              <!-- 快捷选项：默认知识库 -->
              <div v-if="defaultKnowledgeBase" class="knowledge-base-section">
                <h5 style="margin-bottom: 12px; color: #1890ff;">
                  <star-outlined /> 快捷提交
                </h5>
                <div class="knowledge-base-item default-kb">
                  <div class="kb-header">
                    <a-tag color="blue">默认知识库</a-tag>
                    <span class="kb-name">{{ defaultKnowledgeBase.名称 }}</span>
                  </div>
                  <div class="kb-description">
                    {{ defaultKnowledgeBase.描述 || '您的AI助手默认知识库' }}
                  </div>
                  <div class="kb-actions">
                    <a-button
                      type="primary"
                      @click="executeSubmitToKnowledge('默认', defaultKnowledgeBase.名称)"
                      :disabled="loadingKnowledgeBases"
                    >
                      快速提交到默认知识库
                    </a-button>
                  </div>
                </div>
              </div>

              <!-- 所有知识库选项 -->
              <div v-if="knowledgeBaseList.length > 0" class="knowledge-base-section">
                <h5 style="margin-bottom: 12px; color: #52c41a;">
                  <database-outlined /> 选择知识库
                </h5>

                <!-- 知识库选择器 -->
                <div class="knowledge-base-selector">
                  <a-select
                    v-model:value="selectedKnowledgeBaseId"
                    placeholder="请选择要提交的知识库"
                    style="width: 100%; margin-bottom: 12px;"
                    :loading="loadingKnowledgeBases"
                    show-search
                    :filter-option="filterKnowledgeBase"
                  >
                    <a-select-option
                      v-for="kb in knowledgeBaseList"
                      :key="kb.id"
                      :value="kb.id"
                      :label="kb.名称"
                    >
                      <div class="kb-option">
                        <div class="kb-option-header">
                          <a-tag :color="kb.类型 === '默认' ? 'blue' : 'green'" size="small">
                            {{ kb.类型 === '默认' ? '默认' : '自定义' }}
                          </a-tag>
                          <span class="kb-option-name">{{ kb.名称 }}</span>
                        </div>
                        <div class="kb-option-desc" v-if="kb.描述">
                          {{ kb.描述 }}
                        </div>
                      </div>
                    </a-select-option>
                  </a-select>

                  <!-- 提交按钮 -->
                  <a-button
                    type="default"
                    block
                    :disabled="!selectedKnowledgeBaseId || loadingKnowledgeBases"
                    @click="submitToSelectedKnowledgeBase"
                  >
                    提交到选中的知识库
                  </a-button>
                </div>
              </div>

              <!-- 暂无知识库提示 -->
              <div class="no-knowledge-bases" v-if="!defaultKnowledgeBase && knowledgeBaseList.length === 0 && !loadingKnowledgeBases">
                <a-empty
                  description="暂无其他知识库"
                  :image="false"
                >
                  <template #description>
                    <span class="text-gray">您目前只有默认知识库，后续可以创建更多知识库</span>
                  </template>
                </a-empty>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  AppstoreOutlined,
  TagOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloudUploadOutlined,
  LoadingOutlined,
  ClearOutlined,
  StarOutlined,
  DatabaseOutlined,
  CopyOutlined,
  RobotOutlined,
  CheckOutlined,
  CloseOutlined,
  QuestionCircleOutlined,
  LeftOutlined,
  RightOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 导入子组件
import ProductInfoEditor from './ProductInfoEditor.vue'
import ProductDetailView from './ProductDetailView.vue'
import ProductSpecEditor from './ProductSpecEditor.vue'

// 导入服务
import productService from '@/services/productService'
import fileParser from '@/utils/fileParser'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const showAddModal = ref(false)
const showDetailDrawer = ref(false)
const editingProduct = ref(null)
const selectedProduct = ref(null)
const currentStep = ref(0) // 当前步骤：0-解析，1-确认，2-保存
const activeTab = ref('smart') // 当前激活的Tab：smart-智能识别，manual-手动输入
const isUpdatingForm = ref(false) // 标志是否正在更新表单，防止循环更新
const searchText = ref('')
const filterCategory = ref(undefined)
const filterStatus = ref(undefined)

// 解析相关数据
const parseText = ref('')
const parsing = ref(false)
const parseResult = ref(null)
const isDragOver = ref(false)
const fileProcessing = ref(false)
const uploadedFile = ref(null)
const fileInput = ref(null)
const processingMessage = ref('正在解析文件内容...')
const processingDetails = ref('')

// 表单相关
const formData = reactive({
  产品名称: '',
  产品分类: '',
  产品描述: '',
  产品信息: {},
  产品规格: {},
  状态: 1
})

// 手动输入表单数据
const manualFormData = reactive({
  产品名称: '',
  产品分类: '',
  产品描述: '',
  产品信息: {},
  产品规格: {}
})

// 表单验证规则
const formRules = {
  产品名称: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '产品名称长度在2-100字符之间', trigger: 'blur' }
  ]
}

// 产品列表数据
const productList = ref([])

// 知识库选择相关
const showKnowledgeSelectModal = ref(false)
const selectedProductForSubmit = ref(null)
const defaultKnowledgeBase = ref(null)  // 默认知识库信息
const knowledgeBaseList = ref([])       // 所有知识库列表
const selectedKnowledgeBaseId = ref(null) // 选中的知识库ID
const loadingKnowledgeBases = ref(false)

// 产品分类列表
const categories = ref([
  '数码电子',
  '服装配饰',
  '家居用品',
  '美妆护肤',
  '食品饮料',
  '母婴用品',
  '运动户外',
  '图书文娱'
])

// 动态key，用于强制重新渲染组件
const specEditorKey = ref(0)
const infoEditorKey = ref(0)

// 产品统计数据
const productStats = computed(() => [
  {
    key: 'total',
    title: '产品总数',
    value: productList.value.length,
    color: '#1890ff',
    icon: AppstoreOutlined
  },
  {
    key: 'active',
    title: '正常产品',
    value: productList.value.filter(p => p.状态 === 1).length,
    color: '#52c41a',
    icon: CheckCircleOutlined
  },
  {
    key: 'submitted',
    title: '已提交知识库',
    value: productList.value.filter(p => p.知识库状态?.已提交).length,
    color: '#13c2c2',
    icon: CloudUploadOutlined
  },
  {
    key: 'categories',
    title: '产品分类',
    value: new Set(productList.value.filter(p => p.产品分类).map(p => p.产品分类)).size,
    color: '#722ed1',
    icon: TagOutlined
  }
])

// 表格列配置
const columns = [
  {
    title: '产品名称',
    key: 'productName',
    width: 280,
    ellipsis: true
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
    align: 'center'
  },
  {
    title: '知识库状态',
    key: 'knowledgeStatus',
    width: 140,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 150,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 220,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
})

/**
 * 计算过滤后的产品列表
 */
const filteredProducts = computed(() => {
  let result = [...productList.value]
  
  // 根据搜索文本过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(product => 
      product.产品名称?.toLowerCase().includes(search) ||
      product.产品描述?.toLowerCase().includes(search)
    )
  }
  
  // 根据分类过滤
  if (filterCategory.value) {
    result = result.filter(product => product.产品分类 === filterCategory.value)
  }
  
  // 根据状态过滤
  if (filterStatus.value !== undefined) {
    result = result.filter(product => product.状态 === Number(filterStatus.value))
  }
  
  // 更新分页总数
  paginationConfig.total = result.length
  
  // 应用分页
  const start = (paginationConfig.current - 1) * paginationConfig.pageSize
  const end = start + paginationConfig.pageSize
  
  return result.slice(start, end)
})

/**
 * 格式化日期显示
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



/**
 * 粘贴剪贴板内容
 */
const pasteFromClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText()
    parseText.value = text
    message.success('已粘贴剪贴板内容')
  } catch (error) {
    message.error('无法访问剪贴板')
  }
}

/**
 * 清除上传的文件
 */
const clearUploadedFile = () => {
  uploadedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

/**
 * 复制缺失信息列表
 */
const copyMissingInfo = async () => {
  if (!parseResult.value?.缺失信息 || parseResult.value.缺失信息.length === 0) {
    message.warning('没有缺失信息可复制')
    return
  }

  try {
    const 缺失信息文本 = parseResult.value.缺失信息
      .map((item, index) => `${index + 1}. ${item}`)
      .join('\n')

    const 完整文本 = `产品缺失信息清单（共${parseResult.value.缺失信息.length}项）：\n\n${缺失信息文本}\n\n建议联系商家或查阅产品资料补充以上信息，以提高推广效果。`

    await navigator.clipboard.writeText(完整文本)
    message.success('缺失信息清单已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动选择文本复制')
  }
}

/**
 * 加载产品列表
 */
const loadProducts = async () => {
  try {
    loading.value = true

    // 调用产品列表API
    const response = await productService.getProductList({
      页码: paginationConfig.current,
      每页数量: paginationConfig.pageSize,
      产品名称: searchText.value,
      产品分类: filterCategory.value,
      状态: filterStatus.value
    })

    if (response.status === 100) {
      const data = response.data || {}
      // 适配后端返回的字段名
      productList.value = data.列表 || data.产品列表 || []
      paginationConfig.total = data.总数 || 0

      console.log('✅ 产品列表加载成功:', {
        产品数量: productList.value.length,
        总数: paginationConfig.total,
        当前页: paginationConfig.current,
        每页数量: paginationConfig.pageSize
      })
    } else {
      console.warn('⚠️ 产品列表加载失败:', response.message)
      message.error(response.message || '加载产品列表失败')
      productList.value = []
      paginationConfig.total = 0
    }

  } catch (error) {
    console.error('❌ 加载产品列表失败:', error)
    message.error('加载产品列表失败，请稍后重试')
    productList.value = []
    paginationConfig.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = (value) => {
  searchText.value = value
  paginationConfig.current = 1
  // 重新加载数据
  loadProducts()
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  paginationConfig.current = 1
}

/**
 * 重置筛选条件
 */
const resetFilters = () => {
  searchText.value = ''
  filterCategory.value = undefined
  filterStatus.value = undefined
  paginationConfig.current = 1
}

/**
 * 处理表格变化
 */
const handleTableChange = (pagination) => {
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  // 重新加载数据
  loadProducts()
}

/**
 * 查看产品详情
 */
const viewProduct = async (product) => {
  try {
    // 获取产品ID，兼容不同的字段名
    const productId = product.id || product.产品id || product.产品ID
    console.log('🔍 查看产品详情:', {
      产品名称: product.产品名称,
      产品ID: productId,
      原始数据: product
    })

    if (!productId) {
      console.warn('⚠️ 产品ID不存在，直接使用基础信息')
      selectedProduct.value = product
      showDetailDrawer.value = true
      return
    }

    // 获取产品详细信息
    const response = await productService.getProductDetail(productId)

    if (response.status === 100) {
      selectedProduct.value = response.data || product
      console.log('✅ 获取产品详情成功')
    } else {
      // 如果API失败，使用基础信息
      selectedProduct.value = product
      console.warn('⚠️ 获取产品详情失败，使用基础信息:', response.message)
    }

    showDetailDrawer.value = true
  } catch (error) {
    console.error('❌ 获取产品详情失败:', error)
    // 如果API失败，使用基础信息
    selectedProduct.value = product
    showDetailDrawer.value = true
  }
}

/**
 * 添加产品
 */
const handleAddProduct = () => {
  console.log('➕ 打开添加产品弹窗')

  // 先打开弹窗
  showAddModal.value = true

  // 然后重置表单
  resetForm()
}

/**
 * 编辑产品
 */
const editProduct = async (product) => {
  try {
    console.log('✏️ 编辑产品:', product)

    // 设置更新标志，隐藏子组件
    isUpdatingForm.value = true

    // 设置为手动输入模式
    activeTab.value = 'manual'

    // 先打开弹窗
    showAddModal.value = true

    // 获取产品ID，兼容不同的字段名
    const productId = product.id || product.产品id || product.产品ID
    console.log('🔍 编辑产品 - 获取详情:', {
      产品名称: product.产品名称,
      产品ID: productId,
      原始数据: product
    })

    let 完整产品数据 = product

    // 如果有产品ID，尝试获取详细信息
    if (productId) {
      try {
        const response = await productService.getProductDetail(productId)
        if (response.status === 100) {
          完整产品数据 = response.data || product
          console.log('✅ 编辑产品 - 获取详情成功:', 完整产品数据)
        } else {
          console.warn('⚠️ 编辑产品 - 获取详情失败，使用列表数据:', response.message)
        }
      } catch (error) {
        console.warn('⚠️ 编辑产品 - 获取详情异常，使用列表数据:', error)
      }
    }

    // 设置编辑的产品数据
    editingProduct.value = 完整产品数据

    // 处理产品规格格式转换
    let 规格数据 = 完整产品数据.产品规格 || {}

    // 如果是新的字典格式 {规格列表: [...]}, 转换为数组格式供编辑器使用
    if (规格数据.规格列表 && Array.isArray(规格数据.规格列表)) {
      规格数据 = 规格数据.规格列表
    }

    // 处理产品分类格式转换（数组 -> 字符串）
    let 分类数据 = 完整产品数据.产品分类
    if (Array.isArray(分类数据)) {
      分类数据 = 分类数据.join(',') // 将数组转换为逗号分隔的字符串
    } else if (!分类数据) {
      分类数据 = ''
    }



    // 立即设置表单数据
    formData.产品名称 = 完整产品数据.产品名称
    formData.产品分类 = 分类数据
    formData.产品描述 = 完整产品数据.产品描述
    formData.产品信息 = 完整产品数据.产品信息 || {}
    formData.产品规格 = 规格数据
    formData.状态 = 完整产品数据.状态

    manualFormData.产品名称 = 完整产品数据.产品名称
    manualFormData.产品分类 = 分类数据
    manualFormData.产品描述 = 完整产品数据.产品描述
    manualFormData.产品信息 = 完整产品数据.产品信息 || {}
    manualFormData.产品规格 = 规格数据



    // 强制重新渲染子组件
    specEditorKey.value++
    infoEditorKey.value++

    // 延迟显示子组件，确保数据已经设置完成
    setTimeout(() => {
      isUpdatingForm.value = false
    }, 100)

  } catch (error) {
    console.error('❌ 编辑产品失败:', error)
    message.error('编辑产品失败，请稍后重试')
    showAddModal.value = false
    isUpdatingForm.value = false
  }
}

/**
 * 删除产品
 */
const 删除产品 = async (产品) => {
  try {
    // 获取产品ID，兼容不同的字段名
    const 产品id = 产品.id || 产品.产品id || 产品.产品ID
    console.log('🗑️ 开始删除产品:', 产品.产品名称, 'ID:', 产品id)
    console.log('🔍 产品完整数据:', 产品)
    console.log('🔍 产品ID类型:', typeof 产品id, '值:', 产品id)

    if (!产品id) {
      console.error('❌ 产品ID不存在')
      message.error('产品ID不存在，无法删除')
      return
    }

    const response = await productService.deleteProduct(产品id)
    console.log('📥 删除响应:', response)

    if (response?.status === 100) {
      // 从列表中移除
      const index = productList.value.findIndex(p => p.id === 产品.id)
      if (index !== -1) {
        productList.value.splice(index, 1)
        console.log('✅ 产品已从列表中移除')
      }

      // 更新总数
      paginationConfig.total = Math.max(0, paginationConfig.total - 1)

      message.success('产品删除成功')
    } else {
      console.error('❌ 删除失败:', response)
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('💥 删除产品异常:', error)
    message.error('删除失败，请稍后重试')
  }
}

/**
 * 提交产品到知识库
 */
const submitToKnowledge = async (product) => {
  try {
    console.log('📤 准备提交产品到知识库:', product.产品名称)

    // 如果产品已提交，显示确认对话框
    if (product.知识库状态?.已提交) {
      Modal.confirm({
        title: '产品已存在',
        content: '该产品已提交到知识库，是否要重新提交（覆盖现有文档）？',
        okText: '重新提交',
        cancelText: '取消',
        onOk: async () => {
          // 继续执行提交流程
          await proceedWithSubmission()
        },
        onCancel: () => {
          console.log('用户取消重新提交')
        }
      })
      return
    }

    // 正常提交流程
    await proceedWithSubmission()

    async function proceedWithSubmission() {
      // 显示知识库选择弹窗
      selectedProductForSubmit.value = product
      showKnowledgeSelectModal.value = true

      // 加载知识库列表
      await loadKnowledgeBases()
    }

  } catch (error) {
    console.error('💥 准备提交产品异常:', error)
    message.error('操作失败，请稍后重试')
  }
}

/**
 * 获取知识库提交选项
 */
const loadKnowledgeBases = async () => {
  try {
    loadingKnowledgeBases.value = true

    // 调用API获取知识库提交选项
    const response = await productService.getKnowledgeBaseOptions()

    if (response?.status === 100) {
      const data = response.data || {}

      // 存储数据
      defaultKnowledgeBase.value = data.默认知识库
      knowledgeBaseList.value = data.所有知识库 || []

      console.log('📚 获取知识库提交选项成功:', {
        默认知识库: data.默认知识库,
        所有知识库数量: data.所有知识库?.length || 0,
        总数: data.总数
      })
    } else {
      console.error('❌ 获取知识库提交选项失败:', response)
      message.error('获取知识库选项失败')
      defaultKnowledgeBase.value = null
      knowledgeBaseList.value = []
    }
  } catch (error) {
    console.error('💥 获取知识库提交选项异常:', error)
    message.error('获取知识库选项失败')
    defaultKnowledgeBase.value = null
    knowledgeBaseList.value = []
  } finally {
    loadingKnowledgeBases.value = false
  }
}

/**
 * 执行提交到知识库
 */
const executeSubmitToKnowledge = async (knowledgeBaseId, knowledgeBaseName) => {
  try {
    const product = selectedProductForSubmit.value
    if (!product) return

    console.log('📤 开始提交产品到知识库:', product.产品名称, '知识库:', knowledgeBaseName)

    // 显示加载提示
    const hideLoading = message.loading('正在提交产品到知识库，请稍候...', 0)

    try {
      // 调用提交API
      const response = await productService.submitToKnowledge({
        产品id: product.id,
        产品名称: product.产品名称,
        产品描述: product.产品描述,
        产品分类: product.产品分类,
        知识库id: knowledgeBaseId
      })

      console.log('📥 提交响应:', response)

      if (response?.status === 100) {
        // 更新本地产品的知识库状态
        const index = productList.value.findIndex(p => p.id === product.id)
        if (index !== -1) {
          productList.value[index].知识库状态 = {
            已提交: true,
            文档数量: 1,
            最新提交时间: new Date().toISOString(),
            文档名称列表: [product.产品名称]
          }
        }

        message.success(`产品已成功提交到${knowledgeBaseName}`)
        console.log('✅ 产品提交成功')

        // 关闭弹窗
        showKnowledgeSelectModal.value = false
        selectedProductForSubmit.value = null
      } else if (response?.status === 1113) {
        // 产品已提交到知识库的特殊处理
        console.log('ℹ️ 产品已存在于知识库:', response)

        // 显示友好的提示，并提供选项
        Modal.confirm({
          title: '产品已存在',
          content: `${response.message}\n\n您可以选择：\n• 查看现有文档\n• 重新提交（覆盖现有文档）`,
          okText: '重新提交',
          cancelText: '取消',
          onOk: () => {
            // 调用重新提交功能
            handleResubmitToKnowledge(product, knowledgeBaseId, knowledgeBaseName)
          },
          onCancel: () => {
            console.log('用户取消重新提交')
          }
        })
      } else {
        console.error('❌ 产品提交失败:', response)
        message.error(response?.message || '提交失败')
      }
    } finally {
      hideLoading()
    }
  } catch (error) {
    console.error('💥 提交产品到知识库异常:', error)
    message.error('提交失败，请稍后重试')
  }
}

/**
 * 提交到选中的知识库
 */
const submitToSelectedKnowledgeBase = async () => {
  if (!selectedKnowledgeBaseId.value) {
    message.warning('请先选择知识库')
    return
  }

  // 找到选中的知识库信息
  const selectedKb = knowledgeBaseList.value.find(kb => kb.id === selectedKnowledgeBaseId.value)
  if (!selectedKb) {
    message.error('选中的知识库不存在')
    return
  }

  // 调用提交方法
  await executeSubmitToKnowledge(selectedKb.id, selectedKb.名称)

  // 重置选择
  selectedKnowledgeBaseId.value = null
}

/**
 * 重新提交产品到知识库
 */
const handleResubmitToKnowledge = async (product, knowledgeBaseId, knowledgeBaseName) => {
  try {
    console.log('🔄 开始重新提交产品到知识库:', product.产品名称)

    // 显示加载提示
    const hideLoading = message.loading('正在重新提交产品到知识库，请稍候...', 0)

    try {
      // 调用重新提交API
      const response = await productService.resubmitToKnowledge({
        产品id: product.id,
        产品名称: product.产品名称,
        知识库id: knowledgeBaseId,
        强制覆盖: true
      })

      console.log('📥 重新提交响应:', response)

      if (response?.status === 100) {
        // 更新本地产品的知识库状态
        const index = productList.value.findIndex(p => p.id === product.id)
        if (index !== -1) {
          productList.value[index].知识库状态 = {
            已提交: true,
            文档数量: 1,
            最新提交时间: new Date().toISOString(),
            文档名称列表: [product.产品名称]
          }
        }

        message.success(`产品已成功重新提交到${knowledgeBaseName}`)
        console.log('✅ 产品重新提交成功')

        // 关闭弹窗
        showKnowledgeSelectModal.value = false
        selectedProductForSubmit.value = null
      } else {
        console.error('❌ 产品重新提交失败:', response)
        message.error(response?.message || '重新提交失败')
      }
    } finally {
      hideLoading()
    }
  } catch (error) {
    console.error('💥 重新提交产品到知识库异常:', error)
    message.error('重新提交失败，请稍后重试')
  }
}

/**
 * 知识库搜索过滤
 */
const filterKnowledgeBase = (input, option) => {
  const kb = knowledgeBaseList.value.find(kb => kb.id === option.value)
  if (!kb) return false

  const searchText = input.toLowerCase()
  return kb.名称.toLowerCase().includes(searchText) ||
         (kb.描述 && kb.描述.toLowerCase().includes(searchText))
}

/**
 * 处理知识库选择模态框取消
 */
const handleKnowledgeModalCancel = () => {
  showKnowledgeSelectModal.value = false
  selectedProductForSubmit.value = null
  selectedKnowledgeBaseId.value = null
  defaultKnowledgeBase.value = null
  knowledgeBaseList.value = []
}

/**
 * 表单验证
 */
const validateForm = (data = formData) => {
  const errors = []

  if (!data.产品名称?.trim()) {
    errors.push('产品名称不能为空')
  }

  if (data.产品名称?.length > 100) {
    errors.push('产品名称不能超过100个字符')
  }

  if (data.产品分类?.length > 50) {
    errors.push('产品分类不能超过50个字符')
  }

  if (data.产品描述?.length > 500) {
    errors.push('产品描述不能超过500个字符')
  }

  return errors
}

/**
 * 处理弹窗确定按钮
 */
const handleModalOk = () => {
  if (activeTab.value === 'manual') {
    // 手动输入模式直接提交
    handleSubmit()
  } else {
    // 智能识别模式根据步骤处理
    if (currentStep.value === 0) {
      // 第一步：执行解析
      handleParseProduct()
    } else if (currentStep.value === 1) {
      // 第二步：跳转到第三步
      currentStep.value = 2
    } else {
      // 第三步：提交表单
      handleSubmit()
    }
  }
}

/**
 * 获取弹窗确定按钮文本
 */
const getModalOkText = () => {
  if (activeTab.value === 'manual') {
    return editingProduct.value ? '更新产品' : '添加产品'
  } else {
    // 智能识别模式
    if (currentStep.value === 0) {
      return '开始解析'
    } else if (currentStep.value === 1) {
      return '下一步'
    } else {
      return editingProduct.value ? '更新产品' : '添加产品'
    }
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    // 根据当前Tab选择数据源
    const currentFormData = activeTab.value === 'manual' ? manualFormData : formData


    // 表单验证
    const validationErrors = validateForm(currentFormData)
    if (validationErrors.length > 0) {
      message.error(validationErrors[0])
      return
    }

    // 转换数据格式
    const submitData = { ...currentFormData }

    // 转换产品分类格式：字符串 -> 数组
    if (typeof submitData.产品分类 === 'string' && submitData.产品分类.trim()) {
      submitData.产品分类 = submitData.产品分类.split(',').map(item => item.trim()).filter(item => item)
    } else if (!submitData.产品分类) {
      submitData.产品分类 = []
    }

    // 转换产品规格格式：数组 -> 字典
    if (Array.isArray(submitData.产品规格)) {
      // 将数组格式转换为后端期望的字典格式
      submitData.产品规格 = {
        规格列表: submitData.产品规格
      }
    }


    submitLoading.value = true

    if (editingProduct.value) {
      // 更新产品
      const productId = editingProduct.value.id || editingProduct.value.产品id || editingProduct.value.产品ID
      console.log('🔄 更新产品:', productId)
      const response = await productService.updateProduct({
        产品ID: productId,
        ...submitData
      })

      if (response.status === 100) {
        // 更新本地数据
        const index = productList.value.findIndex(p => p.id === editingProduct.value.id)
        if (index !== -1) {
          productList.value[index] = {
            ...productList.value[index],
            ...submitData,
            更新时间: new Date().toLocaleString('zh-CN')
          }
        }
        message.success('产品更新成功')
        console.log('✅ 产品更新成功')
      } else {
        console.error('❌ 产品更新失败:', response)
        message.error(response.message || '更新失败')
        return
      }
    } else {
      // 添加产品
      console.log('➕ 添加新产品')
      const response = await productService.createProduct(submitData)

      if (response.status === 100) {
        // 添加到本地数据
        const newProduct = {
          id: response.data?.产品id || Date.now(),
          ...submitData,
          创建时间: new Date().toLocaleString('zh-CN'),
          更新时间: new Date().toLocaleString('zh-CN')
        }
        productList.value.unshift(newProduct)
        message.success('产品添加成功')
        console.log('✅ 产品添加成功:', newProduct)
      } else {
        console.error('❌ 产品添加失败:', response)
        message.error(response.message || '添加失败')
        return
      }
    }

    // 关闭弹窗并重置表单
    showAddModal.value = false
    resetForm()

  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败，请稍后重试')
  } finally {
    submitLoading.value = false
  }
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  console.log('❌ 取消编辑/添加产品')

  // 只关闭弹窗，不重置表单
  showAddModal.value = false

  // 重置其他状态
  parsing.value = false
  isDragOver.value = false
  fileProcessing.value = false
  processingMessage.value = '正在解析文件内容...'
  processingDetails.value = ''
}

/**
 * 处理产品规格更新
 */
const handleSpecUpdate = (newValue) => {
  if (!isUpdatingForm.value) {
    manualFormData.产品规格 = newValue
  }
}

/**
 * 处理产品信息更新
 */
const handleInfoUpdate = (newValue) => {
  if (!isUpdatingForm.value) {
    manualFormData.产品信息 = newValue
  }
}

/**
 * 导出产品
 */
const exportProducts = () => {
  message.info('导出功能开发中...')
}

/**
 * 刷新产品列表
 */
const refreshProducts = () => {
  paginationConfig.current = 1
  loadProducts()
}

/**
 * 处理产品解析完成
 */
const handleParsed = (parsedData) => {
  console.log('产品解析完成:', parsedData)
  message.success('产品解析完成，可以查看解析结果')
}

/**
 * 触发文件上传
 */
const triggerFileUpload = () => {
  fileInput.value?.click()
}

/**
 * 处理文件选择
 */
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

/**
 * 处理文件拖放
 */
const handleFileDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

/**
 * 处理拖拽事件
 */
const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDragEnter = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  isDragOver.value = false
}

/**
 * 处理文件内容
 */
const processFile = async (file) => {
  try {
    fileProcessing.value = true
    uploadedFile.value = file
    processingMessage.value = '正在检查文件格式...'
    processingDetails.value = ''

    // 检查文件是否支持
    if (!fileParser.isSupported(file)) {
      throw new Error(`不支持的文件格式: ${file.type || fileParser.getFileExtension(file.name)}`)
    }

    // 根据文件类型设置不同的处理提示
    const fileExtension = fileParser.getFileExtension(file.name)
    if (fileExtension === '.pptx' || fileExtension === '.ppt') {
      processingMessage.value = '正在解析PowerPoint文件...'
      processingDetails.value = '正在提取幻灯片文本和图片，这可能需要较长时间'
    } else if (fileExtension === '.docx') {
      processingMessage.value = '正在解析Word文档...'
      processingDetails.value = '正在提取文档内容'
    } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      processingMessage.value = '正在解析Excel表格...'
      processingDetails.value = '正在提取表格数据'
    } else {
      processingMessage.value = '正在解析文件内容...'
      processingDetails.value = '正在读取文件数据'
    }

    // 使用文件解析器解析文件
    const content = await fileParser.parseFile(file)
    parseText.value = content

    // 根据文件类型显示不同的成功消息
    if (fileExtension === '.pptx' || fileExtension === '.ppt') {
      message.success('PowerPoint文件解析完成，已提取文本和图片内容')
    } else {
      message.success('文件解析成功')
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    message.error(`文件处理失败: ${error.message}`)

    // 清理状态
    uploadedFile.value = null
    parseText.value = ''
  } finally {
    fileProcessing.value = false
    processingMessage.value = '正在解析文件内容...'
    processingDetails.value = ''
  }
}





/**
 * 清空解析文本
 */
const clearParseText = () => {
  parseText.value = ''
  clearUploadedFile()
}



/**
 * 开始解析
 */
const handleParse = async () => {
  if (!parseText.value.trim()) {
    message.warning('请输入产品信息文本')
    return
  }

  try {
    parsing.value = true
    parseResult.value = null

    console.log('🚀 开始解析产品信息:', parseText.value.substring(0, 100) + '...')

    // 显示解析进度提示
    const hideLoading = message.loading('AI正在解析产品信息，请耐心等待...', 0)

    try {
      // 调用产品解析API
      const response = await productService.parseProduct({
        productText: parseText.value
      })

      console.log('📥 解析API响应:', response)
      console.log('📥 响应状态:', response.status)
      console.log('📥 响应消息:', response.message)
      console.log('📥 响应数据:', response.data)

      if (response.status === 100) {
        parseResult.value = response.data
        console.log('✅ 解析成功，数据结构:', parseResult.value)
        console.log('✅ 基础信息:', parseResult.value?.基础信息)
        console.log('✅ 产品信息:', parseResult.value?.产品信息)

        // 自动应用解析结果到表单
        applyParseResult()

        // 跳转到第二步
        currentStep.value = 1

        message.success('产品信息解析成功，已自动填充到表单')
      } else {
        console.error('❌ 解析失败:', response)
        console.error('❌ 失败状态码:', response.status)
        console.error('❌ 失败消息:', response.message)
        message.error(response.message || '解析失败')
      }
    } finally {
      hideLoading()
    }
  } catch (error) {
    console.error('💥 产品解析异常:', error)
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      message.error('解析超时，请检查网络连接或稍后重试')
    } else {
      message.error('解析失败，请稍后重试')
    }
  } finally {
    parsing.value = false
  }
}

/**
 * 应用解析结果到表单
 */
const applyParseResult = () => {
  if (!parseResult.value) {
    console.warn('⚠️ 没有解析结果可应用')
    return
  }

  console.log('📋 开始应用解析结果到表单:', parseResult.value)

  // 处理API响应数据结构
  const 基础信息 = parseResult.value.基础信息 || {}
  const 产品信息 = parseResult.value.产品信息 || {}

  console.log('📊 基础信息:', 基础信息)
  console.log('📊 产品信息:', 产品信息)

  // 提取字段值
  const 产品名称 = 基础信息.产品名称 || ''

  // 处理产品分类 - 可能是数组或字符串
  let 产品分类 = ''
  if (产品信息.产品分类) {
    if (Array.isArray(产品信息.产品分类)) {
      产品分类 = 产品信息.产品分类.join(', ')
    } else {
      产品分类 = 产品信息.产品分类
    }
  }

  // 提取产品描述
  const 产品描述 = 产品信息.产品描述 || 基础信息.产品描述 || ''

  // 将解析结果应用到表单数据
  Object.assign(formData, {
    产品名称,
    产品分类,
    产品描述,
    产品信息,
    状态: 1
  })

  console.log('✅ 表单数据已更新:', {
    产品名称,
    产品分类,
    产品描述: 产品描述.substring(0, 50) + (产品描述.length > 50 ? '...' : ''),
    状态: formData.状态
  })

  // 显示解析到的字段统计
  const 解析字段数 = [产品名称, 产品分类, 产品描述].filter(Boolean).length
  console.log(`📈 成功解析 ${解析字段数}/3 个基础字段`)
}

/**
 * 重置表单
 */
const resetForm = () => {
  console.log('🔄 重置表单')

  // 设置更新标志，隐藏子组件
  isUpdatingForm.value = true

  // 重置基本状态
  parseResult.value = null
  parseText.value = ''
  editingProduct.value = null
  currentStep.value = 0
  uploadedFile.value = null
  activeTab.value = 'smart'

  // 立即重置表单数据
  formData.产品名称 = ''
  formData.产品分类 = ''
  formData.产品描述 = ''
  formData.产品信息 = {}
  formData.产品规格 = {}
  formData.状态 = 1

  manualFormData.产品名称 = ''
  manualFormData.产品分类 = ''
  manualFormData.产品描述 = ''
  manualFormData.产品信息 = {}
  manualFormData.产品规格 = {}

  // 强制重新渲染子组件
  specEditorKey.value++
  infoEditorKey.value++

  // 延迟显示子组件，确保数据已经重置完成
  setTimeout(() => {
    isUpdatingForm.value = false
  }, 100)
}

/**
 * 清除解析结果
 */
const clearParseResult = () => {
  console.log('🗑️ 清除解析结果')
  parseResult.value = null
  parseText.value = ''
  message.success('解析结果已清除')
}

// 组件挂载时加载数据
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
/* 模块整体样式 */
.product-management-module {
  padding: 0;
}

/* 产品弹窗样式 */
.product-modal-content {
  .step-content {
    min-height: 400px;
  }

  .input-section {
    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #333;
        font-size: 14px;
        font-weight: 500;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }
    }

    .text-input-area {
      margin-bottom: 16px;
    }

    .file-upload-area {
      margin-bottom: 16px;
    }

    .upload-zone {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover, &.drag-over {
        border-color: #1890ff;
        background-color: #f0f8ff;
      }

      &.has-file {
        border-color: #52c41a;
        background-color: #f6ffed;
      }

      .upload-placeholder {
        text-align: center;
        padding: 20px 0;
      }

      .upload-processing {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;
      }

      .upload-success {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
      }
    }
  }

  .parse-result-summary {
    margin-bottom: 16px;
  }

  /* 缺失信息列表样式 */
  .missing-info-list .ant-list-item {
    border-bottom: 1px solid #f0f0f0;
  }

  .missing-info-list .ant-list-item:last-child {
    border-bottom: none;
  }

  .missing-info-list .ant-list-item-meta-description {
    color: #333 !important;
    font-size: 13px;
    line-height: 1.6;
  }

  .missing-info-list .ant-avatar {
    flex-shrink: 0;
  }

  /* 缺失信息卡片样式 */
  .missing-info-card .ant-card-head {
    background: linear-gradient(135deg, #fff7e6 0%, #fffbe6 100%);
    border-bottom: 1px solid #ffe7ba;
  }

  .missing-info-card .ant-card-body {
    padding: 16px;
  }
}

/* 概览区域样式 */
.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 产品卡片样式 */
.product-card {
  border-radius: 12px;
  overflow: hidden;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

/* 表格样式 */
.product-table {
  margin-top: 16px;
}

.product-table :deep(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
}

.product-name-cell .product-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.text-gray {
  color: #999;
}

/* 知识库状态列样式 */
.knowledge-status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.knowledge-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.text-small {
  font-size: 11px;
  line-height: 1.2;
}

/* 知识库选择弹窗样式 */
.knowledge-select-content {
  .product-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;

    h4 {
      margin: 0 0 12px 0;
      color: #1890ff;
      font-weight: 600;
    }

    .product-details {
      p {
        margin: 8px 0;
        color: #666;

        strong {
          color: #333;
          margin-right: 8px;
        }
      }
    }
  }

  .knowledge-base-selection {
    h4 {
      margin: 0 0 16px 0;
      color: #1890ff;
      font-weight: 600;
    }

    .knowledge-base-list {
      .knowledge-base-item {
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        }

        &.default-kb {
          border-color: #1890ff;
          background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        }

        .kb-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .kb-name {
            font-weight: 600;
            color: #333;
            margin-left: 8px;
          }
        }

        .kb-description {
          color: #666;
          font-size: 13px;
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .kb-actions {
          text-align: right;
        }
      }

      .no-other-kb {
        text-align: center;
        padding: 20px;
        color: #999;
      }
    }
  }
}

/* 表单样式 */
.product-form {
  margin-top: 16px;
}

/* 解析标签页样式 */
.parse-tab-content {
  max-height: 70vh;
  overflow-y: auto;
}

.parse-alert {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
}

/* 文件拖放区域样式 */
.file-drop-section {
  margin-bottom: 24px;
}

.file-drop-zone {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.file-drop-zone:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.file-drop-zone.drag-over {
  border-color: #1890ff;
  background: #e6f7ff;
  transform: scale(1.02);
}

.file-drop-zone.has-content {
  border-color: #52c41a;
  background: #f6ffed;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.drop-icon {
  font-size: 48px;
  color: #1890ff;
}

.drop-text .primary-text {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 8px 0;
}

.drop-text .secondary-text {
  font-size: 14px;
  color: #52c41a;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.drop-text .hint-text {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.processing-text {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.processing-details {
  margin-top: 8px;
}

.processing-details p {
  font-size: 12px;
  color: #999;
  margin: 0;
  font-style: italic;
}

.file-info {
  margin-top: 12px;
}

/* 文本输入区域样式 */
.text-input-section {
  margin-bottom: 24px;
}

.parse-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 解析结果样式 */
.parse-result-section {
  margin-bottom: 24px;
}

.result-preview {
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.missing-info {
  margin-top: 16px;
}

.missing-list {
  margin: 0;
  padding-left: 20px;
}

.missing-list li {
  margin-bottom: 4px;
}

/* 知识库选择样式 */
.knowledge-base-section {
  margin-bottom: 20px;
}

.knowledge-base-section h5 {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.knowledge-base-section:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.knowledge-base-item.default-kb {
  border: 2px solid #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
}

.knowledge-base-item.default-kb .kb-header .kb-name {
  color: #1890ff;
  font-weight: 600;
}

/* 知识库选择器样式 */
.knowledge-base-selector {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.kb-option {
  padding: 4px 0;
}

.kb-option-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.kb-option-name {
  font-weight: 500;
  color: #262626;
}

.kb-option-desc {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 24px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    padding: 12px;
  }

  .product-table :deep(.ant-table-thead > tr > th),
  .product-table :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }

  .parse-tab-content {
    max-height: 80vh;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .file-drop-zone {
    padding: 30px 15px;
  }

  .drop-icon {
    font-size: 36px;
  }
}

/* 手动输入Tab样式 */
.manual-input-content {
  .ant-card {
    border-radius: 8px;
  }
}

/* 自定义Footer样式 */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  margin: 0 -24px -20px -24px;
  background: #fafafa;
}
</style>