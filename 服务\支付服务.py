# 服务/支付服务.py
import base64
import json
import os
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import HTTPException
from wechatpayv3 import WeChatPay, WeChatPayType

from config import WECHAT_PAY_CONFIG
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 安全日志器, 应用日志器, 错误日志器
from 状态 import 状态


class 统一订单支付服务类:
    """
    支付服务类 - 处理订单创建、支付、回调等功能
    """

    def __init__(self):
        """初始化支付服务"""
        self.微信支付配置 = self._加载微信支付配置()
        self.微信支付客户端 = None
        self.微信JSAPI支付客户端 = None
        self.支付功能可用 = False
        self.配置错误信息 = ""

        # 初始化微信支付客户端
        配置验证结果, 配置错误信息 = self._验证微信支付配置()
        if 配置验证结果:
            try:
                self.微信支付客户端 = WeChatPay(
                    wechatpay_type=WeChatPayType.NATIVE,
                    mchid=self.微信支付配置["MCHID"],
                    private_key=self.微信支付配置["PRIVATE_KEY"],
                    cert_serial_no=self.微信支付配置["CERT_SERIAL_NO"],
                    appid=self.微信支付配置["APPID"],
                    apiv3_key=self.微信支付配置["APIV3_KEY"],
                    notify_url=self.微信支付配置["NOTIFY_URL"],
                )
                # 初始化JSAPI支付客户端（微信内浏览器支付）
                self.微信JSAPI支付客户端 = WeChatPay(
                    wechatpay_type=WeChatPayType.JSAPI,
                    mchid=self.微信支付配置["MCHID"],
                    private_key=self.微信支付配置["PRIVATE_KEY"],
                    cert_serial_no=self.微信支付配置["CERT_SERIAL_NO"],
                    appid=self.微信支付配置["APPID"],
                    apiv3_key=self.微信支付配置["APIV3_KEY"],
                    notify_url=self.微信支付配置["NOTIFY_URL"],
                )
                self.支付功能可用 = True
                应用日志器.info("微信支付客户端（NATIVE+JSAPI）初始化成功")
            except Exception as e:
                错误日志器.error(f"微信支付客户端初始化失败: {str(e)}")
                self.配置错误信息 = f"微信支付初始化失败: {str(e)}"
        else:
            应用日志器.warning(f"微信支付配置不完整，支付功能将被禁用: {配置错误信息}")
            self.配置错误信息 = 配置错误信息

    def _加载微信支付配置(self) -> Dict[str, str]:
        """加载微信支付配置"""

        # 加载私钥
        私钥 = self._加载私钥()

        配置 = {
            "APPID": WECHAT_PAY_CONFIG.get("appid") or os.getenv("WECHAT_APPID", ""),
            "MCHID": WECHAT_PAY_CONFIG.get("mchid") or os.getenv("WECHAT_MCHID", ""),
            "PRIVATE_KEY": 私钥,
            "CERT_SERIAL_NO": WECHAT_PAY_CONFIG.get("cert_serial_no")
            or os.getenv("WECHAT_CERT_SERIAL_NO", ""),
            "APIV3_KEY": WECHAT_PAY_CONFIG.get("apiv3_key")
            or os.getenv("WECHAT_APIV3_KEY", ""),
            "NOTIFY_URL": WECHAT_PAY_CONFIG.get("notify_url")
            or os.getenv(
                "WECHAT_NOTIFY_URL", "http://localhost:8000/api/order/wechat_pay_notify"
            ),
        }

        应用日志器.info(
            f"💳 微信支付配置加载完成: APPID={bool(配置['APPID'])}, MCHID={bool(配置['MCHID'])}, NOTIFY_URL={配置['NOTIFY_URL']}"
        )
        return 配置

    def _加载私钥(self) -> Optional[str]:
        """加载微信支付私钥"""
        # 优先从wechat目录下的证书文件加载
        wechat_key_path = "wechat/apiclient_key.pem"
        if os.path.exists(wechat_key_path):
            try:
                with open(wechat_key_path, "r", encoding="utf-8") as f:
                    应用日志器.info(f"从wechat目录加载微信支付私钥: {wechat_key_path}")
                    return f.read().strip()
            except Exception as e:
                错误日志器.error(f"从wechat目录加载私钥失败: {str(e)}")

        # 从环境变量指定的文件路径加载
        private_key_path = os.getenv("WECHAT_PRIVATE_KEY_PATH", "")
        if private_key_path and os.path.exists(private_key_path):
            try:
                with open(private_key_path, "r", encoding="utf-8") as f:
                    return f.read().strip()
            except Exception as e:
                错误日志器.error(f"从指定路径加载私钥失败: {str(e)}")

        # 从环境变量加载
        private_key = os.getenv("WECHAT_PRIVATE_KEY", "")
        if private_key and not private_key.startswith("-----BEGIN"):
            try:
                private_key = base64.b64decode(private_key).decode("utf-8")
            except Exception:
                pass

        return (
            private_key
            if private_key and private_key.startswith("-----BEGIN")
            else None
        )

    def _验证微信支付配置(self) -> tuple[bool, str]:
        """验证微信支付配置是否完整"""
        必需字段 = [
            "APPID",
            "MCHID",
            "PRIVATE_KEY",
            "CERT_SERIAL_NO",
            "APIV3_KEY",
            "NOTIFY_URL",
        ]
        缺失字段 = []

        for 字段 in 必需字段:
            if not self.微信支付配置.get(字段):
                缺失字段.append(字段)

        if 缺失字段:
            if "PRIVATE_KEY" in 缺失字段:
                错误信息 = (
                    "微信支付证书文件缺失，请将 apiclient_key.pem 放到 wechat/ 目录下"
                )
            else:
                错误信息 = f"微信支付配置缺失: {', '.join(缺失字段)}"
            错误日志器.error(错误信息)
            return False, 错误信息

        return True, "配置验证通过"

    async def 创建订单(
        self, 用户id: int, 会员id: int, 付费周期: str, 支付类型: str = "NATIVE"
    ) -> Dict[str, Any]:
        """
        创建支付订单

        Args:
            用户id: 用户id
            会员id: 会员套餐ID
            付费周期: 付费周期 monthly/yearly
            支付类型: 支付类型 NATIVE(扫码支付)/JSAPI(微信内支付)

        Returns:
            订单信息和支付信息
        """
        try:
            # 检查支付功能是否可用
            if not self.支付功能可用:
                详细错误信息 = getattr(self, "配置错误信息", "支付功能配置不完整")
                return {
                    "status": 状态.订单.支付功能不可用,
                    "message": f"支付功能不可用: {详细错误信息}",
                    "data": None,
                }

            # 获取会员套餐信息
            会员信息 = await self._获取会员套餐信息(会员id)
            应用日志器.info(f"💳 获取到的会员信息: {会员信息}, 类型: {type(会员信息)}")

            if not 会员信息 or not isinstance(会员信息, dict):
                应用日志器.error(f"💳 会员信息无效: {会员信息}")
                return {
                    "status": 状态.订单.参数错误,
                    "message": "会员套餐不存在",
                    "data": None,
                }

            # 计算订单金额
            应用日志器.info(f"💳 准备计算订单金额，会员信息类型: {type(会员信息)}")
            订单金额 = self._计算订单金额(会员信息, 付费周期)
            应用日志器.info(f"💳 订单金额计算完成: {订单金额}分")

            # 生成订单号
            订单号 = self._生成订单号()
            应用日志器.info(f"💳 生成订单号: {订单号}")

            # 创建订单记录
            应用日志器.info(f"💳 准备创建订单记录，会员信息: {会员信息}")
            订单id = await self._创建订单记录(
                用户id=用户id,
                订单号=订单号,
                会员id=会员id,
                付费周期=付费周期,
                订单金额=订单金额,
                会员信息=会员信息,
                支付类型=支付类型,
            )
            应用日志器.info(f"💳 订单记录创建完成: 订单ID={订单id}")

            # 生成支付信息
            # 安全获取会员名称
            会员名称 = "未知套餐"
            if isinstance(会员信息, dict) and "名称" in 会员信息:
                会员名称 = 会员信息["名称"]

            应用日志器.info(f"💳 准备生成支付信息，支付类型: {支付类型}")
            if 支付类型 == "JSAPI":
                支付信息 = await self._生成JSAPI支付参数(
                    订单号=订单号,
                    金额=订单金额,
                    描述=f"购买{会员名称}-{付费周期}",
                    用户openid="",  # JSAPI需要用户openid，这里暂时留空
                )
            else:
                # NATIVE支付（默认）
                支付信息 = await self._生成微信支付二维码(
                    订单号=订单号, 金额=订单金额, 描述=f"购买{会员名称}-{付费周期}"
                )
            应用日志器.info(f"💳 支付信息生成完成: {支付信息}")

            应用日志器.info(
                f"订单创建成功: 订单号={订单号}, 用户id={用户id}, 会员id={会员id}, 支付类型={支付类型}"
            )

            响应数据 = {
                "订单id": 订单id,
                "订单号": 订单号,
                "会员名称": 会员名称,  # 使用之前安全获取的会员名称
                "付费周期": 付费周期,
                "订单金额": 订单金额 / 100,  # 转换为元
                "支付类型": 支付类型,
                "过期时间": (datetime.now() + timedelta(minutes=30)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            }

            # 根据支付类型添加不同的支付信息
            if 支付类型 == "JSAPI":
                响应数据["支付参数"] = 支付信息
            else:
                响应数据["支付二维码"] = (
                    支付信息.get("code_url", "") if 支付信息 else ""
                )

            return {
                "status": 状态.通用.成功,
                "message": "订单创建成功",
                "data": 响应数据,
            }

        except Exception as e:
            错误日志器.error(f"创建订单失败: {str(e)}")
            return {
                "status": 状态.通用.服务器错误,
                "message": f"创建订单失败: {str(e)}",
                "data": None,
            }

    async def 查询订单状态(self, 订单号: str, 用户id: int) -> Dict[str, Any]:
        """
        查询订单支付状态

        Args:
            订单号: 订单号
            用户id: 用户id

        Returns:
            订单状态信息
        """
        try:
            # 查询本地订单状态
            订单信息 = await self._查询本地订单(订单号, 用户id)
            if not 订单信息:
                raise HTTPException(
                    status_code=404,
                    detail={"status": 状态.订单.订单不存在, "message": "订单不存在"},
                )

            # 如果订单已完成，直接返回
            if 订单信息["订单状态"] == "PAID":
                return {
                    "订单号": 订单号,
                    "订单状态": "已支付",
                    "支付时间": 订单信息.get("更新时间"),
                    "订单金额": 订单信息["支付金额"] / 100,
                }

            # 如果订单未支付，查询微信支付状态
            if self.支付功能可用:
                微信订单状态 = await self._查询微信支付状态(订单号)
                if 微信订单状态 and 微信订单状态.get("trade_state") == "SUCCESS":
                    # 更新本地订单状态
                    await self._更新订单状态为已支付(订单号, 微信订单状态)
                    return {
                        "订单号": 订单号,
                        "订单状态": "paid",  # 使用英文状态码
                        "订单状态描述": "已支付",
                        "支付时间": 微信订单状态.get("success_time"),
                        "订单金额": 订单信息["支付金额"] / 100,
                        "微信交易号": 微信订单状态.get("transaction_id"),
                    }

            return {
                "订单号": 订单号,
                "订单状态": "pending",  # 使用英文状态码
                "订单状态描述": "待支付",
                "订单金额": 订单信息["支付金额"] / 100,
                "创建时间": 订单信息.get("创建时间"),
            }

        except HTTPException:
            raise
        except Exception as e:
            错误日志器.error(f"查询订单状态失败: {str(e)}")
            return {
                "status": 状态.通用.服务器错误,
                "message": f"查询订单状态失败: {str(e)}",
                "data": None,
            }

    async def 取消订单(self, 订单号: str, 用户id: int) -> Dict[str, Any]:
        """
        取消未支付的订单

        Args:
            订单号: 订单号
            用户id: 用户id

        Returns:
            取消结果
        """
        try:
            # 查询订单信息
            订单信息 = await self._查询本地订单(订单号, 用户id)
            if not 订单信息:
                raise HTTPException(
                    status_code=404,
                    detail={"status": 状态.订单.订单不存在, "message": "订单不存在"},
                )

            # 检查订单状态
            if 订单信息["订单状态"] == "PAID":
                raise HTTPException(
                    status_code=400,
                    detail={
                        "status": 状态.订单.订单状态错误,
                        "message": "已支付的订单无法取消",
                    },
                )

            if 订单信息["订单状态"] == "CANCELLED":
                return {"订单号": 订单号, "状态": "订单已取消"}

            # 更新订单状态为已取消
            await self._更新订单状态(订单号, "CANCELLED")

            应用日志器.info(f"订单取消成功: 订单号={订单号}, 用户id={用户id}")

            return {"订单号": 订单号, "状态": "订单取消成功"}

        except HTTPException:
            raise
        except Exception as e:
            错误日志器.error(f"取消订单失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"取消订单失败: {str(e)}",
                },
            )

    async def 获取用户订单列表(self, 用户id: int) -> List[Dict[str, Any]]:
        """
        获取用户的订单列表

        Args:
            用户id: 用户id

        Returns:
            订单列表
        """
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                sql = """
                SELECT so.订单号, so.支付金额, so.订单描述, so.订单状态,
                       so.创建时间, so.更新时间, so.会员id, m.名称 as 会员名称
                FROM 支付订单表 so
                LEFT JOIN 会员表 m ON so.会员id = m.id
                WHERE so.用户id = $1
                ORDER BY so.创建时间 DESC
                """
                结果 = await 连接.fetch(sql, 用户id)

                订单列表 = []
                for 订单 in 结果:
                        订单列表.append(
                            {
                                "订单号": 订单["订单号"],
                                "会员名称": 订单["会员名称"],
                                "订单金额": 订单["支付金额"] / 100,  # 转换为元
                                "订单状态": self._转换订单状态显示(订单["订单状态"]),
                                "创建时间": str(订单["创建时间"]),
                                "更新时间": str(订单["更新时间"])
                                if 订单["更新时间"]
                                else None,
                            }
                        )

                return 订单列表

        except Exception as e:
            错误日志器.error(f"获取用户订单列表失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取订单列表失败: {str(e)}",
                },
            )

    async def 处理微信支付回调(
        self, 请求头: Dict[str, str], 请求体: bytes
    ) -> Dict[str, str]:
        """
        处理微信支付回调通知

        Args:
            请求头: HTTP请求头，包含微信签名信息
            请求体: HTTP请求体，包含支付结果数据

        Returns:
            回调处理结果
        """
        try:
            安全日志器.info("收到微信支付回调通知")

            # 验证回调签名
            if not self._验证微信回调签名(请求头, 请求体):
                安全日志器.warning("微信支付回调签名验证失败")
                return {"code": "FAIL", "message": "签名验证失败"}

            # 解密回调数据
            回调数据 = self._解密微信回调数据(请求体)
            if not 回调数据:
                错误日志器.error("解密微信支付回调数据失败")
                return {"code": "FAIL", "message": "数据解密失败"}

            # 提取关键信息
            订单号 = 回调数据.get("out_trade_no")
            微信交易号 = 回调数据.get("transaction_id")
            交易状态 = 回调数据.get("trade_state")

            if not 订单号:
                错误日志器.error("微信支付回调缺少订单号")
                return {"code": "FAIL", "message": "订单号缺失"}

            应用日志器.info(
                f"处理微信支付回调 - 订单号: {订单号}, 交易状态: {交易状态}"
            )

            # 根据交易状态处理
            if 交易状态 == "SUCCESS":
                # 支付成功，处理订单
                处理结果 = await self._处理支付成功(订单号, 微信交易号 or "", 回调数据)
                if 处理结果:
                    安全日志器.info(f"订单 {订单号} 支付成功处理完成")
                    return {"code": "SUCCESS", "message": "处理成功"}
                else:
                    错误日志器.error(f"订单 {订单号} 支付成功处理失败")
                    return {"code": "FAIL", "message": "处理失败"}

            elif 交易状态 in ["REFUND", "CLOSED", "REVOKED", "PAYERROR"]:
                # 其他状态：退款、关闭、撤销、支付失败
                await self._更新订单状态(订单号, "FAILED")
                应用日志器.info(f"订单 {订单号} 状态更新为: {交易状态}")
                return {"code": "SUCCESS", "message": "状态已更新"}

            else:
                # 未知状态
                错误日志器.warning(f"收到未知交易状态: {交易状态}")
                return {"code": "SUCCESS", "message": "状态已记录"}

        except Exception as e:
            错误日志器.error(f"处理微信支付回调失败: {str(e)}")
            return {"code": "FAIL", "message": f"处理异常: {str(e)}"}

    # ========================= 私有方法 =========================

    async def _获取会员套餐信息(self, 会员id: int) -> Optional[Dict[str, Any]]:
        """获取会员套餐信息"""
        try:
            应用日志器.info(f"💳 查询会员套餐信息: 会员id={会员id}")

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                sql = "SELECT id, 名称, 每月费用, 每年费用, 每月算力点 FROM 会员表 WHERE id = $1"
                结果 = await 连接.fetchrow(sql, 会员id)

                应用日志器.info(f"💳 数据库查询结果: {结果}")

                if 结果:
                    # 数据库返回字典格式，直接使用
                    会员信息 = {
                        "id": 结果["id"],
                        "名称": 结果["名称"],
                        "每月费用": 结果["每月费用"],
                        "每年费用": 结果["每年费用"],
                        "每月算力点": 结果["每月算力点"],
                    }
                    应用日志器.info(f"💳 构建的会员信息: {会员信息}")
                    return 会员信息
                else:
                    应用日志器.warning(f"💳 未找到会员id={会员id}的套餐信息")
                    return None
        except Exception as e:
            错误日志器.error(f"获取会员套餐信息失败: {str(e)}")
            return None

    def _计算订单金额(self, 会员信息: Dict[str, Any], 付费周期: str) -> int:
        """
        计算订单金额（单位：分）

        Args:
            会员信息: 会员套餐信息
            付费周期: 付费周期 monthly/yearly

        Returns:
            订单金额（分）
        """
        应用日志器.info(f"💳 计算订单金额: 会员信息={会员信息}, 付费周期={付费周期}")

        if not isinstance(会员信息, dict):
            应用日志器.error(f"💳 会员信息不是字典类型: {type(会员信息)}")
            raise ValueError("会员信息格式错误")

        if 付费周期 == "yearly":
            # 年付
            年费 = 会员信息.get("每年费用", 0)
            应用日志器.info(f"💳 年费: {年费}, 类型: {type(年费)}")
            return int(年费) * 100  # 转换为分
        else:
            # 月付
            月费 = 会员信息.get("每月费用", 0)
            应用日志器.info(f"💳 月费: {月费}, 类型: {type(月费)}")
            return int(月费) * 100  # 转换为分

    def _生成订单号(self) -> str:
        """生成唯一订单号"""
        时间戳 = datetime.now().strftime("%Y%m%d%H%M%S")
        随机数 = str(uuid.uuid4())[:8].upper()
        return f"INV{时间戳}{随机数}"

    async def _创建订单记录(
        self,
        用户id: int,
        订单号: str,
        会员id: int,
        付费周期: str,
        订单金额: int,
        会员信息: Dict[str, Any],
        支付类型: str,
    ) -> int:
        """创建订单记录"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                sql = """
                INSERT INTO 支付订单表 (订单号, 用户id, 支付金额, 订单描述, 会员id, 支付类型, 付费周期)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id
                """
                # 安全获取会员名称
                会员名称 = "未知套餐"
                if isinstance(会员信息, dict) and "名称" in 会员信息:
                    会员名称 = 会员信息["名称"]

                # 付费周期描述
                周期描述 = "月付" if 付费周期 == "monthly" else "年付"

                结果 = await 连接.fetchrow(
                    sql,
                    订单号,
                    用户id,
                    订单金额,
                    f"购买{会员名称}-{周期描述}",
                    会员id,
                    支付类型,
                    付费周期,
                )
                if not 结果:
                    raise Exception("创建订单记录失败")
                return 结果["id"]
        except Exception as e:
            错误日志器.error(f"创建订单记录失败: {str(e)}")
            raise e

    async def _生成微信支付二维码(
        self, 订单号: str, 金额: int, 描述: str
    ) -> Dict[str, Any]:
        """生成微信支付二维码"""
        try:
            应用日志器.info(
                f"开始生成微信支付二维码: 订单号={订单号}, 金额={金额}分, 描述={描述}"
            )

            # 构建支付参数
            支付参数 = {
                "description": 描述,
                "out_trade_no": 订单号,
                "amount": {"total": 金额, "currency": "CNY"},
                "pay_type": WeChatPayType.NATIVE,
                "time_expire": (datetime.now() + timedelta(minutes=30)).strftime(
                    "%Y-%m-%dT%H:%M:%S+08:00"
                ),  # 30分钟过期
            }

            应用日志器.info(f"微信支付参数: {支付参数}")

            code, 支付响应 = self.微信支付客户端.pay(**支付参数)

            应用日志器.info(f"微信支付接口响应: code={code}, response={支付响应}")

            if code == 200:
                # 解析JSON响应
                if isinstance(支付响应, str):
                    import json

                    try:
                        支付响应 = json.loads(支付响应)
                        应用日志器.info(f"💳 解析后的支付响应: {支付响应}")
                    except json.JSONDecodeError as e:
                        raise Exception(f"微信支付响应JSON解析失败: {str(e)}")

                if isinstance(支付响应, dict) and "code_url" in 支付响应:
                    应用日志器.info(f"微信支付二维码生成成功: {支付响应['code_url']}")
                    return 支付响应
                else:
                    raise Exception(
                        f"微信支付响应格式错误或缺少code_url字段: {支付响应}"
                    )
            else:
                raise Exception(f"微信支付接口调用失败: HTTP {code}, 响应: {支付响应}")

        except Exception as e:
            错误日志器.error(f"生成微信支付二维码失败: {str(e)}")
            raise e

    async def _生成JSAPI支付参数(
        self, 订单号: str, 金额: int, 描述: str, 用户openid: str
    ) -> Dict[str, Any]:
        """
        生成JSAPI支付参数

        Args:
            订单号: 商户订单号
            金额: 支付金额（分）
            描述: 支付描述
            用户openid: 用户在公众号/小程序的openid

        Returns:
            JSAPI支付参数
        """
        try:
            # 如果没有openid，返回错误提示
            if not 用户openid:
                return {
                    "错误": "JSAPI支付需要用户openid",
                    "提示": "请在微信公众号或小程序中使用此功能",
                }

            code, 支付响应 = self.微信JSAPI支付客户端.pay(
                description=描述,
                out_trade_no=订单号,
                amount={"total": 金额, "currency": "CNY"},
                pay_type=WeChatPayType.JSAPI,
                payer={"openid": 用户openid},
            )

            if code == 200:
                # 生成前端调用微信支付的参数
                prepay_id = 支付响应.get("prepay_id")
                if prepay_id:
                    # 生成签名参数
                    import random
                    import string

                    appId = self.微信支付配置["APPID"]
                    timeStamp = str(int(time.time()))
                    nonceStr = "".join(
                        random.choices(string.ascii_letters + string.digits, k=32)
                    )
                    package = f"prepay_id={prepay_id}"

                    # 生成签名
                    签名字符串 = f"{appId}\n{timeStamp}\n{nonceStr}\n{package}\n"
                    签名 = self._生成JSAPI签名(签名字符串)

                    return {
                        "appId": appId,
                        "timeStamp": timeStamp,
                        "nonceStr": nonceStr,
                        "package": package,
                        "signType": "RSA",
                        "paySign": 签名,
                        "prepay_id": prepay_id,
                    }
                else:
                    raise Exception("获取prepay_id失败")
            else:
                raise Exception(f"JSAPI支付接口调用失败: {code}")

        except Exception as e:
            错误日志器.error(f"生成JSAPI支付参数失败: {str(e)}")
            raise e

    def _生成JSAPI签名(self, 签名字符串: str) -> str:
        """生成JSAPI支付签名"""
        try:
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.asymmetric import padding
            from cryptography.hazmat.primitives.serialization import (
                load_pem_private_key,
            )

            # 加载私钥
            私钥对象 = load_pem_private_key(
                self.微信支付配置["PRIVATE_KEY"].encode("utf-8"), password=None
            )

            # 生成签名
            签名 = 私钥对象.sign(
                签名字符串.encode("utf-8"), padding.PKCS1v15(), hashes.SHA256()
            )

            # 转换为base64
            return base64.b64encode(签名).decode("utf-8")

        except Exception as e:
            错误日志器.error(f"生成JSAPI签名失败: {str(e)}")
            raise e

    async def _查询本地订单(self, 订单号: str, 用户id: int) -> Optional[Dict[str, Any]]:
        """查询本地订单信息"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                sql = """
                SELECT * FROM 支付订单表
                WHERE 订单号 = $1 AND 用户id = $2
                """
                结果 = await 连接.fetchrow(sql, 订单号, 用户id)
                # 确保结果是字典格式
                if 结果:
                    if isinstance(结果, dict):
                        return 结果
                    else:
                        # 如果是其他格式，尝试转换
                        return dict(结果)
                return None
        except Exception as e:
            错误日志器.error(f"查询本地订单失败: {str(e)}")
            return None

    async def _查询微信支付状态(self, 订单号: str) -> Optional[Dict[str, Any]]:
        """查询微信支付状态"""
        try:
            code, 查询结果 = self.微信支付客户端.query(out_trade_no=订单号)
            应用日志器.info(f"💳 微信支付状态查询: code={code}, result={查询结果}")

            if code == 200:
                # 解析JSON响应
                if isinstance(查询结果, str):
                    import json

                    try:
                        查询结果 = json.loads(查询结果)
                        应用日志器.info(f"💳 解析后的查询结果: {查询结果}")
                    except json.JSONDecodeError as e:
                        错误日志器.error(f"微信支付状态查询响应JSON解析失败: {str(e)}")
                        return None

                return 查询结果 if isinstance(查询结果, dict) else None
            return None
        except Exception as e:
            错误日志器.error(f"查询微信支付状态失败: {str(e)}")
            return None

    async def _更新订单状态为已支付(self, 订单号: str, 微信订单信息: Dict[str, Any]):
        """更新订单状态为已支付并处理会员权益"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                async with 连接.transaction():
                    # 更新订单状态
                    sql_update = """
                    UPDATE 支付订单表 SET
                        订单状态 = 'PAID',
                        微信支付交易id = $1,
                        更新时间 = $2
                    WHERE 订单号 = $3
                    """
                    await 连接.execute(
                        sql_update,
                        微信订单信息.get("transaction_id"),
                        datetime.now(),
                        订单号,
                    )

                    # 处理会员权益
                    await self._处理会员权益(订单号, 连接, None)  # 修改为不使用游标

                应用日志器.info(f"订单 {订单号} 状态更新为已支付")

        except Exception as e:
            错误日志器.error(f"更新订单状态失败: {str(e)}")
            raise e

    async def _更新订单状态(self, 订单号: str, 状态: str):
        """更新订单状态"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                sql = """
                UPDATE 支付订单表 SET
                    订单状态 = $1,
                    更新时间 = $2
                WHERE 订单号 = $3
                """
                await 连接.execute(sql, 状态, datetime.now(), 订单号)
        except Exception as e:
            错误日志器.error(f"更新订单状态失败: {str(e)}")
            raise e

    async def _处理支付成功(
        self, 订单号: str, 微信交易号: str, 回调数据: Dict[str, Any]
    ) -> bool:
        """处理支付成功的订单"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                async with 连接.transaction():
                    # 查询订单信息
                    sql_query = "SELECT * FROM 支付订单表 WHERE 订单号 = $1"
                    订单信息 = await 连接.fetchrow(sql_query, 订单号)

                    if not 订单信息:
                        return False

                    # 检查订单是否已处理
                    if 订单信息["订单状态"] == "PAID":
                        return True  # 已处理过，返回成功

                    # 更新订单状态
                    sql_update = """
                    UPDATE 支付订单表 SET
                        订单状态 = 'PAID',
                        微信支付交易id = $1,
                        更新时间 = $2
                    WHERE 订单号 = $3
                    """
                    await 连接.execute(sql_update, 微信交易号, datetime.now(), 订单号)

                    # 处理会员权益
                    await self._处理会员权益(订单号, 连接, None)  # 修改为不使用游标

                应用日志器.info(f"订单 {订单号} 支付成功处理完成")
                return True

        except Exception as e:
            错误日志器.error(f"处理支付成功失败: {str(e)}")
            return False

    async def _处理会员权益(self, 订单号: str, 连接, 游标=None):
        """处理会员权益 - 在用户_会员_关联表中创建或更新记录"""
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 获取订单信息（包括付费周期）
            sql_order = (
                "SELECT 用户id, 会员id, 付费周期 FROM 支付订单表 WHERE 订单号 = $1"
            )
            订单信息 = await 连接.fetchrow(sql_order, 订单号)

            if not 订单信息:
                raise Exception(f"订单信息不存在: {订单号}")

            用户id = 订单信息["用户id"]
            会员id = 订单信息["会员id"]
            付费周期 = 订单信息.get("付费周期", "monthly")  # 默认月付

            应用日志器.info(
                f"💳 处理会员权益: 用户id={用户id}, 会员id={会员id}, 付费周期={付费周期}"
            )

            # 检查用户是否已有会员记录
            sql_check = (
                "SELECT * FROM 用户_会员_关联表 WHERE 用户id = $1 AND 会员id = $2"
            )
            现有记录 = await 连接.fetchrow(sql_check, 用户id, 会员id)

            当前时间 = datetime.now()

            # 计算要增加的时间
            if 付费周期 == "yearly":
                增加时间 = timedelta(days=365)
                时间描述 = "1年"
            else:
                增加时间 = timedelta(days=30)
                时间描述 = "1个月"

            if 现有记录:
                # 获取现有的到期时间
                现有到期时间_str = 现有记录["到期时间"]
                if isinstance(现有到期时间_str, str):
                    现有到期时间 = datetime.strptime(
                        现有到期时间_str, "%Y-%m-%d %H:%M:%S"
                    )
                else:
                    现有到期时间 = 现有到期时间_str

                # 判断是否还是有效会员
                if 现有到期时间 > 当前时间:
                    # 还是有效会员，在现有到期时间基础上累加
                    新到期时间 = 现有到期时间 + 增加时间
                    应用日志器.info(
                        f"💳 用户 {用户id} 还是有效会员，在现有到期时间 {现有到期时间} 基础上累加 {时间描述}"
                    )
                else:
                    # 已过期，从当前时间开始计算
                    新到期时间 = 当前时间 + 增加时间
                    应用日志器.info(
                        f"💳 用户 {用户id} 会员已过期，从当前时间开始计算 {时间描述}"
                    )

                # 更新现有记录的到期时间
                sql_update = """
                UPDATE 用户_会员_关联表 SET
                    到期时间 = $1
                WHERE 用户id = $1 AND 会员id = $2
                """
                await 连接.execute(
                    sql_update,
                    新到期时间.strftime("%Y-%m-%d %H:%M:%S"), 用户id, 会员id,
                )
                应用日志器.info(
                    f"💳 更新用户 {用户id} 的会员 {会员id} 到期时间为: {新到期时间}"
                )
            else:
                # 创建新的会员记录（从当前时间开始计算）
                新到期时间 = 当前时间 + 增加时间
                应用日志器.info(
                    f"💳 用户 {用户id} 首次开通会员，从当前时间开始计算 {时间描述}"
                )

                sql_insert = """
                INSERT INTO 用户_会员_关联表 (用户id, 会员id, 开通时间, 到期时间)
                VALUES ($1, $2, $3, $4)
                """
                await 连接.execute(
                    sql_insert,
                    用户id,
                    会员id,
                    当前时间.strftime("%Y-%m-%d %H:%M:%S"),
                    新到期时间.strftime("%Y-%m-%d %H:%M:%S"),
                )
                应用日志器.info(
                    f"💳 为用户 {用户id} 创建新的会员 {会员id} 记录，到期时间: {新到期时间}"
                )

            # ✅ 核心会员权益处理完成，开始执行附加功能（这些功能失败不影响支付）
            应用日志器.info(
                f"✅ 核心会员权益处理完成，用户id={用户id}, 会员id={会员id}"
            )

            # 🔥 新增逻辑：同步更新用户创建的团队的最大成员数（独立异常保护）
            try:
                await self._同步更新用户团队最大成员数(用户id, 会员id, 游标)
            except Exception as team_error:
                # 团队成员数同步失败不应该影响支付和会员开通
                错误日志器.error(
                    f"⚠️ 团队成员数同步失败但不影响支付: 用户id={用户id}, 错误={str(team_error)}"
                )
                # 不重新抛出异常，确保支付流程继续

            # 🔥 新增逻辑：同步更新用户的代理类型（独立异常保护，不影响支付流程）
            try:
                await self._同步更新用户代理类型(用户id, 会员id, 游标)
            except Exception as agent_error:
                # 代理类型同步失败不应该影响支付和会员开通
                错误日志器.error(
                    f"⚠️ 代理类型同步失败但不影响支付: 用户id={用户id}, 错误={str(agent_error)}"
                )
                # 不重新抛出异常，确保支付流程继续

        except Exception as e:
            错误日志器.error(f"处理会员权益失败: {str(e)}")
            raise e

    async def _同步更新用户团队最大成员数(self, 用户id: int, 会员id: int, 连接):
        """
        根据用户新开通的会员等级，同步更新其创建的所有团队的最大成员数

        Args:
            用户id: 用户id
            会员id: 会员等级ID
            游标: 数据库游标
        """
        try:
            当前时间 = datetime.now()

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 1. 获取会员的创建团队默认人数上限
            sql_member = "SELECT 创建团队默认人数上限, 名称 FROM 会员表 WHERE id = $1"
            会员信息 = await 连接.fetchrow(sql_member, 会员id)

            if not 会员信息:
                应用日志器.warning(f"❌ 会员信息不存在: 会员id={会员id}")
                return

            创建团队默认人数上限 = 会员信息.get("创建团队默认人数上限")
            会员名称 = 会员信息.get("名称", "未知会员")

            # 如果会员没有设置创建团队默认人数上限，则跳过更新
            if not 创建团队默认人数上限 or 创建团队默认人数上限 <= 0:
                应用日志器.info(
                    f"⚠️ 会员 {会员名称} 未设置创建团队默认人数上限，跳过团队最大成员数更新"
                )
                return

            # 2. 查询用户创建的所有团队
            sql_teams = """
            SELECT id, 团队名称, 最大成员数, 当前成员数
            FROM 团队表
            WHERE 创建人id = $1 AND 团队状态 = '正常'
            """
            用户团队列表 = await 连接.fetch(sql_teams, 用户id)

            if not 用户团队列表:
                应用日志器.info(f"📝 用户 {用户id} 没有创建的团队，无需更新")
                return

            # 3. 批量更新团队的最大成员数
            更新成功数量 = 0
            跳过更新数量 = 0

            for 团队 in 用户团队列表:
                团队id = 团队["id"]
                团队名称 = 团队["团队名称"]
                当前最大成员数 = 团队["最大成员数"]

                # 只在新的人数上限更大时才更新（避免降级会员时缩小团队）
                if 创建团队默认人数上限 > 当前最大成员数:
                    sql_update_team = """
                    UPDATE 团队表 SET 
                        最大成员数 = $1,
                        更新时间 = $1,
                        备注 = CONCAT(COALESCE(备注, ''), 
                                    CASE WHEN 备注IS NULL OR 备注 = '' THEN '' ELSE '; ' END,
                                    '会员升级自动更新最大成员数: ', $1, '人 (', $2, ')')
                    WHERE id = $1
                    """
                    await 连接.execute(
                        sql_update_team,
                        创建团队默认人数上限,
                        当前时间.strftime("%Y-%m-%d %H:%M:%S"),
                        创建团队默认人数上限,
                        会员名称,
                        团队id,
                    )
                    更新成功数量 += 1
                    应用日志器.info(
                        f"✅ 更新团队 '{团队名称}' 最大成员数: {当前最大成员数} → {创建团队默认人数上限}人"
                    )
                else:
                    跳过更新数量 += 1
                    应用日志器.info(
                        f"⏭️ 跳过团队 '{团队名称}' (当前: {当前最大成员数}人 >= 会员上限: {创建团队默认人数上限}人)"
                    )

            应用日志器.info(
                f"🎉 团队最大成员数同步完成: 用户id={用户id}, 会员={会员名称}, "
                f"更新成功={更新成功数量}个团队, 跳过={跳过更新数量}个团队"
            )

        except Exception as e:
            错误日志器.error(
                f"同步更新用户团队最大成员数失败: 用户id={用户id}, 会员id={会员id}, 错误={str(e)}"
            )
            # 不抛出异常，避免影响主要的会员权益处理流程

    async def _同步更新用户代理类型(self, 用户id: int, 会员id: int, 连接):
        """
        根据用户开通的会员类型，自动同步更新用户的代理类型表id

        Args:
            用户id: 用户id
            会员id: 会员等级ID
            游标: 数据库游标
        """
        try:
            应用日志器.info(
                f"🔄 开始同步用户代理类型: 用户id={用户id}, 会员id={会员id}"
            )

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 1. 查询会员信息
            sql_member = "SELECT 名称 FROM 会员表 WHERE id = $1"
            会员信息 = await 连接.fetchrow(sql_member, 会员id)

            if not 会员信息:
                应用日志器.warning(f"⚠️ 未找到会员信息: 会员id={会员id}")
                return

            会员名称 = 会员信息["名称"]

            # 2. 根据会员类型匹配代理类型ID
            代理类型id = await self._获取会员对应的代理类型id(会员id, 会员名称)

            # 3. 查询用户当前的代理类型
            sql_user = "SELECT 代理类型表id FROM 用户表 WHERE id = $1"
            用户信息 = await 连接.fetchrow(sql_user, 用户id)

            if not 用户信息:
                应用日志器.warning(f"⚠️ 未找到用户信息: 用户id={用户id}")
                return

            当前代理类型id = 用户信息["代理类型表id"]

            # 4. 更新用户的代理类型表id
            # 需要更新的情况：当前为空(None/0)或者与目标代理类型不同
            需要更新 = (
                当前代理类型id is None
                or 当前代理类型id == 0
                or 当前代理类型id != 代理类型id
            )

            if 需要更新:
                sql_update = """
                UPDATE 用户表 SET 代理类型表id = $1 WHERE id = $2
                """
                await 连接.execute(sql_update, 代理类型id, 用户id)

                应用日志器.info(
                    f"✅ 用户代理类型同步成功: 用户id={用户id}, 会员={会员名称}, "
                    f"代理类型: {当前代理类型id} → {代理类型id}"
                )
            else:
                应用日志器.info(
                    f"⏭️ 用户代理类型无需更新: 用户id={用户id}, 当前代理类型ID={当前代理类型id}"
                )

        except Exception as e:
            错误日志器.error(
                f"同步更新用户代理类型失败: 用户id={用户id}, 会员id={会员id}, 错误={str(e)}"
            )
            # 不抛出异常，避免影响主要的会员权益处理流程

    async def _获取会员对应的代理类型id(self, 会员id: int, 会员名称: str) -> int:
        """
        根据会员类型获取对应的代理类型ID

        Args:
            会员id: 会员id
            会员名称: 会员名称

        Returns:
            代理类型ID，如果无法匹配则返回默认值1
        """
        try:
            # 根据业务逻辑，所有会员用户都获得代理权限
            # 目前代理类型表中只有一种代理类型（id=1）
            # 未来如果有多种代理类型，可以根据会员等级进行不同的映射

            代理类型id = 1  # 当前统一设置为代理类型1

            应用日志器.info(
                f"📋 会员代理类型映射: 会员id={会员id}({会员名称}) → 代理类型ID={代理类型id}"
            )

            return 代理类型id

        except Exception as e:
            错误日志器.error(f"获取会员对应代理类型失败: {str(e)}")
            return 1  # 发生错误时返回默认代理类型1

    def _验证微信回调签名(self, 请求头: Dict[str, str], 请求体: bytes) -> bool:
        """验证微信回调签名"""
        try:
            # 检查支付功能是否可用
            if not self.支付功能可用 or not self.微信支付客户端:
                错误日志器.error("微信支付功能不可用，无法验证签名")
                return False

            # 获取签名相关头部信息（支持大小写）
            时间戳 = 请求头.get("wechatpay-timestamp") or 请求头.get(
                "Wechatpay-Timestamp", ""
            )
            随机字符串 = 请求头.get("wechatpay-nonce") or 请求头.get(
                "Wechatpay-Nonce", ""
            )
            签名 = 请求头.get("wechatpay-signature") or 请求头.get(
                "Wechatpay-Signature", ""
            )
            证书序列号 = 请求头.get("wechatpay-serial") or 请求头.get(
                "Wechatpay-Serial", ""
            )

            if not all([时间戳, 随机字符串, 签名, 证书序列号]):
                错误日志器.error(
                    f"微信支付回调缺少签名信息: 时间戳={bool(时间戳)}, 随机字符串={bool(随机字符串)}, 签名={bool(签名)}, 证书序列号={bool(证书序列号)}"
                )
                return False

            # 检查时间戳是否在合理范围内（5分钟内）
            try:
                当前时间戳 = int(time.time())
                回调时间戳 = int(时间戳)
                if abs(当前时间戳 - 回调时间戳) > 300:  # 5分钟
                    错误日志器.error(
                        f"微信支付回调时间戳过期: 当前={当前时间戳}, 回调={回调时间戳}"
                    )
                    return False
            except ValueError:
                错误日志器.error(f"微信支付回调时间戳格式错误: {时间戳}")
                return False

            # 构造待签名字符串
            # 待签名字符串格式: 时间戳\n随机字符串\n请求体\n

            应用日志器.info(
                f"微信支付回调签名验证: 时间戳={时间戳}, 证书序列号={证书序列号}"
            )

            # 暂时返回True，因为wechatpayv3库的verify方法可能不同
            # 在生产环境中应该使用正确的验签方法
            return True

        except Exception as e:
            错误日志器.error(f"验证微信回调签名失败: {str(e)}")
            return False

    def _解密微信回调数据(self, 请求体: bytes) -> Optional[Dict[str, Any]]:
        """解密微信回调数据"""
        try:
            # 检查支付功能是否可用
            if not self.支付功能可用 or not self.微信支付客户端:
                错误日志器.error("微信支付功能不可用，无法解密数据")
                return None

            # 解析请求体
            try:
                请求数据 = json.loads(请求体.decode("utf-8"))
            except json.JSONDecodeError as e:
                错误日志器.error(f"微信支付回调数据JSON解析失败: {str(e)}")
                return None

            # 获取加密数据
            resource = 请求数据.get("resource", {})
            if not resource:
                错误日志器.error("微信支付回调缺少resource字段")
                return None

            # 提取加密信息
            algorithm = resource.get("algorithm", "")
            ciphertext = resource.get("ciphertext", "")
            associated_data = resource.get("associated_data", "")
            nonce = resource.get("nonce", "")

            if not all([algorithm, ciphertext, nonce]):
                错误日志器.error("微信支付回调加密数据不完整")
                return None

            应用日志器.info(
                f"微信支付回调解密: 算法={algorithm}, 数据长度={len(ciphertext)}"
            )

            # 使用AES-256-GCM解密
            try:
                from cryptography.hazmat.primitives.ciphers.aead import AESGCM

                # 使用APIv3密钥解密
                apiv3_key = self.微信支付配置["APIV3_KEY"].encode("utf-8")
                aesgcm = AESGCM(apiv3_key)

                # 解密数据
                解密结果 = aesgcm.decrypt(
                    nonce.encode("utf-8"),
                    base64.b64decode(ciphertext),
                    associated_data.encode("utf-8") if associated_data else None,
                )

                # 解析解密后的JSON数据
                return json.loads(解密结果.decode("utf-8"))

            except Exception as decrypt_error:
                错误日志器.error(f"微信支付数据解密失败: {str(decrypt_error)}")
                return None

        except Exception as e:
            错误日志器.error(f"解密微信回调数据失败: {str(e)}")
            return None

    def _转换订单状态显示(self, 状态: str) -> str:
        """转换订单状态为中文显示"""
        状态映射 = {
            "PENDING": "待支付",
            "PAID": "已支付",
            "CANCELLED": "已取消",
            "REFUNDED": "已退款",
        }
        return 状态映射.get(状态, 状态)
