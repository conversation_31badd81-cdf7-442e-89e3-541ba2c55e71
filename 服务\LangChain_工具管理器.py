"""
LangChain工具管理器

功能：
1. 工具注册和管理
2. 工具调用和执行
3. MCP工具集成
4. 自定义工具支持
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional

# 静态导入所有依赖 - 避免动态导入
from 数据.LangChain_工具数据层 import LangChain工具数据层, LangChain工具数据层实例

# 配置日志
工具日志器 = logging.getLogger("LangChain.工具管理器")

# LangChain工具组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain_community.tools import DuckDuckGoSearchRun  # type: ignore
    from langchain_core.tools import BaseTool, tool  # type: ignore

    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符类
    class BaseTool:  # type: ignore
        def run(self, *_args, **_kwargs):
            return "工具不可用"

    def tool(func):  # type: ignore
        return func

    class CallbackManagerForToolRun:  # type: ignore
        pass

    class DuckDuckGoSearchRun:  # type: ignore
        def run(self, _query: str) -> str:
            return "搜索功能不可用"


# MCP工具组件
MCP_AVAILABLE = False
try:
    from langchain_mcp_adapters.client import MultiServerMCPClient
    from langchain_mcp_adapters.tools import load_mcp_tools

    MCP_AVAILABLE = True
except ImportError:
    工具日志器.warning("MCP工具库不可用，将使用内置MCP客户端实现")

    # 内置MCP客户端实现
    class MultiServerMCPClient:  # type: ignore
        """内置MCP客户端实现 - 当langchain_mcp_adapters不可用时使用"""

        def __init__(self, server_configs: Dict[str, Any]):
            self.server_configs = server_configs
            self.connected_servers = {}
            self.tools_cache = {}
            工具日志器.info(
                f"初始化内置MCP客户端，配置了 {len(server_configs)} 个服务器"
            )

        async def get_tools(self):
            """获取所有MCP工具"""
            try:
                all_tools = []

                for server_name, config in self.server_configs.items():
                    try:
                        # 模拟MCP工具加载
                        server_tools = await self._load_server_tools(
                            server_name, config
                        )
                        all_tools.extend(server_tools)
                        工具日志器.debug(
                            f"从服务器 {server_name} 加载了 {len(server_tools)} 个工具"
                        )
                    except Exception as e:
                        工具日志器.error(
                            f"从服务器 {server_name} 加载工具失败: {str(e)}"
                        )
                        continue

                工具日志器.info(f"MCP客户端总共加载了 {len(all_tools)} 个工具")
                return all_tools

            except Exception as e:
                工具日志器.error(f"获取MCP工具失败: {str(e)}")
                return []

        async def _load_server_tools(self, server_name: str, config: Dict[str, Any]):
            """从单个服务器加载工具"""
            try:
                # 这里可以实现真实的MCP协议通信
                # 目前返回模拟工具，实际使用时需要根据MCP协议实现
                transport = config.get("transport", "stdio")

                if transport == "stdio":
                    return await self._load_stdio_tools(server_name, config)
                elif transport == "http":
                    return await self._load_http_tools(server_name, config)
                else:
                    工具日志器.warning(f"不支持的传输协议: {transport}")
                    return []

            except Exception as e:
                工具日志器.error(f"加载服务器 {server_name} 工具失败: {str(e)}")
                return []

        async def _load_stdio_tools(self, server_name: str, config: Dict[str, Any]):
            """加载stdio传输的MCP工具"""
            # 实际实现需要启动子进程并通过stdio与MCP服务器通信
            # 这里返回示例工具
            return [
                MCPTool(
                    name=f"{server_name}_example_tool",
                    description=f"来自 {server_name} 的示例工具",
                    server_name=server_name,
                    config=config,
                )
            ]

        async def _load_http_tools(self, server_name: str, config: Dict[str, Any]):
            """加载HTTP传输的MCP工具"""
            # 实际实现需要通过HTTP与MCP服务器通信
            # 这里返回示例工具
            return [
                MCPTool(
                    name=f"{server_name}_http_tool",
                    description=f"来自 {server_name} 的HTTP工具",
                    server_name=server_name,
                    config=config,
                )
            ]

    class MCPTool:
        """MCP工具包装类"""

        def __init__(
            self, name: str, description: str, server_name: str, config: Dict[str, Any]
        ):
            self.name = name
            self.description = description
            self.server_name = server_name
            self.config = config

        async def run(self, *args, **kwargs):
            """执行MCP工具"""
            try:
                # 实际实现需要调用MCP服务器
                # 这里返回模拟结果
                工具日志器.info(f"执行MCP工具: {self.name}, 参数: {args}, {kwargs}")
                return f"MCP工具 {self.name} 执行结果 (模拟)"
            except Exception as e:
                工具日志器.error(f"MCP工具 {self.name} 执行失败: {str(e)}")
                return f"MCP工具执行失败: {str(e)}"

    async def load_mcp_tools(*args, **kwargs):  # type: ignore
        """加载MCP工具的兼容函数"""
        工具日志器.info("使用内置MCP工具加载器")
        return []


class 简单工具:
    """简单工具实现"""

    def __init__(self, name: str, description: str, func: Callable):
        self.name = name
        self.description = description
        self.func = func

    async def run(self, *args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(self.func):
                return await self.func(*args, **kwargs)
            else:
                return self.func(*args, **kwargs)
        except Exception as e:
            return f"工具执行错误: {str(e)}"


class LangChain工具管理器:
    """LangChain工具管理器 - 企业级工具管理系统

    功能特性：
    - 工具注册与管理：支持多种工具类型（LangChain、MCP、自定义工具）
    - 性能监控：实时统计、热度评分、缓存命中率分析
    - 智能缓存：基于使用模式的预测缓存和热度排序
    - 安全控制：权限验证、安全级别管理
    - 容错机制：超时控制、重试机制、异常处理
    - 数据持久化：统计数据、调用日志、配置管理

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于工具管理业务逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    5. 高性能 - 智能缓存、并发控制、资源优化
    6. 高可用 - 容错机制、重试策略、降级处理
    7. 可观测 - 详细日志、性能指标、监控告警
    """

    def __init__(self, 工具数据层: Optional[LangChain工具数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            工具数据层: LangChain工具数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保工具数据层永远不为None
        self.工具数据层: LangChain工具数据层 = 工具数据层 or LangChain工具数据层实例
        self.工具注册表 = {}  # 工具名称 -> 工具实例 (运行时缓存)
        self.工具配置 = {}  # 工具名称 -> 配置信息 (运行时缓存)
        self.已初始化 = True  # 简化初始化逻辑

        # MCP工具管理
        self.MCP客户端: Optional[MultiServerMCPClient] = None
        self.MCP工具缓存 = {}  # MCP工具名称 -> 工具实例
        self.MCP服务器配置 = {}  # 服务器名称 -> 配置信息

        # 性能监控和缓存系统
        self.工具性能统计 = {}  # 工具名称 -> 性能统计信息
        self.工具调用历史 = {}  # 工具名称 -> 最近调用历史
        self.缓存策略 = "LRU"  # 缓存策略：LRU, LFU, TTL
        self.最大缓存大小 = 100  # 最大缓存工具数量
        self.缓存TTL = 3600  # 缓存生存时间（秒）

        # 高级缓存功能
        self.工具热度评分 = {}  # 工具名称 -> 热度评分
        self.缓存命中统计 = {"命中次数": 0, "未命中次数": 0}  # 缓存命中率统计
        self.预加载工具列表 = []  # 预加载的热门工具列表
        self.智能预测缓存 = {}  # 基于使用模式的预测缓存

        # 并发控制
        self.工具调用锁 = {}  # 工具名称 -> 异步锁
        self.最大并发数 = 10  # 最大并发工具调用数
        self.当前并发数 = 0

        # 内存管理
        self.内存使用统计 = {"工具实例数": 0, "缓存大小": 0}
        self.自动清理间隔 = 1200  # 自动清理间隔（秒）- 调整为20分钟
        self.上次清理时间 = datetime.now()

        工具日志器.info("LangChain工具管理器创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain工具管理器":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保工具数据层已初始化
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 创建管理器实例
        实例 = cls(LangChain工具数据层实例)

        # 执行异步初始化逻辑
        await 实例._异步初始化()

        return 实例

    async def _异步初始化(self):
        """内部异步初始化方法"""
        try:
            # 从数据库加载工具配置
            await self._从数据库加载工具配置()

            # 注册默认工具
            await self._注册默认工具()

            # 初始化内部函数工具
            await self._初始化内部函数工具()

            # 初始化MCP工具
            await self._初始化MCP工具()

            # 初始化性能监控
            await self._初始化性能监控()

            工具日志器.info("✅ LangChain工具管理器异步初始化成功")

        except Exception as e:
            工具日志器.error(f"❌ LangChain工具管理器异步初始化失败: {str(e)}")
            raise

    async def _从数据库加载工具配置(self):
        """从数据库加载工具配置 - 统一的工具配置加载逻辑"""
        try:
            if not self.工具数据层:
                工具日志器.warning("工具数据层未初始化，跳过数据库加载")
                return

            # 获取所有启用的工具配置
            工具配置列表 = await self.工具数据层.获取工具配置列表(启用状态=True)

            加载成功数量 = 0
            for 工具配置 in 工具配置列表:
                if await self._加载单个工具配置(工具配置):
                    加载成功数量 += 1

            工具日志器.info(
                f"✅ 从数据库加载了 {加载成功数量}/{len(工具配置列表)} 个工具配置"
            )

        except Exception as e:
            工具日志器.error(f"从数据库加载工具配置失败: {str(e)}")

    async def _加载单个工具配置(self, 工具配置: Dict[str, Any]) -> bool:
        """加载单个工具配置的统一逻辑"""
        try:
            工具名称 = 工具配置.get("工具名称")
            工具类型 = 工具配置.get("工具类型") or "内部函数工具"  # 默认为内部函数工具

            # 类型检查 - 只检查工具名称
            if not isinstance(工具名称, str):
                工具日志器.warning(f"跳过无效工具配置: 工具名称={工具名称}")
                return False

            # 将数据库配置加载到内存缓存
            self.工具配置[工具名称] = self._构建工具配置信息(工具配置)

            # 对于内部函数工具，不需要创建实例（它们由内部函数包装器管理）
            if 工具类型 == "内部函数工具":
                工具日志器.debug(f"✅ 内部函数工具配置已加载: {工具名称}")
                return True

            # 根据工具类型创建工具实例
            return await self._创建数据库工具实例(工具名称, 工具配置)

        except Exception as e:
            工具日志器.error(
                f"加载工具配置失败 ({工具配置.get('工具名称', 'unknown')}): {str(e)}"
            )
            return False

    def _构建工具配置信息(self, 工具配置: Dict[str, Any]) -> Dict[str, Any]:
        """构建标准化的工具配置信息"""
        工具类型 = 工具配置.get("工具类型") or "内部函数工具"  # 默认为内部函数工具
        return {
            "描述": 工具配置.get("工具描述", ""),
            "类型": 工具类型,
            "分类": self._获取工具分类(工具类型),
            "权限要求": 工具配置.get("权限要求", []),
            "安全级别": 工具配置.get("安全级别", 1),
            "超时时间": 工具配置.get("超时时间", 30),
            "重试次数": 工具配置.get("重试次数", 3),
            "数据库配置": True,  # 标记为数据库配置
            "注册时间": 工具配置.get("创建时间"),
            "启用状态": 工具配置.get("启用状态", True),
        }

    def _获取工具分类(self, 工具类型: str) -> str:
        """根据工具类型获取分类"""
        分类映射 = {
            "calculation": "数学计算",
            "search": "搜索工具",
            "database": "数据库工具",
            "api": "API工具",
            "file": "文件工具",
            "builtin": "系统工具",
            "custom": "自定义工具",
            "内部函数工具": "业务功能",
        }
        return 分类映射.get(工具类型, "其他工具")

    async def _创建数据库工具实例(
        self, 工具名称: str, 工具配置: Dict[str, Any]
    ) -> bool:
        """根据数据库配置创建工具实例 - 统一的工具实例创建逻辑"""
        try:
            工具类型 = 工具配置.get("工具类型") or "custom"

            # 使用工具工厂模式创建实例
            工具实例 = await self._工具工厂(工具名称, 工具类型, 工具配置)

            if 工具实例:
                self.工具注册表[工具名称] = 工具实例
                工具日志器.debug(f"✅ 创建工具实例成功: {工具名称} ({工具类型})")
                return True
            else:
                工具日志器.debug(f"⚠️ 工具类型 {工具类型} 暂不支持自动创建: {工具名称}")
                return False

        except Exception as e:
            工具日志器.error(f"创建数据库工具实例失败 ({工具名称}): {str(e)}")
            return False

    async def _工具工厂(self, 工具名称: str, 工具类型: str, 工具配置: Dict[str, Any]):
        """工具工厂 - 根据类型创建对应的工具实例"""
        try:
            if 工具类型 == "search":
                return await self._创建搜索工具(工具名称, 工具配置)
            elif 工具类型 == "calculation":
                return await self._创建计算工具(工具名称, 工具配置)
            elif 工具类型 == "text":
                return await self._创建文本工具(工具名称, 工具配置)
            elif 工具类型 in ["api", "database", "file"]:
                # 这些工具需要特殊配置，暂时返回None
                return None
            else:
                工具日志器.warning(f"未知工具类型: {工具类型}")
                return None

        except Exception as e:
            工具日志器.error(f"工具工厂创建失败 ({工具名称}): {str(e)}")
            return None

    async def _创建搜索工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建搜索工具实例"""
        if not LANGCHAIN_AVAILABLE:
            return None

        try:
            if 工具名称 == "duckduckgo_search":
                return DuckDuckGoSearchRun()
            # 可以扩展其他搜索工具
            return None
        except Exception as e:
            工具日志器.warning(f"创建搜索工具失败: {e}")
            return None

    async def _创建计算工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建计算工具实例"""
        # 计算工具通常在_注册标准LangChain工具中创建
        # 这里可以扩展其他计算工具
        return None

    async def _创建文本工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建文本处理工具实例"""
        # 可以扩展文本处理工具
        return None

    async def _注册默认工具(self):
        """注册默认工具 - 简化版本，避免与内部函数工具重复"""
        try:
            # 只注册标准LangChain工具，其他工具由内部函数包装器管理
            await self._注册标准LangChain工具()

            工具日志器.info("✅ 默认工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册默认工具失败: {str(e)}")

    async def _注册标准LangChain工具(self):
        """注册标准的LangChain工具"""
        try:
            if not LANGCHAIN_AVAILABLE:
                工具日志器.warning("LangChain不可用，跳过标准工具注册")
                return

            # 数学计算工具 - 遵循LangChain最佳实践
            @tool
            def multiply(first_int: int, second_int: int) -> int:
                """Multiply two integers together.

                Args:
                    first_int: The first integer to multiply
                    second_int: The second integer to multiply

                Returns:
                    The product of the two integers
                """
                return first_int * second_int

            @tool
            def add(first_int: int, second_int: int) -> int:
                """Add two integers together.

                Args:
                    first_int: The first integer to add
                    second_int: The second integer to add

                Returns:
                    The sum of the two integers
                """
                return first_int + second_int

            @tool
            def subtract(first_int: int, second_int: int) -> int:
                """Subtract the second integer from the first integer.

                Args:
                    first_int: The integer to subtract from
                    second_int: The integer to subtract

                Returns:
                    The difference between the two integers
                """
                return first_int - second_int

            @tool
            def divide(first_int: int, second_int: int) -> float:
                """Divide the first integer by the second integer.

                Args:
                    first_int: The dividend (number to be divided)
                    second_int: The divisor (number to divide by)

                Returns:
                    The quotient as a float

                Raises:
                    ValueError: If the divisor is zero
                """
                if second_int == 0:
                    raise ValueError("Division by zero is not allowed")
                return first_int / second_int

            # 注册LangChain工具
            await self.注册LangChain工具("multiply", multiply, {"分类": "数学计算"})
            await self.注册LangChain工具("add", add, {"分类": "数学计算"})
            await self.注册LangChain工具("subtract", subtract, {"分类": "数学计算"})
            await self.注册LangChain工具("divide", divide, {"分类": "数学计算"})

            工具日志器.info("✅ 标准LangChain工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册标准LangChain工具失败: {str(e)}")

    async def 注册工具(
        self,
        工具名称: str,
        工具实例_或_函数,
        配置: Optional[Dict[str, Any]] = None,
        覆盖已存在: bool = False,
    ) -> bool:
        """统一的工具注册方法 - 支持多种工具类型"""
        try:
            # 检查工具是否已存在
            if 工具名称 in self.工具注册表:
                if not 覆盖已存在:
                    工具日志器.info(f"工具已存在，跳过注册: {工具名称}")
                    return True  # 返回True表示工具已存在，不需要重复注册
                else:
                    工具日志器.info(f"覆盖已存在的工具: {工具名称}")

            # 统一的工具实例处理
            工具实例, 工具类型 = await self._处理工具实例(工具名称, 工具实例_或_函数)

            if not 工具实例:
                工具日志器.error(f"无法创建工具实例: {工具名称}")
                return False

            # 原子性注册：同时更新注册表和配置
            try:
                self.工具注册表[工具名称] = 工具实例
                self.工具配置[工具名称] = self._构建注册工具配置(
                    工具实例, 工具类型, 配置
                )

                工具日志器.info(f"注册工具成功: {工具名称} ({工具类型})")
                return True

            except Exception as reg_error:
                # 回滚注册
                self.工具注册表.pop(工具名称, None)
                self.工具配置.pop(工具名称, None)
                raise reg_error

        except Exception as e:
            工具日志器.error(f"注册工具失败 {工具名称}: {str(e)}")
            return False

    async def _处理工具实例(self, 工具名称: str, 工具实例_或_函数):
        """处理不同类型的工具实例"""
        try:
            # LangChain BaseTool实例
            if LANGCHAIN_AVAILABLE and isinstance(工具实例_或_函数, BaseTool):
                return 工具实例_或_函数, "LangChain工具"

            # 普通函数 - 转换为LangChain工具
            elif callable(工具实例_或_函数):
                if LANGCHAIN_AVAILABLE:
                    # 使用已导入的tool装饰器
                    工具实例 = tool(工具实例_或_函数)
                    工具实例.name = 工具名称
                    工具实例.description = 工具实例_或_函数.__doc__ or f"{工具名称}工具"
                    return 工具实例, "LangChain工具"
                else:
                    # 回退到简单工具
                    描述 = getattr(工具实例_或_函数, "__doc__", "") or f"{工具名称}工具"
                    工具实例 = 简单工具(工具名称, 描述, 工具实例_或_函数)
                    return 工具实例, "自定义工具"

            # 简单工具实例
            elif isinstance(工具实例_或_函数, 简单工具):
                return 工具实例_或_函数, "自定义工具"

            else:
                工具日志器.error(f"不支持的工具类型: {type(工具实例_或_函数)}")
                return None, None

        except Exception as e:
            工具日志器.error(f"处理工具实例失败: {str(e)}")
            return None, None

    def _构建注册工具配置(
        self, 工具实例, 工具类型: str, 额外配置: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """构建注册工具的配置信息"""
        基础配置 = {
            "描述": getattr(工具实例, "description", "")
            or getattr(工具实例, "描述", ""),
            "类型": 工具类型,
            "注册时间": datetime.now(),
            "数据库配置": False,  # 标记为运行时注册
            "启用状态": True,
        }

        if 额外配置:
            基础配置.update(额外配置)

        return 基础配置

    # 保留向后兼容的方法
    async def 注册LangChain工具(
        self, 工具名称: str, 工具函数_或_实例, 配置: Optional[Dict[str, Any]] = None
    ) -> bool:
        """注册LangChain工具 - 向后兼容方法"""
        return await self.注册工具(工具名称, 工具函数_或_实例, 配置)

    async def 调用工具(
        self,
        工具名称: str,
        *参数列表,
        用户ID: int = None,
        智能体id: int = None,
        **关键字参数,
    ) -> Dict[str, Any]:
        """调用工具 - 使用统一状态码体系和性能监控"""
        from 状态 import 状态

        开始时间 = datetime.now()

        # 并发控制检查
        if self.当前并发数 >= self.最大并发数:
            return {
                "status_code": 状态.工具调用.系统繁忙,
                "success": False,
                "message": f"系统繁忙，当前并发数已达上限: {self.最大并发数}",
                "result": None,
                "execution_time": 0,
            }

        try:
            # 增加并发计数
            self.当前并发数 += 1

            # 检查工具是否存在
            if 工具名称 not in self.工具注册表:
                # 记录缓存未命中
                self.缓存命中统计["未命中次数"] += 1
                if 工具名称 in self.工具性能统计:
                    self.工具性能统计[工具名称]["缓存未命中次数"] += 1

                await self._记录工具调用统计(工具名称, False, 0, "工具不存在")
                return {
                    "status_code": 状态.工具调用.工具不存在,
                    "success": False,
                    "message": f"工具不存在: {工具名称}",
                    "result": None,
                    "execution_time": 0,
                }
            else:
                # 记录缓存命中
                self.缓存命中统计["命中次数"] += 1
                if 工具名称 in self.工具性能统计:
                    self.工具性能统计[工具名称]["缓存命中次数"] += 1

            # 获取或创建工具锁
            if 工具名称 not in self.工具调用锁:
                self.工具调用锁[工具名称] = asyncio.Lock()

            # 获取工具配置
            工具配置 = self.工具配置.get(工具名称, {})
            超时时间 = 工具配置.get("超时时间", 30)  # 默认30秒
            重试次数 = 工具配置.get("重试次数", 3)  # 默认重试3次

            开始时间 = datetime.now()

            # 使用工具锁确保线程安全
            async with self.工具调用锁[工具名称]:
                工具实例 = self.工具注册表[工具名称]

                # 实现重试机制的工具调用
                for 重试计数 in range(重试次数 + 1):  # +1 因为第一次不算重试
                    try:
                        # 使用asyncio.wait_for实现超时控制
                        执行结果 = await asyncio.wait_for(
                            self._执行单次工具调用(工具实例, 参数列表, 关键字参数),
                            timeout=超时时间,
                        )
                        # 执行成功，跳出重试循环
                        break

                    except (asyncio.TimeoutError, Exception) as error:
                        错误类型 = (
                            "超时错误"
                            if isinstance(error, asyncio.TimeoutError)
                            else "执行错误"
                        )
                        工具日志器.warning(
                            f"工具 {工具名称} 第 {重试计数 + 1} 次调用失败: {错误类型} - {str(error)}"
                        )

                        if 重试计数 < 重试次数:
                            # 等待一段时间后重试（指数退避）
                            等待时间 = min(2**重试计数, 10)  # 最多等待10秒
                            await asyncio.sleep(等待时间)
                            continue
                        else:
                            # 所有重试都失败了，返回错误
                            return await self._处理工具调用失败(
                                工具名称, 开始时间, 重试次数, error, 错误类型
                            )

                # 如果到这里说明执行成功了，处理成功结果
                return await self._处理工具调用成功(
                    工具名称, 开始时间, 执行结果, 用户ID, 智能体id, 参数列表, 关键字参数
                )

        except Exception as e:
            # 处理未预期的异常
            return await self._处理工具调用异常(
                工具名称, 开始时间, e, 用户ID, 智能体id, 参数列表, 关键字参数
            )
        finally:
            # 减少并发计数
            self.当前并发数 = max(0, self.当前并发数 - 1)

    async def 获取工具列表(self, 分类: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取工具列表"""
        try:
            工具列表 = []

            for 工具名称, 配置 in self.工具配置.items():
                if 分类 and 配置.get("分类") != 分类:
                    continue

                工具列表.append(
                    {
                        "工具名称": 工具名称,
                        "描述": 配置.get("描述", ""),
                        "类型": 配置.get("类型", "未知"),
                        "分类": 配置.get("分类", "其他"),
                        "注册时间": 配置.get("注册时间", "").isoformat()
                        if isinstance(配置.get("注册时间"), datetime)
                        else str(配置.get("注册时间", "")),
                    }
                )

            return 工具列表

        except Exception as e:
            工具日志器.error(f"获取工具列表失败: {str(e)}")
            return []

    async def 删除工具(self, 工具名称: str) -> bool:
        """删除工具"""
        try:
            if 工具名称 in self.工具注册表:
                del self.工具注册表[工具名称]

            if 工具名称 in self.工具配置:
                del self.工具配置[工具名称]

            工具日志器.info(f"删除工具成功: {工具名称}")
            return True

        except Exception as e:
            工具日志器.error(f"删除工具失败 {工具名称}: {str(e)}")
            return False

    # ==================== 内部函数工具集成 ====================

    async def _初始化内部函数工具(self):
        """初始化内部函数工具 - 简化版本"""
        try:
            工具日志器.info("开始初始化内部函数工具...")

            # 导入简化的内部函数包装器
            from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

            await 内部函数包装器实例.初始化()

            # 注册内部函数工具
            for 工具名称, 工具实例 in 内部函数包装器实例.已注册工具.items():
                await self.注册LangChain工具(
                    工具名称, 工具实例, {"类型": "内部函数工具", "分类": "业务功能"}
                )

            工具日志器.info(
                f"✅ 内部函数工具初始化成功，注册了 {len(内部函数包装器实例.已注册工具)} 个工具"
            )

        except Exception as e:
            工具日志器.error(f"初始化内部函数工具失败: {str(e)}")

    def 获取状态(self) -> Dict[str, Any]:
        """获取工具管理器状态"""
        内部工具数量 = sum(
            1 for 配置 in self.工具配置.values() if 配置.get("类型") == "内部函数工具"
        )

        return {
            "已初始化": self.已初始化,
            "注册工具数量": len(self.工具注册表),
            "内部函数工具数量": 内部工具数量,
            "LangChain可用": LANGCHAIN_AVAILABLE,
        }

    async def 测试工具(self, 工具名称: str, 用户id: int = None) -> Dict[str, Any]:
        """增强的工具测试功能 - 使用统一状态码体系"""
        from 状态 import 状态

        测试开始时间 = datetime.now()
        测试结果 = {
            "工具名称": 工具名称,
            "测试时间": 测试开始时间.isoformat(),
            "测试项目": {},
        }

        try:
            # 1. 基础存在性测试
            if 工具名称 not in self.工具注册表:
                return {
                    "status_code": 状态.工具调用.工具不存在,
                    "success": False,
                    "message": f"工具不存在: {工具名称}",
                    "测试结果": 测试结果,
                }

            工具实例 = self.工具注册表[工具名称]
            测试结果["测试项目"]["存在性测试"] = {"通过": True, "消息": "工具存在"}

            # 2. 配置完整性测试
            工具配置 = self.工具配置.get(工具名称, {})
            配置测试结果 = {
                "有描述": bool(工具配置.get("描述")),
                "有类型": bool(工具配置.get("类型")),
                "启用状态": 工具配置.get("启用状态", False),
            }
            测试结果["测试项目"]["配置测试"] = 配置测试结果

            # 3. 权限测试（如果提供用户ID）
            if 用户id and self.权限控制器:
                权限测试开始 = datetime.now()
                try:
                    权限检查结果 = await self.权限控制器.检查工具调用权限(
                        用户id, 工具名称
                    )
                    权限测试时间 = (datetime.now() - 权限测试开始).total_seconds()

                    测试结果["测试项目"]["权限测试"] = {
                        "通过": 权限检查结果,
                        "测试时间": f"{权限测试时间:.3f}s",
                    }
                except Exception as e:
                    测试结果["测试项目"]["权限测试"] = {"通过": False, "错误": str(e)}

            # 4. 性能测试（简单调用测试）
            性能测试开始 = datetime.now()
            try:
                # 使用安全的测试参数
                测试参数 = self._获取工具测试参数(工具名称)
                测试调用结果 = await self._安全测试调用(工具实例, 测试参数)
                性能测试时间 = (datetime.now() - 性能测试开始).total_seconds()

                测试结果["测试项目"]["性能测试"] = {
                    "通过": 测试调用结果.get("success", False),
                    "响应时间": f"{性能测试时间:.3f}s",
                    "消息": 测试调用结果.get("message", ""),
                }
            except Exception as e:
                测试结果["测试项目"]["性能测试"] = {"通过": False, "错误": str(e)}

            # 计算总测试时间
            总测试时间 = (datetime.now() - 测试开始时间).total_seconds()
            测试结果["总测试时间"] = f"{总测试时间:.3f}s"

            # 判断整体测试结果
            所有测试通过 = all(
                项目.get("通过", True) for 项目 in 测试结果["测试项目"].values()
            )

            return {
                "status_code": 状态.工具调用.测试成功
                if 所有测试通过
                else 状态.工具调用.调用失败,
                "success": 所有测试通过,
                "message": "所有测试通过" if 所有测试通过 else "部分测试失败",
                "测试结果": 测试结果,
            }

        except Exception as e:
            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"测试执行失败: {str(e)}",
                "测试结果": 测试结果,
            }

    def _获取工具测试参数(self, 工具名称: str) -> Dict[str, Any]:
        """获取工具的安全测试参数"""
        # 根据工具类型返回安全的测试参数
        工具配置 = self.工具配置.get(工具名称, {})
        工具类型 = 工具配置.get("类型", "unknown")

        if 工具类型 == "字符串处理":
            return {"text": "test"}
        elif 工具类型 == "数学计算":
            return {"a": 1, "b": 2}
        else:
            return {}

    async def _安全测试调用(self, 工具实例, 测试参数: Dict[str, Any]) -> Dict[str, Any]:
        """安全的工具测试调用 - 支持所有工具类型"""
        try:
            if isinstance(工具实例, 简单工具):
                # 对于简单工具，使用关键字参数
                结果 = await 工具实例.run(**测试参数)
            elif LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
                # 对于LangChain工具，使用字符串参数
                测试字符串 = str(测试参数) if 测试参数 else "test"
                结果 = 工具实例.run(测试字符串)
            elif isinstance(工具实例, MCPTool):
                # 对于MCP工具，使用关键字参数
                结果 = await 工具实例.run(**测试参数)
            elif hasattr(工具实例, "run"):
                # 对于其他具有run方法的工具
                if asyncio.iscoroutinefunction(工具实例.run):
                    结果 = await 工具实例.run(**测试参数)
                else:
                    结果 = 工具实例.run(**测试参数)
            else:
                return {
                    "success": False,
                    "message": f"不支持的工具类型: {type(工具实例)}",
                }

            return {"success": True, "result": 结果, "message": "测试调用成功"}
        except Exception as e:
            return {"success": False, "message": f"测试调用失败: {str(e)}"}

    async def _初始化MCP工具(self):
        """初始化MCP工具系统 - 支持内置和外部MCP客户端"""
        try:
            # 从数据库加载MCP工具配置
            MCP工具列表 = await self.工具数据层.获取MCP工具列表(启用状态=True)

            if not MCP工具列表:
                工具日志器.info("未找到启用的MCP工具配置")
                return

            # 构建MCP服务器配置
            服务器配置 = {}
            for MCP工具 in MCP工具列表:
                try:
                    import json

                    连接配置 = json.loads(MCP工具.get("连接配置", "{}"))

                    服务器名称 = MCP工具["工具名称"]
                    服务器配置[服务器名称] = {
                        "transport": 连接配置.get("transport", "stdio"),
                        "description": MCP工具.get("工具描述", ""),
                        "display_name": MCP工具.get("显示名称", 服务器名称),
                        **连接配置,
                    }

                    工具日志器.debug(f"加载MCP工具配置: {服务器名称}")

                except Exception as e:
                    工具日志器.warning(
                        f"解析MCP工具配置失败 {MCP工具['工具名称']}: {str(e)}"
                    )
                    continue

            if not 服务器配置:
                工具日志器.warning("没有有效的MCP服务器配置")
                return

            # 创建MCP客户端
            self.MCP客户端 = MultiServerMCPClient(服务器配置)
            self.MCP服务器配置 = 服务器配置

            # 加载MCP工具
            await self._加载MCP工具()

            工具日志器.info(
                f"✅ MCP工具系统初始化成功，配置了 {len(服务器配置)} 个服务器"
            )

        except Exception as e:
            工具日志器.error(f"❌ MCP工具系统初始化失败: {str(e)}")
            self.MCP客户端 = None

    async def _加载MCP工具(self):
        """加载MCP工具到工具注册表"""
        try:
            if not self.MCP客户端:
                return

            # 获取所有MCP工具
            MCP工具列表 = await self.MCP客户端.get_tools()

            加载成功数量 = 0
            for 工具 in MCP工具列表:
                try:
                    工具名称 = getattr(工具, "name", str(工具))

                    # 注册到工具注册表
                    self.工具注册表[工具名称] = 工具
                    self.工具配置[工具名称] = {
                        "类型": "MCP工具",
                        "描述": getattr(工具, "description", ""),
                        "启用状态": True,
                        "来源": "MCP",
                    }

                    # 缓存到MCP工具缓存
                    self.MCP工具缓存[工具名称] = 工具

                    加载成功数量 += 1
                    工具日志器.debug(f"注册MCP工具: {工具名称}")

                except Exception as e:
                    工具日志器.warning(f"注册MCP工具失败: {str(e)}")
                    continue

            工具日志器.info(f"✅ 成功加载 {加载成功数量} 个MCP工具")

        except Exception as e:
            工具日志器.error(f"❌ 加载MCP工具失败: {str(e)}")

    async def 重新加载MCP工具(self) -> Dict[str, Any]:
        """重新加载MCP工具 - 使用统一状态码体系"""
        from 状态 import 状态

        try:
            if not MCP_AVAILABLE:
                return {
                    "status_code": 状态.工具调用.MCP连接失败,
                    "success": False,
                    "message": "MCP工具库不可用",
                }

            # 清理现有MCP工具
            MCP工具名称列表 = [
                名称
                for 名称, 配置 in self.工具配置.items()
                if 配置.get("来源") == "MCP"
            ]
            for 工具名称 in MCP工具名称列表:
                self.工具注册表.pop(工具名称, None)
                self.工具配置.pop(工具名称, None)
                self.MCP工具缓存.pop(工具名称, None)

            # 重新初始化MCP工具
            await self._初始化MCP工具()

            return {
                "status_code": 状态.工具调用.加载成功,
                "success": True,
                "message": f"MCP工具重新加载成功，当前有 {len(self.MCP工具缓存)} 个MCP工具",
            }

        except Exception as e:
            return {
                "status_code": 状态.工具调用.MCP连接失败,
                "success": False,
                "message": f"MCP工具重新加载失败: {str(e)}",
            }

    async def _初始化性能监控(self):
        """初始化性能监控系统"""
        try:
            # 初始化所有已注册工具的性能统计
            for 工具名称 in self.工具注册表.keys():
                self._初始化工具统计(工具名称)

            # 从数据库加载历史统计数据
            await self._加载历史统计数据()

            # 初始化热度评分系统
            await self._初始化热度评分系统()

            # 启动定期清理任务
            asyncio.create_task(self._定期清理任务())

            # 启动性能监控任务
            asyncio.create_task(self._性能监控任务())

            # 启动智能缓存预测任务
            asyncio.create_task(self._智能缓存预测任务())

            工具日志器.info("✅ 性能监控系统初始化成功")

        except Exception as e:
            工具日志器.error(f"❌ 性能监控系统初始化失败: {str(e)}")

    def _初始化工具统计(self, 工具名称: str):
        """初始化单个工具的统计信息 - 消除重复代码"""
        if 工具名称 not in self.工具性能统计:
            self.工具性能统计[工具名称] = {
                "调用次数": 0,
                "成功次数": 0,
                "失败次数": 0,
                "总执行时间": 0.0,
                "平均执行时间": 0.0,
                "最小执行时间": float("inf"),
                "最大执行时间": 0.0,
                "最近调用时间": None,
                "错误信息": [],
                # 新增性能指标
                "P95执行时间": 0.0,
                "P99执行时间": 0.0,
                "内存使用峰值": 0,
                "并发调用峰值": 0,
                "当前并发数": 0,
                "缓存命中次数": 0,
                "缓存未命中次数": 0,
            }
            self.工具调用历史[工具名称] = []
            self.工具热度评分[工具名称] = 0.0

    async def _记录工具调用统计(
        self, 工具名称: str, 成功: bool, 执行时间: float, 错误信息: str = ""
    ):
        """记录工具调用统计信息"""
        try:
            # 确保工具统计存在
            self._初始化工具统计(工具名称)
            统计信息 = self.工具性能统计[工具名称]
            当前时间 = datetime.now()

            # 更新统计信息
            统计信息["调用次数"] += 1
            统计信息["总执行时间"] += 执行时间
            统计信息["平均执行时间"] = 统计信息["总执行时间"] / 统计信息["调用次数"]
            统计信息["最小执行时间"] = min(统计信息["最小执行时间"], 执行时间)
            统计信息["最大执行时间"] = max(统计信息["最大执行时间"], 执行时间)
            统计信息["最近调用时间"] = 当前时间

            if 成功:
                统计信息["成功次数"] += 1
            else:
                统计信息["失败次数"] += 1
                if 错误信息:
                    统计信息["错误信息"].append({"时间": 当前时间, "错误": 错误信息})
                    # 只保留最近10个错误
                    if len(统计信息["错误信息"]) > 10:
                        统计信息["错误信息"] = 统计信息["错误信息"][-10:]

            # 记录调用历史（用于缓存策略）
            调用记录 = {
                "时间": 当前时间,
                "成功": 成功,
                "执行时间": 执行时间,
                "内存使用": self._获取当前内存使用(),
                "并发数": self.当前并发数,
            }
            self.工具调用历史[工具名称].append(调用记录)

            # 只保留最近100次调用历史（增加历史记录数量）
            if len(self.工具调用历史[工具名称]) > 100:
                self.工具调用历史[工具名称] = self.工具调用历史[工具名称][-100:]

            # 更新P95和P99执行时间
            await self._更新百分位执行时间(工具名称)

            # 更新工具热度评分
            await self._更新工具热度评分(工具名称, 成功, 执行时间)

            # 更新内存使用统计
            self.内存使用统计.update(
                {"工具实例数": len(self.工具注册表), "缓存大小": len(self.工具性能统计)}
            )

        except Exception as e:
            工具日志器.warning(f"记录工具调用统计失败: {str(e)}")

    async def _执行单次工具调用(self, 工具实例, 参数列表: tuple, 关键字参数: dict):
        """执行单次工具调用 - 用于重试机制"""
        if isinstance(工具实例, 简单工具):
            return await 工具实例.run(*参数列表, **关键字参数)
        elif LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
            if 参数列表:
                return 工具实例.run(参数列表[0])  # LangChain工具通常接受单个字符串参数
            else:
                return 工具实例.run("")
        elif isinstance(工具实例, MCPTool):
            # 处理MCP工具调用
            return await 工具实例.run(*参数列表, **关键字参数)
        elif hasattr(工具实例, "run"):
            # 处理其他具有run方法的工具
            if asyncio.iscoroutinefunction(工具实例.run):
                return await 工具实例.run(*参数列表, **关键字参数)
            else:
                return 工具实例.run(*参数列表, **关键字参数)
        else:
            raise ValueError(f"不支持的工具类型: {type(工具实例)}")

    async def _处理工具调用失败(
        self,
        工具名称: str,
        开始时间: datetime,
        重试次数: int,
        error: Exception,
        错误类型: str,
    ) -> Dict[str, Any]:
        """处理工具调用失败的统一方法"""
        执行时间 = (datetime.now() - 开始时间).total_seconds()
        错误消息 = f"{错误类型} (重试{重试次数}次): {str(error)}"

        await self._记录工具调用统计(工具名称, False, 执行时间, 错误消息)

        # 确定状态码
        状态码 = (
            状态.工具调用.超时错误
            if isinstance(error, asyncio.TimeoutError)
            else 状态.工具调用.调用失败
        )

        return {
            "status_code": 状态码,
            "success": False,
            "message": f"工具调用失败: {str(error)}",
            "result": None,
            "execution_time": 执行时间,
            "retry_count": 重试次数,
        }

    async def _处理工具调用成功(
        self,
        工具名称: str,
        开始时间: datetime,
        执行结果: Any,
        用户ID: Optional[int],
        智能体id: Optional[int],
        参数列表: tuple,
        关键字参数: dict,
    ) -> Dict[str, Any]:
        """处理工具调用成功的统一方法"""
        执行时间 = (datetime.now() - 开始时间).total_seconds()

        # 记录成功的性能统计
        await self._记录工具调用统计(工具名称, True, 执行时间, "调用成功")
        工具日志器.debug(f"工具调用成功: {工具名称}, 执行时间: {执行时间:.2f}s")

        # 记录成功调用统计到数据库
        if self.工具数据层:
            try:
                await self.工具数据层.记录工具调用(
                    工具名称, 成功=True, 执行时间=执行时间
                )

                # 记录详细调用日志
                if 用户ID:
                    import json

                    调用参数_json = json.dumps(
                        {"参数列表": list(参数列表), "关键字参数": 关键字参数},
                        ensure_ascii=False,
                    )
                    执行结果_json = (
                        json.dumps(执行结果, ensure_ascii=False) if 执行结果 else ""
                    )

                    await self.工具数据层.记录工具调用日志(
                        用户ID=用户ID,
                        智能体id=智能体id,
                        工具名称=工具名称,
                        调用参数=调用参数_json,
                        执行结果=执行结果_json,
                        执行状态="成功",
                        执行时间=执行时间,
                    )
            except Exception as 统计异常:
                工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

        return {
            "status_code": 状态.工具调用.调用成功,
            "success": True,
            "message": "工具调用成功",
            "result": 执行结果,
            "execution_time": 执行时间,
            "tool_name": 工具名称,
        }

    async def _处理工具调用异常(
        self,
        工具名称: str,
        开始时间: datetime,
        异常: Exception,
        用户ID: Optional[int],
        智能体id: Optional[int],
        参数列表: tuple,
        关键字参数: dict,
    ) -> Dict[str, Any]:
        """处理工具调用异常的统一方法"""
        执行时间 = (datetime.now() - 开始时间).total_seconds()
        工具日志器.error(f"工具调用失败 {工具名称}: {str(异常)}")

        # 记录失败调用统计到数据库
        if self.工具数据层:
            try:
                await self.工具数据层.记录工具调用(工具名称, 成功=False)

                # 记录详细调用日志
                if 用户ID:
                    import json

                    调用参数_json = json.dumps(
                        {"参数列表": list(参数列表), "关键字参数": 关键字参数},
                        ensure_ascii=False,
                    )

                    await self.工具数据层.记录工具调用日志(
                        用户ID=用户ID,
                        智能体id=智能体id,
                        工具名称=工具名称,
                        调用参数=调用参数_json,
                        执行结果=str(异常),
                        执行状态="失败",
                        执行时间=执行时间,
                    )
            except Exception as 统计异常:
                工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

        # 记录失败的性能统计
        await self._记录工具调用统计(
            工具名称, False, 执行时间, f"调用失败: {str(异常)}"
        )

        return {
            "status_code": 状态.工具调用.调用失败,
            "success": False,
            "message": f"工具调用失败: {str(异常)}",
            "result": None,
            "execution_time": 执行时间,
            "tool_name": 工具名称,
        }

    def _获取当前内存使用(self) -> int:
        """获取当前内存使用量（字节）"""
        try:
            import psutil

            process = psutil.Process()
            return process.memory_info().rss
        except ImportError:
            # 如果psutil不可用，返回估算值
            return len(self.工具注册表) * 1024 * 1024  # 每个工具估算1MB
        except Exception:
            return 0

    async def _更新百分位执行时间(self, 工具名称: str):
        """更新P95和P99执行时间"""
        try:
            历史记录 = self.工具调用历史.get(工具名称, [])
            if len(历史记录) < 10:  # 至少需要10次调用才计算百分位
                return

            执行时间列表 = [记录["执行时间"] for 记录 in 历史记录 if 记录["成功"]]
            if not 执行时间列表:
                return

            执行时间列表.sort()
            统计信息 = self.工具性能统计[工具名称]

            # 计算P95（第95百分位）
            p95_index = int(len(执行时间列表) * 0.95)
            统计信息["P95执行时间"] = 执行时间列表[
                min(p95_index, len(执行时间列表) - 1)
            ]

            # 计算P99（第99百分位）
            p99_index = int(len(执行时间列表) * 0.99)
            统计信息["P99执行时间"] = 执行时间列表[
                min(p99_index, len(执行时间列表) - 1)
            ]

        except Exception as e:
            工具日志器.warning(f"更新百分位执行时间失败 ({工具名称}): {str(e)}")

    async def _更新工具热度评分(self, 工具名称: str, 成功: bool, 执行时间: float):
        """更新工具热度评分"""
        try:
            当前评分 = self.工具热度评分.get(工具名称, 0.0)

            # 热度评分算法：基于调用频率、成功率、执行效率
            频率权重 = 0.4  # 调用频率权重
            成功率权重 = 0.3  # 成功率权重
            效率权重 = 0.3  # 执行效率权重

            统计信息 = self.工具性能统计[工具名称]

            # 计算频率分数（基于最近调用）
            最近调用次数 = len(
                [
                    记录
                    for 记录 in self.工具调用历史.get(工具名称, [])
                    if (datetime.now() - 记录["时间"]).total_seconds() < 3600
                ]
            )  # 最近1小时
            频率分数 = min(最近调用次数 / 10.0, 1.0)  # 最多10次调用得满分

            # 计算成功率分数
            总调用次数 = 统计信息["调用次数"]
            成功率分数 = 统计信息["成功次数"] / max(总调用次数, 1)

            # 计算效率分数（执行时间越短分数越高）
            平均执行时间 = 统计信息["平均执行时间"]
            效率分数 = max(
                0, 1.0 - min(平均执行时间 / 10.0, 1.0)
            )  # 10秒以上执行时间得0分

            # 计算新的热度评分
            新评分 = 频率分数 * 频率权重 + 成功率分数 * 成功率权重 + 效率分数 * 效率权重

            # 使用指数移动平均更新评分
            衰减因子 = 0.1
            self.工具热度评分[工具名称] = 当前评分 * (1 - 衰减因子) + 新评分 * 衰减因子

        except Exception as e:
            工具日志器.warning(f"更新工具热度评分失败 ({工具名称}): {str(e)}")

    async def _定期清理任务(self):
        """定期清理任务 - 清理过期缓存和统计信息"""
        while True:
            try:
                await asyncio.sleep(self.自动清理间隔)

                当前时间 = datetime.now()

                # 检查是否需要清理
                if (当前时间 - self.上次清理时间).total_seconds() >= self.自动清理间隔:
                    await self._执行内存清理()
                    self.上次清理时间 = 当前时间

            except Exception as e:
                工具日志器.warning(f"定期清理任务异常: {str(e)}")
                await asyncio.sleep(60)  # 出错时等待1分钟再重试

    async def _执行内存清理(self):
        """执行内存清理"""
        try:
            清理前大小 = len(self.工具性能统计)

            # 清理长时间未使用的工具统计
            当前时间 = datetime.now()
            过期时间 = 当前时间 - timedelta(seconds=self.缓存TTL)

            过期工具 = []
            for 工具名称, 统计信息 in self.工具性能统计.items():
                最近调用时间 = 统计信息.get("最近调用时间")
                if 最近调用时间 and 最近调用时间 < 过期时间:
                    # 如果工具仍在注册表中，不清理统计信息
                    if 工具名称 not in self.工具注册表:
                        过期工具.append(工具名称)

            # 清理过期工具统计
            for 工具名称 in 过期工具:
                self.工具性能统计.pop(工具名称, None)
                self.工具调用历史.pop(工具名称, None)
                self.工具调用锁.pop(工具名称, None)

            清理后大小 = len(self.工具性能统计)

            if 过期工具:
                工具日志器.info(
                    f"内存清理完成: 清理了 {len(过期工具)} 个过期工具统计，缓存大小从 {清理前大小} 减少到 {清理后大小}"
                )

        except Exception as e:
            工具日志器.error(f"内存清理失败: {str(e)}")

    async def _性能监控任务(self):
        """性能监控任务 - 定期收集和分析性能数据"""
        while True:
            try:
                await asyncio.sleep(1800)  # 每30分钟执行一次

                # 分析性能趋势
                await self._分析性能趋势()

                # 检测性能异常
                await self._检测性能异常()

                # 更新缓存命中率
                await self._更新缓存命中率()

                # 生成性能报告
                await self._生成性能报告()

            except Exception as e:
                工具日志器.warning(f"性能监控任务异常: {str(e)}")
                await asyncio.sleep(60)

    async def _智能缓存预测任务(self):
        """智能缓存预测任务 - 基于使用模式预测需要缓存的工具"""
        while True:
            try:
                await asyncio.sleep(1800)  # 每30分钟执行一次

                # 分析使用模式
                使用模式 = await self._分析使用模式()

                # 预测热门工具
                预测工具 = await self._预测热门工具(使用模式)

                # 预加载工具
                await self._预加载工具(预测工具)

            except Exception as e:
                工具日志器.warning(f"智能缓存预测任务异常: {str(e)}")
                await asyncio.sleep(120)

    async def _加载历史统计数据(self):
        """从数据库加载历史统计数据"""
        try:
            if not self.工具数据层:
                return

            历史统计 = await self.工具数据层.获取工具统计信息()

            for 统计记录 in 历史统计:
                工具名称 = 统计记录.get("工具名称")
                if 工具名称 and 工具名称 in self.工具性能统计:
                    # 合并数据库中的统计数据
                    self.工具性能统计[工具名称].update(
                        {
                            "调用次数": 统计记录.get("使用次数", 0),
                            "成功次数": 统计记录.get("成功次数", 0),
                            "失败次数": 统计记录.get("失败次数", 0),
                        }
                    )

            工具日志器.info(f"加载历史统计数据完成: {len(历史统计)} 个工具")

        except Exception as e:
            工具日志器.warning(f"加载历史统计数据失败: {str(e)}")

    async def _初始化热度评分系统(self):
        """初始化热度评分系统"""
        try:
            # 基于历史数据计算初始热度评分
            for 工具名称, 统计信息 in self.工具性能统计.items():
                if 统计信息["调用次数"] > 0:
                    # 基于历史调用次数和成功率计算初始评分
                    成功率 = 统计信息["成功次数"] / max(统计信息["调用次数"], 1)
                    调用频率 = min(统计信息["调用次数"] / 100.0, 1.0)  # 100次调用得满分

                    初始评分 = 成功率 * 0.6 + 调用频率 * 0.4
                    self.工具热度评分[工具名称] = 初始评分

            工具日志器.info("热度评分系统初始化完成")

        except Exception as e:
            工具日志器.warning(f"初始化热度评分系统失败: {str(e)}")

    async def _分析性能趋势(self):
        """分析性能趋势"""
        try:
            for 工具名称, 历史记录 in self.工具调用历史.items():
                if len(历史记录) < 10:
                    continue

                # 分析最近的性能趋势
                最近记录 = 历史记录[-20:]  # 最近20次调用
                较早记录 = 历史记录[-40:-20] if len(历史记录) >= 40 else []

                if 较早记录:
                    最近平均时间 = sum(r["执行时间"] for r in 最近记录) / len(最近记录)
                    较早平均时间 = sum(r["执行时间"] for r in 较早记录) / len(较早记录)

                    # 如果性能下降超过50%，记录警告
                    if 最近平均时间 > 较早平均时间 * 1.5:
                        工具日志器.warning(
                            f"工具 {工具名称} 性能下降: {较早平均时间:.3f}s -> {最近平均时间:.3f}s"
                        )

        except Exception as e:
            工具日志器.warning(f"分析性能趋势失败: {str(e)}")

    async def _检测性能异常(self):
        """检测性能异常"""
        try:
            for 工具名称, 统计信息 in self.工具性能统计.items():
                # 检测异常高的执行时间
                if 统计信息["最大执行时间"] > 统计信息["平均执行时间"] * 10:
                    工具日志器.warning(
                        f"工具 {工具名称} 检测到异常执行时间: {统计信息['最大执行时间']:.3f}s"
                    )

                # 检测异常低的成功率
                if 统计信息["调用次数"] > 10:
                    成功率 = 统计信息["成功次数"] / 统计信息["调用次数"]
                    if 成功率 < 0.8:  # 成功率低于80%
                        工具日志器.warning(f"工具 {工具名称} 成功率异常: {成功率:.2%}")

        except Exception as e:
            工具日志器.warning(f"检测性能异常失败: {str(e)}")

    async def _更新缓存命中率(self):
        """更新缓存命中率"""
        try:
            总命中 = self.缓存命中统计["命中次数"]
            总未命中 = self.缓存命中统计["未命中次数"]
            总请求 = 总命中 + 总未命中

            if 总请求 > 0:
                命中率 = 总命中 / 总请求
                if 命中率 < 0.7:  # 命中率低于70%
                    工具日志器.info(f"缓存命中率较低: {命中率:.2%}，考虑调整缓存策略")

        except Exception as e:
            工具日志器.warning(f"更新缓存命中率失败: {str(e)}")

    async def _生成性能报告(self):
        """生成性能报告"""
        try:
            # 每小时生成一次详细报告
            当前时间 = datetime.now()
            if 当前时间.minute == 0:  # 整点时生成报告
                报告数据 = {
                    "时间": 当前时间,
                    "工具总数": len(self.工具注册表),
                    "活跃工具数": len(
                        [t for t, s in self.工具性能统计.items() if s["调用次数"] > 0]
                    ),
                    "总调用次数": sum(
                        s["调用次数"] for s in self.工具性能统计.values()
                    ),
                    "平均成功率": sum(
                        s["成功次数"] / max(s["调用次数"], 1)
                        for s in self.工具性能统计.values()
                    )
                    / max(len(self.工具性能统计), 1),
                    "缓存命中率": self.缓存命中统计["命中次数"]
                    / max(
                        self.缓存命中统计["命中次数"] + self.缓存命中统计["未命中次数"],
                        1,
                    ),
                }

                工具日志器.info(f"性能报告: {报告数据}")

        except Exception as e:
            工具日志器.warning(f"生成性能报告失败: {str(e)}")

    async def _分析使用模式(self) -> Dict[str, Any]:
        """分析使用模式"""
        try:
            模式数据 = {
                "时间段分布": {},  # 不同时间段的使用分布
                "工具组合": {},  # 常见的工具组合
                "用户行为": {},  # 用户使用行为模式
            }

            # 分析时间段分布
            当前小时 = datetime.now().hour
            for 工具名称, 历史记录 in self.工具调用历史.items():
                for 记录 in 历史记录[-50:]:  # 最近50次调用
                    小时 = 记录["时间"].hour
                    if 小时 not in 模式数据["时间段分布"]:
                        模式数据["时间段分布"][小时] = {}
                    if 工具名称 not in 模式数据["时间段分布"][小时]:
                        模式数据["时间段分布"][小时][工具名称] = 0
                    模式数据["时间段分布"][小时][工具名称] += 1

            return 模式数据

        except Exception as e:
            工具日志器.warning(f"分析使用模式失败: {str(e)}")
            return {}

    async def _预测热门工具(self, 使用模式: Dict[str, Any]) -> List[str]:
        """预测热门工具"""
        try:
            # 基于热度评分和使用模式预测
            工具评分 = []

            for 工具名称, 热度评分 in self.工具热度评分.items():
                # 结合时间段使用模式
                当前小时 = datetime.now().hour
                时间段权重 = 1.0

                if 当前小时 in 使用模式.get("时间段分布", {}):
                    时间段使用 = 使用模式["时间段分布"][当前小时].get(工具名称, 0)
                    时间段权重 = min(时间段使用 / 10.0, 2.0)  # 最多2倍权重

                最终评分 = 热度评分 * 时间段权重
                工具评分.append((工具名称, 最终评分))

            # 按评分排序，返回前10个
            工具评分.sort(key=lambda x: x[1], reverse=True)
            return [工具名称 for 工具名称, _ in 工具评分[:10]]

        except Exception as e:
            工具日志器.warning(f"预测热门工具失败: {str(e)}")
            return []

    async def _预加载工具(self, 预测工具列表: List[str]):
        """预加载工具"""
        try:
            for 工具名称 in 预测工具列表:
                if 工具名称 not in self.工具注册表:
                    # 尝试从数据库加载工具
                    await self._尝试加载工具(工具名称)

            self.预加载工具列表 = 预测工具列表
            工具日志器.debug(f"预加载工具完成: {len(预测工具列表)} 个工具")

        except Exception as e:
            工具日志器.warning(f"预加载工具失败: {str(e)}")

    async def _尝试加载工具(self, 工具名称: str):
        """尝试加载单个工具"""
        try:
            if not self.工具数据层:
                return

            工具配置 = await self.工具数据层.获取工具配置(工具名称)
            if 工具配置:
                # 根据工具类型加载工具实例
                # 这里可以扩展不同类型工具的加载逻辑
                pass

        except Exception as e:
            工具日志器.debug(f"加载工具 {工具名称} 失败: {str(e)}")

    def _是否核心工具(self, 工具名称: str) -> bool:
        """检查是否为核心工具 - 统一核心工具判断逻辑"""
        核心工具列表 = ["搜索工具", "计算工具", "文本处理工具"]
        return 工具名称 in 核心工具列表

    def _清理工具实例(self, 工具名称: str):
        """清理单个工具的所有相关数据 - 统一清理逻辑"""
        self.工具注册表.pop(工具名称, None)
        self.工具配置.pop(工具名称, None)
        self.工具调用锁.pop(工具名称, None)

    async def 获取性能统计(self, 工具名称: Optional[str] = None) -> Dict[str, Any]:
        """获取工具性能统计信息"""
        from 状态 import 状态

        try:
            if 工具名称:
                # 获取特定工具的统计信息
                if 工具名称 in self.工具性能统计:
                    return {
                        "status_code": 状态.工具调用.调用成功,
                        "success": True,
                        "message": f"获取工具 {工具名称} 性能统计成功",
                        "data": {
                            "工具名称": 工具名称,
                            "统计信息": self.工具性能统计[工具名称],
                            "调用历史": self.工具调用历史.get(工具名称, [])[
                                -10:
                            ],  # 最近10次调用
                            "热度评分": self.工具热度评分.get(工具名称, 0.0),
                            "缓存状态": {
                                "是否已缓存": 工具名称 in self.工具注册表,
                                "缓存命中次数": self.工具性能统计[工具名称].get(
                                    "缓存命中次数", 0
                                ),
                                "缓存未命中次数": self.工具性能统计[工具名称].get(
                                    "缓存未命中次数", 0
                                ),
                            },
                            "性能指标": {
                                "P95执行时间": self.工具性能统计[工具名称].get(
                                    "P95执行时间", 0.0
                                ),
                                "P99执行时间": self.工具性能统计[工具名称].get(
                                    "P99执行时间", 0.0
                                ),
                                "内存使用峰值": self.工具性能统计[工具名称].get(
                                    "内存使用峰值", 0
                                ),
                                "并发调用峰值": self.工具性能统计[工具名称].get(
                                    "并发调用峰值", 0
                                ),
                            },
                        },
                    }
                else:
                    return {
                        "status_code": 状态.工具调用.工具不存在,
                        "success": False,
                        "message": f"工具 {工具名称} 不存在或无统计信息",
                    }
            else:
                # 获取所有工具的统计概览
                概览统计 = {
                    "总工具数": len(self.工具注册表),
                    "有统计信息的工具数": len(self.工具性能统计),
                    "当前并发数": self.当前并发数,
                    "最大并发数": self.最大并发数,
                    "内存使用": self.内存使用统计,
                    "缓存统计": {
                        "缓存策略": self.缓存策略,
                        "最大缓存大小": self.最大缓存大小,
                        "当前缓存大小": len(self.工具注册表),
                        "缓存命中率": self._计算总体缓存命中率(),
                        "预加载工具数": len(self.预加载工具列表),
                    },
                    "性能概览": {
                        "总调用次数": sum(
                            s["调用次数"] for s in self.工具性能统计.values()
                        ),
                        "总成功次数": sum(
                            s["成功次数"] for s in self.工具性能统计.values()
                        ),
                        "总失败次数": sum(
                            s["失败次数"] for s in self.工具性能统计.values()
                        ),
                        "平均执行时间": self._计算平均执行时间(),
                        "热门工具": self._获取热门工具(5),
                    },
                    "工具统计": {},
                }

                for 工具名称, 统计信息 in self.工具性能统计.items():
                    概览统计["工具统计"][工具名称] = {
                        "调用次数": 统计信息["调用次数"],
                        "成功率": 统计信息["成功次数"]
                        / max(统计信息["调用次数"], 1)
                        * 100,
                        "平均执行时间": 统计信息["平均执行时间"],
                        "最近调用时间": 统计信息["最近调用时间"],
                        "热度评分": self.工具热度评分.get(工具名称, 0.0),
                        "P95执行时间": 统计信息.get("P95执行时间", 0.0),
                        "缓存命中率": self._计算工具缓存命中率(工具名称),
                    }

                return {
                    "status_code": 状态.工具调用.调用成功,
                    "success": True,
                    "message": "获取性能统计概览成功",
                    "data": 概览统计,
                }

        except Exception as e:
            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"获取性能统计失败: {str(e)}",
            }

    async def 同步内部函数工具到数据库(self) -> Dict[str, Any]:
        """同步内部函数工具到数据库 - 业务逻辑层"""
        try:
            工具日志器.info("🔄 开始同步内部函数工具到数据库...")

            # 导入内部函数包装器
            from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

            # 初始化内部函数包装器（如果尚未初始化）
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            # 获取所有内部函数工具
            内部工具字典 = await 内部函数包装器实例.获取可用工具列表()
            工具元数据字典 = await 内部函数包装器实例.获取工具元数据()

            if not 内部工具字典:
                工具日志器.warning("⚠️ 未发现任何内部函数工具")
                return {
                    "success": True,
                    "message": "未发现内部函数工具",
                    "新增": 0,
                    "更新": 0,
                }

            # 获取现有工具映射
            现有工具映射 = await self.工具数据层.获取现有工具映射()
            新增计数 = 0
            更新计数 = 0

            # 遍历所有内部函数工具
            for 工具名称, 工具实例 in 内部工具字典.items():
                元数据 = 工具元数据字典.get(工具名称, {})

                # 构建工具配置
                工具配置 = self._构建内部工具配置(工具实例, 元数据)

                if 工具名称 in 现有工具映射:
                    # 更新现有工具
                    if await self.工具数据层.更新工具配置(工具名称, 工具配置):
                        更新计数 += 1
                        工具日志器.debug(f"🔄 更新工具: {工具名称}")
                else:
                    # 插入新工具
                    工具配置["工具名称"] = 工具名称
                    if await self.工具数据层.插入工具配置(工具配置):
                        新增计数 += 1
                        工具日志器.info(f"✅ 新增工具: {工具名称}")

            结果消息 = f"同步完成: 新增 {新增计数} 个工具, 更新 {更新计数} 个工具"
            工具日志器.info(f"🎉 {结果消息}")

            return {
                "success": True,
                "message": 结果消息,
                "新增": 新增计数,
                "更新": 更新计数,
                "总计": len(内部工具字典),
            }

        except Exception as e:
            错误消息 = f"同步内部函数工具到数据库失败: {str(e)}"
            工具日志器.error(错误消息, exc_info=True)
            return {"success": False, "message": 错误消息, "新增": 0, "更新": 0}

    def _构建内部工具配置(self, 工具实例, 元数据: Dict[str, Any]) -> Dict[str, Any]:
        """构建内部工具配置 - 提取配置构建逻辑"""
        import json

        # 处理工具参数
        工具参数 = None
        if hasattr(工具实例, "args_schema") and 工具实例.args_schema:
            try:
                if hasattr(工具实例.args_schema, "model_json_schema"):
                    工具参数 = 工具实例.args_schema.model_json_schema()
                else:
                    工具参数 = str(工具实例.args_schema)
            except Exception:
                工具参数 = None

        工具描述 = getattr(工具实例, "description", "") or 元数据.get("描述", "")

        # 处理安全级别：将字符串转换为数字
        安全级别数值 = {"low": 1, "medium": 2, "high": 3}.get(
            元数据.get("安全级别", "medium"), 2
        )

        return {
            "工具描述": 工具描述,
            "工具参数": json.dumps(工具参数, ensure_ascii=False) if 工具参数 else None,
            "权限要求": 元数据.get("权限要求", ""),
            "安全级别": 安全级别数值,
            "启用状态": True,  # 默认启用
            "超时时间": 30,  # 默认超时30秒
            "重试次数": 3,  # 默认重试3次
        }

    async def 智能预加载工具(self, 预加载策略: str = "热门") -> Dict[str, Any]:
        """智能预加载工具 - 基于使用频率和策略预加载工具"""
        from 状态 import 状态

        try:
            预加载数量 = 0

            if 预加载策略 == "热门":
                # 基于调用频率预加载热门工具
                工具使用频率 = []
                for 工具名称, 统计信息 in self.工具性能统计.items():
                    if 统计信息["调用次数"] > 0:
                        工具使用频率.append((工具名称, 统计信息["调用次数"]))

                # 按调用次数排序，预加载前10个热门工具
                工具使用频率.sort(key=lambda x: x[1], reverse=True)
                热门工具 = [工具名称 for 工具名称, _ in 工具使用频率[:10]]

                for 工具名称 in 热门工具:
                    if 工具名称 not in self.工具注册表:
                        # 尝试从数据库加载工具配置
                        工具配置列表 = await self.工具数据层.获取工具配置列表(
                            工具名称=工具名称
                        )
                        if 工具配置列表:
                            if await self._加载单个工具配置(工具配置列表[0]):
                                预加载数量 += 1

            elif 预加载策略 == "全部":
                # 预加载所有启用的工具
                工具配置列表 = await self.工具数据层.获取工具配置列表(启用状态=True)
                for 工具配置 in 工具配置列表:
                    工具名称 = 工具配置.get("工具名称")
                    if 工具名称 not in self.工具注册表:
                        if await self._加载单个工具配置(工具配置):
                            预加载数量 += 1

            elif 预加载策略 == "最近使用":
                # 预加载最近使用的工具
                最近使用工具 = []
                当前时间 = datetime.now()

                for 工具名称, 统计信息 in self.工具性能统计.items():
                    最近调用时间 = 统计信息.get("最近调用时间")
                    if 最近调用时间:
                        时间差 = (当前时间 - 最近调用时间).total_seconds()
                        if 时间差 < 3600:  # 最近1小时内使用过
                            最近使用工具.append(工具名称)

                for 工具名称 in 最近使用工具:
                    if 工具名称 not in self.工具注册表:
                        工具配置列表 = await self.工具数据层.获取工具配置列表(
                            工具名称=工具名称
                        )
                        if 工具配置列表:
                            if await self._加载单个工具配置(工具配置列表[0]):
                                预加载数量 += 1

            工具日志器.info(
                f"智能预加载完成: 策略={预加载策略}, 预加载工具数={预加载数量}"
            )

            return {
                "status_code": 状态.工具调用.加载成功,
                "success": True,
                "message": f"智能预加载完成，预加载了 {预加载数量} 个工具",
                "data": {
                    "预加载策略": 预加载策略,
                    "预加载数量": 预加载数量,
                    "当前工具总数": len(self.工具注册表),
                },
            }

        except Exception as e:
            工具日志器.error(f"智能预加载工具失败: {str(e)}")
            return {
                "status_code": 状态.工具调用.加载失败,
                "success": False,
                "message": f"智能预加载失败: {str(e)}",
            }

    async def 卸载未使用工具(self, 未使用时间阈值: int = 3600) -> Dict[str, Any]:
        """卸载长时间未使用的工具以释放内存"""
        from 状态 import 状态

        try:
            卸载数量 = 0
            当前时间 = datetime.now()
            未使用时间 = timedelta(seconds=未使用时间阈值)

            待卸载工具 = []

            for 工具名称 in list(self.工具注册表.keys()):
                # 检查工具是否长时间未使用
                统计信息 = self.工具性能统计.get(工具名称)
                if 统计信息:
                    最近调用时间 = 统计信息.get("最近调用时间")
                    if 最近调用时间:
                        if (当前时间 - 最近调用时间) > 未使用时间:
                            待卸载工具.append(工具名称)
                    else:
                        # 从未被调用过的工具
                        待卸载工具.append(工具名称)
                else:
                    # 没有统计信息的工具
                    待卸载工具.append(工具名称)

            # 执行卸载
            for 工具名称 in 待卸载工具:
                # 保留核心工具，不卸载
                if self._是否核心工具(工具名称):
                    continue

                # 卸载工具
                self._清理工具实例(工具名称)

                # 保留统计信息，但清理调用历史
                if 工具名称 in self.工具调用历史:
                    self.工具调用历史[工具名称] = []

                卸载数量 += 1
                工具日志器.debug(f"卸载未使用工具: {工具名称}")

            # 更新内存使用统计
            self.内存使用统计["工具实例数"] = len(self.工具注册表)

            工具日志器.info(f"卸载未使用工具完成: 卸载了 {卸载数量} 个工具")

            return {
                "status_code": 状态.工具调用.调用成功,
                "success": True,
                "message": f"卸载完成，释放了 {卸载数量} 个未使用工具的内存",
                "data": {
                    "卸载数量": 卸载数量,
                    "剩余工具数": len(self.工具注册表),
                    "未使用时间阈值": 未使用时间阈值,
                },
            }

        except Exception as e:
            工具日志器.error(f"卸载未使用工具失败: {str(e)}")
            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"卸载未使用工具失败: {str(e)}",
            }

    async def 优化工具缓存(self) -> Dict[str, Any]:
        """优化工具缓存 - 基于LRU策略管理工具缓存"""
        from 状态 import 状态

        try:
            优化前数量 = len(self.工具注册表)

            # 如果工具数量超过最大缓存大小，执行LRU清理
            if len(self.工具注册表) > self.最大缓存大小:
                # 按最近使用时间排序
                工具使用时间 = []
                当前时间 = datetime.now()

                for 工具名称 in self.工具注册表.keys():
                    统计信息 = self.工具性能统计.get(工具名称)
                    if 统计信息 and 统计信息.get("最近调用时间"):
                        最近使用时间 = 统计信息["最近调用时间"]
                    else:
                        最近使用时间 = datetime.min  # 从未使用过的工具

                    工具使用时间.append((工具名称, 最近使用时间))

                # 按使用时间排序，最久未使用的在前
                工具使用时间.sort(key=lambda x: x[1])

                # 计算需要清理的工具数量
                需要清理数量 = len(self.工具注册表) - self.最大缓存大小

                # 清理最久未使用的工具
                清理数量 = 0
                for 工具名称, _ in 工具使用时间[:需要清理数量]:
                    # 保留核心工具
                    if self._是否核心工具(工具名称):
                        continue

                    self._清理工具实例(工具名称)
                    清理数量 += 1

                    if 清理数量 >= 需要清理数量:
                        break

            优化后数量 = len(self.工具注册表)
            清理数量 = 优化前数量 - 优化后数量

            # 更新内存使用统计
            self.内存使用统计["工具实例数"] = len(self.工具注册表)
            self.内存使用统计["缓存大小"] = len(self.工具性能统计)

            工具日志器.info(f"工具缓存优化完成: 清理了 {清理数量} 个工具")

            return {
                "status_code": 状态.工具调用.调用成功,
                "success": True,
                "message": f"缓存优化完成，清理了 {清理数量} 个工具",
                "data": {
                    "优化前数量": 优化前数量,
                    "优化后数量": 优化后数量,
                    "清理数量": 清理数量,
                    "最大缓存大小": self.最大缓存大小,
                },
            }

        except Exception as e:
            工具日志器.error(f"优化工具缓存失败: {str(e)}")
            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"缓存优化失败: {str(e)}",
            }

    def _计算总体缓存命中率(self) -> float:
        """计算总体缓存命中率"""
        try:
            总命中 = self.缓存命中统计["命中次数"]
            总未命中 = self.缓存命中统计["未命中次数"]
            总请求 = 总命中 + 总未命中

            return 总命中 / max(总请求, 1) * 100
        except Exception:
            return 0.0

    def _计算工具缓存命中率(self, 工具名称: str) -> float:
        """计算单个工具的缓存命中率"""
        try:
            统计信息 = self.工具性能统计.get(工具名称, {})
            命中次数 = 统计信息.get("缓存命中次数", 0)
            未命中次数 = 统计信息.get("缓存未命中次数", 0)
            总请求 = 命中次数 + 未命中次数

            return 命中次数 / max(总请求, 1) * 100
        except Exception:
            return 0.0

    def _计算平均执行时间(self) -> float:
        """计算所有工具的平均执行时间"""
        try:
            总时间 = sum(s["总执行时间"] for s in self.工具性能统计.values())
            总次数 = sum(s["调用次数"] for s in self.工具性能统计.values())

            return 总时间 / max(总次数, 1)
        except Exception:
            return 0.0

    def _获取热门工具(self, 数量: int = 5) -> List[Dict[str, Any]]:
        """获取热门工具列表"""
        try:
            工具评分 = []
            for 工具名称, 热度评分 in self.工具热度评分.items():
                统计信息 = self.工具性能统计.get(工具名称, {})
                工具评分.append(
                    {
                        "工具名称": 工具名称,
                        "热度评分": 热度评分,
                        "调用次数": 统计信息.get("调用次数", 0),
                        "成功率": 统计信息.get("成功次数", 0)
                        / max(统计信息.get("调用次数", 1), 1)
                        * 100,
                    }
                )

            # 按热度评分排序
            工具评分.sort(key=lambda x: x["热度评分"], reverse=True)
            return 工具评分[:数量]
        except Exception:
            return []


# 创建全局工具管理器实例
LangChain工具管理器实例 = LangChain工具管理器()
