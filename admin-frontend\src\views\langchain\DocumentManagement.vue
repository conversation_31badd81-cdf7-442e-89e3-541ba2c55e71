<template>
  <div class="document-management">
    <div class="page-header">
      <div class="header-content">
        <h1>📄 文档管理</h1>
        <p>知识库：{{ 知识库名称 }} - 管理文档和支持的文件格式</p>
        <div class="stats-overview">
          <a-statistic title="文档总数" :value="文档统计.总数量" />
          <a-statistic title="已处理" :value="文档统计.已处理数量" />
          <a-statistic title="处理中" :value="文档统计.处理中数量" />
          <a-statistic title="失败" :value="文档统计.失败数量" />
        </div>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="显示上传对话框">
          <template #icon><UploadOutlined /></template>
          上传文档
        </a-button>
        <a-button @click="批量处理选中文档" :disabled="选中文档列表.length === 0">
          <template #icon><ThunderboltOutlined /></template>
          批量处理
        </a-button>
        <a-button @click="批量删除选中文档" :disabled="选中文档列表.length === 0" danger>
          <template #icon><DeleteOutlined /></template>
          批量删除
        </a-button>
        <a-button @click="查看支持格式">
          <template #icon><FileTextOutlined /></template>
          支持格式
        </a-button>
        <a-button @click="刷新列表">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-button @click="返回知识库列表">
          <template #icon><ArrowLeftOutlined /></template>
          返回
        </a-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" size="small">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="搜索条件.搜索关键字"
            placeholder="搜索文档名称"
            @press-enter="获取文档列表"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="搜索条件.文件类型"
            placeholder="文件类型"
            allow-clear
            @change="获取文档列表"
          >
            <a-select-option value="pdf">PDF文档</a-select-option>
            <a-select-option value="docx">Word文档</a-select-option>
            <a-select-option value="xlsx">Excel表格</a-select-option>
            <a-select-option value="text">文本文件</a-select-option>
            <a-select-option value="markdown">Markdown</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="搜索条件.处理状态"
            placeholder="处理状态"
            allow-clear
            @change="获取文档列表"
          >
            <a-select-option value="processed">已处理</a-select-option>
            <a-select-option value="processing">处理中</a-select-option>
            <a-select-option value="uploading">上传中</a-select-option>
            <a-select-option value="failed">失败</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-button type="primary" @click="获取文档列表">搜索</a-button>
        </a-col>
        <a-col :span="3">
          <a-button @click="重置搜索条件">重置</a-button>
        </a-col>
        <a-col :span="4">
          <a-switch 
            v-model:checked="自动刷新" 
            checked-children="自动刷新" 
            un-checked-children="手动刷新"
            @change="切换自动刷新"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- 文档列表 -->
    <a-card class="list-card">
      <template #title>
        <div class="table-header">
          <span>文档列表 ({{ 文档列表.length }} / {{ 分页配置.total }})</span>
          <div class="batch-actions">
            <a-checkbox 
              v-model:checked="全选状态" 
              :indeterminate="部分选中状态"
              @change="切换全选"
            >
              全选
            </a-checkbox>
            <span v-if="选中文档列表.length > 0" class="selected-count">
              已选择 {{ 选中文档列表.length }} 项
            </span>
          </div>
        </div>
      </template>

      <a-table
        :columns="表格列定义"
        :data-source="文档列表"
        :loading="加载中"
        :pagination="分页配置"
        :row-selection="行选择配置"
        @change="处理表格变化"
        row-key="id"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'document_info'">
            <div class="document-info">
              <a-avatar :size="32" :style="{ backgroundColor: 获取文件类型颜色(record.文档类型) }">
                <template #icon>
                  <component :is="获取文件类型图标(record.文档类型)" />
                </template>
              </a-avatar>
              <div class="info-content">
                <div class="name" :title="record.文档名称">{{ record.文档名称 }}</div>
                <div class="type">{{ 获取文件类型描述(record.文档类型) }}</div>
                <div class="upload-time">{{ 格式化时间(record.上传时间) }}</div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'file_info'">
            <div class="file-info">
              <div class="size">{{ 格式化文件大小(record.文档大小) }}</div>
              <div class="chunks">{{ record.分块数量 || 0 }} 个分块</div>
              <div class="vectors" v-if="record.向量数量">{{ record.向量数量 }} 个向量</div>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <div class="status-info">
              <a-tag :color="获取状态颜色(record.状态)">
                {{ 获取状态文本(record.状态) }}
              </a-tag>
              <a-progress 
                v-if="record.状态 === 'processing'" 
                :percent="record.处理进度 || 0" 
                size="small"
                status="active"
              />
              <div v-if="record.错误信息" class="error-info">
                <a-tooltip :title="record.错误信息">
                  <ExclamationCircleOutlined style="color: #ff4d4f" />
                </a-tooltip>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="detail" @click="查看文档详情(record)">
                      <EyeOutlined /> 查看详情
                    </a-menu-item>
                    <a-menu-item key="preview" @click="预览文档(record)">
                      <FileSearchOutlined /> 预览内容
                    </a-menu-item>
                    <a-menu-item key="edit" @click="编辑文档(record)">
                      <EditOutlined /> 编辑文档
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="reprocess" 
                      @click="重新处理文档(record)"
                      :disabled="record.状态 === 'processing'"
                    >
                      <ReloadOutlined /> 重新处理
                    </a-menu-item>
                    <a-menu-item key="download" @click="下载文档(record)">
                      <DownloadOutlined /> 下载文档
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="删除文档(record)" class="danger-item">
                      <DeleteOutlined /> 删除文档
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="primary" size="small">
                  操作 <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 上传文档对话框 -->
    <a-modal
      v-model:open="上传对话框可见"
      title="📤 上传文档到知识库"
      width="700px"
      @ok="确认上传文档"
      @cancel="取消上传"
      :confirm-loading="上传中"
      :ok-button-props="{ disabled: 上传文件列表.length === 0 }"
    >
      <div class="upload-content">
        <!-- 上传区域 -->
        <a-upload-dragger
          v-model:fileList="上传文件列表"
          name="file"
          :multiple="true"
          :before-upload="处理文件上传前"
          @remove="移除文件"
          :show-upload-list="{ showRemoveIcon: true, showPreviewIcon: false }"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个或批量上传。支持多种文件格式，包括PDF、Word、Excel、文本等。
          </p>
        </a-upload-dragger>
        
        <!-- 上传选项 -->
        <div class="upload-options">
          <h4>上传选项</h4>
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="分块策略">
                  <div class="strategy-selector">
                    <!-- 智能推荐标签 -->
                    <div v-if="getRecommendedStrategy(上传文件列表)" class="recommended-tag">
                      <a-tag color="green" size="small">
                        <BulbOutlined /> 推荐：{{ getStrategyDisplayName(getRecommendedStrategy(上传文件列表)) }}
                      </a-tag>
                      <a-button
                        type="link"
                        size="small"
                        @click="() => applyRecommendedStrategy(上传文件列表, 上传选项, message)"
                      >
                        一键应用
                      </a-button>
                    </div>

                    <a-select
                      v-model:value="上传选项.分块策略"
                      placeholder="选择分块策略"
                      @change="(strategy) => {
                        const result = onStrategyChange(strategy, 上传选项, 上传文件列表.value);
                        if (!result.success) {
                          message.error(result.message);
                          if (result.suggestedStrategy) {
                            上传选项.分块策略 = result.suggestedStrategy;
                          }
                        }
                        检查分块策略适用性();
                      }"
                    >
                      <a-select-option
                        v-for="strategy in getAvailableStrategies(上传文件列表)"
                        :key="strategy.value"
                        :value="strategy.value"
                      >
                        <div class="strategy-option">
                          <span class="strategy-name">
                            {{ strategy.icon }} {{ strategy.name }}
                          </span>
                          <span v-if="strategy.recommended" class="recommended-badge">推荐</span>
                        </div>
                      </a-select-option>
                    </a-select>

                    <!-- 策略说明卡片 -->
                    <div class="strategy-info-card">
                      <div class="strategy-description">
                        {{ getCurrentStrategyInfo().description }}
                      </div>
                      <div class="strategy-advantages">
                        <strong>优势：</strong>{{ getCurrentStrategyInfo().advantages }}
                      </div>
                      <div class="strategy-use-cases">
                        <strong>适用场景：</strong>{{ getCurrentStrategyInfo().useCases }}
                      </div>
                    </div>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="分块大小">
                  <a-input-number
                    v-model:value="上传选项.分块大小"
                    :min="getMinChunkSize()"
                    :max="5000"
                    :step="getChunkStep()"
                    placeholder="分块大小"
                  />
                  <div style="font-size: 12px; color: #666; margin-top: 4px;">
                    {{ getChunkSizeHint() }}
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="重叠大小">
                  <a-input-number 
                    v-model:value="上传选项.分块重叠" 
                    :min="0" 
                    :max="1000" 
                    placeholder="重叠大小"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item>
                  <a-checkbox v-model:checked="上传选项.自动处理">
                    上传后自动处理（向量化）
                  </a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 支持格式提示 -->
        <div v-if="支持的格式列表.length > 0" class="supported-formats">
          <h4>支持的文件格式：</h4>
          <div class="format-tags">
            <a-tag
              v-for="格式 in 支持的格式列表.slice(0, 15)"
              :key="格式.扩展名"
              color="blue"
              size="small"
            >
              {{ 格式.扩展名 }}
            </a-tag>
            <a-tag v-if="支持的格式列表.length > 15" color="default" size="small">
              +{{ 支持的格式列表.length - 15 }} 更多
            </a-tag>
          </div>
        </div>

        <!-- 上传进度 -->
        <div v-if="上传进度信息.显示" class="upload-progress">
          <h4>上传进度</h4>
          <a-progress 
            :percent="上传进度信息.百分比" 
            :status="上传进度信息.状态"
            :show-info="true"
          />
          <div class="progress-details">
            <span>{{ 上传进度信息.当前文件 }} / {{ 上传进度信息.总文件数 }}</span>
            <span>{{ 上传进度信息.速度 }}</span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 支持格式对话框 -->
    <a-modal
      v-model:open="格式对话框可见"
      title="📋 支持的文件格式"
      width="900px"
      :footer="null"
    >
      <div class="formats-content">
        <!-- 格式统计概览 -->
        <div class="format-stats" v-if="格式统计.总格式数">
          <a-row :gutter="16" style="margin-bottom: 24px;">
            <a-col :span="6">
              <a-statistic title="总格式数" :value="格式统计.总格式数" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="文档类型" :value="格式统计.文档类型" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="文本类型" :value="格式统计.文本类型" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="数据类型" :value="格式统计.数据类型" />
            </a-col>
          </a-row>
        </div>

        <!-- 详细格式列表 -->
        <a-descriptions :column="1" bordered size="small" v-if="格式统计.文件类型详情?.length">
          <a-descriptions-item
            v-for="类型 in 格式统计.文件类型详情"
            :key="类型.类型名称"
            :label="类型.类型名称.toUpperCase()"
          >
            <div class="format-detail">
              <div class="extensions">
                <a-tag
                  v-for="扩展名 in 类型.扩展名列表"
                  :key="扩展名"
                  color="blue"
                  size="small"
                >
                  {{ 扩展名 }}
                </a-tag>
              </div>
              <div class="description">{{ 类型.描述 }}</div>
            </div>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 空状态 -->
        <a-empty v-else description="暂无支持的文件格式数据" />
      </div>
    </a-modal>

    <!-- 编辑文档对话框 -->
    <a-modal
      v-model:open="编辑对话框可见"
      title="编辑文档"
      width="800px"
      @ok="确认编辑文档"
      @cancel="取消编辑"
      :confirm-loading="编辑中"
    >
      <div class="edit-content" v-if="当前编辑文档">
        <a-form
          ref="编辑表单引用"
          :model="编辑表单数据"
          :rules="编辑表单规则"
          layout="vertical"
        >
          <a-form-item label="文档名称" name="文档名称">
            <a-input
              v-model:value="编辑表单数据.文档名称"
              placeholder="请输入文档名称"
            />
          </a-form-item>

          <a-form-item label="文档类型" name="文档类型">
            <a-select
              v-model:value="编辑表单数据.文档类型"
              placeholder="选择文档类型"
              disabled
            >
              <a-select-option value="pdf">PDF文档</a-select-option>
              <a-select-option value="docx">Word文档</a-select-option>
              <a-select-option value="word">Word文档</a-select-option>
              <a-select-option value="xlsx">Excel表格</a-select-option>
              <a-select-option value="excel">Excel表格</a-select-option>
              <a-select-option value="text">文本文件</a-select-option>
              <a-select-option value="markdown">Markdown文档</a-select-option>
              <a-select-option value="html">HTML文档</a-select-option>
              <a-select-option value="csv">CSV文件</a-select-option>
              <a-select-option value="json">JSON文件</a-select-option>
              <a-select-option value="xml">XML文件</a-select-option>
              <a-select-option value="pptx">PowerPoint演示</a-select-option>
              <a-select-option value="other">其他类型</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="文档状态" name="状态">
            <a-select
              v-model:value="编辑表单数据.状态"
              placeholder="选择文档状态"
            >
              <a-select-option value="processed">已处理</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="uploading">上传中</a-select-option>
              <a-select-option value="failed">处理失败</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item 
            label="文档内容" 
            name="文档内容" 
            v-if="编辑表单数据.文档类型 === 'text' || 编辑表单数据.文档类型 === 'markdown' || 编辑表单数据.文档类型 === 'json' || 编辑表单数据.文档类型 === 'html'"
          >
            <a-textarea
              v-model:value="编辑表单数据.文档内容"
              placeholder="请输入文档内容"
              :rows="10"
              show-count
              :maxlength="50000"
            />
            <div class="help-text">
              <small>支持编辑文本类型文档的内容，修改后将重新进行向量化处理</small>
            </div>
          </a-form-item>

          <a-form-item label="元数据" name="元数据">
            <a-textarea
              v-model:value="编辑表单数据.元数据字符串"
              placeholder="请输入JSON格式的元数据"
              :rows="4"
              class="json-editor"
            />
            <div class="help-text">
              <small>请输入有效的JSON格式，例如：{"标签": ["重要"], "作者": "张三", "分类": "技术文档"}</small>
            </div>
            <div v-if="元数据验证错误" class="error-text">
              <small>{{ 元数据验证错误 }}</small>
            </div>
          </a-form-item>

          <a-form-item label="备注" name="备注">
            <a-textarea
              v-model:value="编辑表单数据.备注"
              placeholder="请输入备注信息"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <!-- 文档统计信息（只读） -->
          <a-descriptions title="文档统计信息" :column="2" size="small" bordered>
            <a-descriptions-item label="文件大小">
              {{ 格式化文件大小(当前编辑文档.文档大小) }}
            </a-descriptions-item>
            <a-descriptions-item label="分块数量">
              {{ 当前编辑文档.分块数量 || 0 }} 个
            </a-descriptions-item>
            <a-descriptions-item label="创建时间" :span="2">
              {{ 当前编辑文档.创建时间 ? new Date(当前编辑文档.创建时间).toLocaleString() : '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-form>
      </div>
    </a-modal>

    <!-- 文档详情对话框 -->
    <a-modal
      v-model:open="详情对话框可见"
      title="文档详情"
      width="900px"
      :footer="null"
    >
      <div class="detail-content" v-if="当前查看文档">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="文档名称" :span="2">
            {{ 当前查看文档.文档名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="文档类型">
            <a-tag :color="获取文件类型颜色(当前查看文档.文档类型)">
              {{ 获取文件类型描述(当前查看文档.文档类型) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="文档状态">
            <a-tag :color="获取状态颜色(当前查看文档.状态)">
              {{ 获取状态文本(当前查看文档.状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ 格式化文件大小(当前查看文档.文档大小) }}
          </a-descriptions-item>
          <a-descriptions-item label="分块数量">
            {{ 当前查看文档.分块数量 || 0 }} 个分块
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">
            {{ 当前查看文档.创建时间 ? new Date(当前查看文档.创建时间).toLocaleString() : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="最后更新" :span="2">
            {{ 当前查看文档.更新时间 ? new Date(当前查看文档.更新时间).toLocaleString() : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="元数据" :span="2" v-if="当前查看文档.元数据">
            <pre class="metadata-display">{{ JSON.stringify(当前查看文档.元数据, null, 2) }}</pre>
          </a-descriptions-item>
          <a-descriptions-item label="文档内容预览" :span="2" v-if="当前查看文档.内容预览">
            <div class="content-preview">
              {{ 当前查看文档.内容预览 }}
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 文档预览对话框 -->
    <a-modal
      v-model:open="预览对话框可见"
      title="文档内容预览"
      width="1000px"
      :footer="null"
    >
      <div class="preview-content" v-if="当前预览文档">
        <div class="preview-header">
          <h3>{{ 当前预览文档.文档名称 }}</h3>
          <a-space>
            <a-tag :color="获取文件类型颜色(当前预览文档.文档类型)">
              {{ 获取文件类型描述(当前预览文档.文档类型) }}
            </a-tag>
            <a-tag :color="获取状态颜色(当前预览文档.状态)">
              {{ 获取状态文本(当前预览文档.状态) }}
            </a-tag>
          </a-space>
        </div>
        <a-divider />
        <div class="preview-body">
          <a-spin :spinning="预览加载中">
            <div v-if="预览内容" class="content-display">
              <pre>{{ 预览内容 }}</pre>
            </div>
            <div v-else class="no-content">
              <a-empty description="暂无预览内容" />
            </div>
          </a-spin>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {
    applyRecommendedStrategy,
    getAvailableStrategies,
    getChunkSizeHint,
    getChunkStep,
    getFileSizeDescription,
    getMinChunkSize,
    getRecommendedStrategy,
    getStrategyDisplayName,
    onStrategyChange,
    分块策略配置,
    文件大小分类
} from '@/config/chunkStrategies'
import documentService from '@/services/documentService'
import {
    ArrowLeftOutlined,
    BulbOutlined,
    DeleteOutlined,
    DownloadOutlined,
    DownOutlined,
    EditOutlined,
    ExclamationCircleOutlined,
    EyeOutlined,
    FileExcelOutlined,
    FileImageOutlined,
    FileOutlined,
    FilePdfOutlined,
    FileSearchOutlined,
    FileTextOutlined,
    FileWordOutlined,
    InboxOutlined,
    ReloadOutlined,
    SearchOutlined,
    ThunderboltOutlined,
    UploadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 导入共享配置

// 路由
const route = useRoute()
const router = useRouter()

// 表单引用
const 编辑表单引用 = ref(null)

// ==================== 响应式数据 ====================

const 加载中 = ref(false)
const 上传中 = ref(false)
const 编辑中 = ref(false)
const 预览加载中 = ref(false)
const 文档列表 = ref([])
const 上传对话框可见 = ref(false)
const 格式对话框可见 = ref(false)
const 编辑对话框可见 = ref(false)
const 详情对话框可见 = ref(false)
const 预览对话框可见 = ref(false)
const 上传文件列表 = ref([])
const 支持的格式列表 = ref([])
const 格式统计 = ref({})
const 当前编辑文档 = ref(null)
const 当前查看文档 = ref(null)
const 当前预览文档 = ref(null)
const 预览内容 = ref('')
const 元数据验证错误 = ref('')
const 全选状态 = ref(false)
const 部分选中状态 = ref(false)
const 选中文档列表 = ref([])
const 自动刷新 = ref(true)
const 自动刷新定时器 = ref(null)

// 统计信息
const 文档统计 = ref({
  总数量: 0,
  已处理数量: 0,
  处理中数量: 0,
  失败数量: 0
})

// 搜索条件
const 搜索条件 = reactive({
  搜索关键字: '',
  文件类型: '',
  处理状态: ''
})

// 编辑表单数据
const 编辑表单数据 = reactive({
  文档名称: '',
  文档类型: '',
  状态: '',
  文档内容: '',
  元数据字符串: '',
  备注: ''
})

// 上传相关
const 上传选项 = reactive({
  分块策略: '智能递归分块',
  分块大小: 1000,
  分块重叠: 200,
  自动处理: true
})
const 上传进度信息 = reactive({
  显示: false,
  百分比: 0,
  状态: 'normal',
  当前文件: 0,
  总文件数: 0,
  速度: ''
})

// 知识库信息
const 知识库ID = ref(parseInt(route.params.knowledgeBaseId))
const 知识库名称 = ref(route.query.knowledgeBaseName || '未知知识库')

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列定义
const 表格列定义 = [
  {
    title: '文档信息',
    key: 'document_info',
    width: 300,
    ellipsis: true
  },
  {
    title: '文件信息',
    key: 'file_info',
    width: 150,
    align: 'center'
  },
  {
    title: '处理状态',
    key: 'status',
    width: 180,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]

// 行选择配置
const 行选择配置 = {
  selectedRowKeys: computed(() => 选中文档列表.value),
  onChange: (selectedRowKeys) => {
    选中文档列表.value = selectedRowKeys
    更新选择状态()
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    if (selected) {
      选中文档列表.value = 文档列表.value.map(item => item.id)
    } else {
      选中文档列表.value = []
    }
    更新选择状态()
  }
}

// 表单验证规则
const 编辑表单规则 = {
  文档名称: [
    { required: true, message: '请输入文档名称', trigger: 'blur' },
    { min: 1, max: 200, message: '文档名称长度应在1-200字符之间', trigger: 'blur' }
  ]
}

/**
 * 更新选择状态
 */
const 更新选择状态 = () => {
  const 总数 = 文档列表.value.length
  const 选中数 = 选中文档列表.value.length
  
  全选状态.value = 选中数 === 总数 && 总数 > 0
  部分选中状态.value = 选中数 > 0 && 选中数 < 总数
}

/**
 * 切换全选状态
 */
const 切换全选 = (checked) => {
  if (checked) {
    选中文档列表.value = 文档列表.value.map(item => item.id)
  } else {
    选中文档列表.value = []
  }
  更新选择状态()
}

/**
 * 获取文档列表
 */
const 获取文档列表 = async () => {
  try {
    加载中.value = true
    console.log('🔍 获取文档列表，知识库ID:', 知识库ID.value, '参数:', {
      页码: 分页配置.current,
      每页数量: 分页配置.pageSize,
      ...搜索条件
    })

    const 响应 = await documentService.getDocumentList(知识库ID.value, {
      页码: 分页配置.current,
      每页数量: 分页配置.pageSize,
      ...搜索条件
    })

    console.log('📡 文档列表响应:', 响应)

    if (响应.success) {
      // 处理文档列表数据，解析元数据字段
      const 原始文档列表 = 响应.data.文档列表 || []
      文档列表.value = 原始文档列表.map(文档 => {
        // 解析元数据字段
        let 元数据 = {}
        try {
          if (typeof 文档.元数据 === 'string') {
            元数据 = JSON.parse(文档.元数据)
          } else if (typeof 文档.元数据 === 'object' && 文档.元数据 !== null) {
            元数据 = 文档.元数据
          }
        } catch (error) {
          console.warn('解析元数据失败:', error, '原始数据:', 文档.元数据)
          元数据 = {}
        }

        // 返回处理后的文档对象，添加前端需要的字段
        return {
          ...文档,
          // 使用数据库表字段而不是元数据字段
          分块数量: 文档.向量分块数量 || 0,  // 使用表字段
          向量数量: 文档.向量分块数量 || 0,  // 使用表字段
          上传时间: 文档.创建时间, // 使用创建时间作为上传时间
          处理进度: 文档.状态 === 'processing' ? 50 : (文档.状态 === 'processed' ? 100 : 0),
          // 保持原有字段
          元数据: 元数据
        }
      })

      分页配置.total = 响应.data.总数量 || 0

      // 更新统计信息
      更新文档统计()

      // 清除选中状态
      选中文档列表.value = []
      更新选择状态()

      console.log('✅ 获取文档列表成功，共', 文档列表.value.length, '条记录')
      console.log('📋 处理后的文档数据:', 文档列表.value)
    } else {
      message.error(响应.error || '获取文档列表失败')
      文档列表.value = []
      分页配置.total = 0
    }
  } catch (error) {
    console.error('❌ 获取文档列表异常:', error)
    message.error('获取文档列表失败')
  } finally {
    加载中.value = false
  }
}

/**
 * 刷新列表 - 重新获取文档列表
 */
const 刷新列表 = () => {
  console.log('🔄 手动刷新文档列表')
  获取文档列表()
}

/**
 * 更新文档统计
 */
const 更新文档统计 = () => {
  文档统计.value.总数量 = 文档列表.value.length
  文档统计.value.已处理数量 = 文档列表.value.filter(doc => doc.状态 === 'processed').length
  文档统计.value.处理中数量 = 文档列表.value.filter(doc => doc.状态 === 'processing').length
  文档统计.value.失败数量 = 文档列表.value.filter(doc => doc.状态 === 'failed').length
}

/**
 * 重置搜索条件
 */
const 重置搜索条件 = () => {
  搜索条件.搜索关键字 = ''
  搜索条件.文件类型 = ''
  搜索条件.处理状态 = ''
  分页配置.current = 1
  获取文档列表()
}

/**
 * 切换自动刷新
 */
const 切换自动刷新 = (enabled) => {
  if (enabled) {
    启动自动刷新()
  } else {
    停止自动刷新()
  }
}

/**
 * 启动自动刷新
 */
const 启动自动刷新 = () => {
  停止自动刷新() // 先清除现有定时器
  自动刷新定时器.value = setInterval(() => {
    // 只有在有处理中的文档时才自动刷新
    if (文档列表.value.some(doc => doc.状态 === 'processing' || doc.状态 === 'uploading')) {
      获取文档列表()
    }
  }, 5000) // 每5秒刷新一次
}

/**
 * 停止自动刷新
 */
const 停止自动刷新 = () => {
  if (自动刷新定时器.value) {
    clearInterval(自动刷新定时器.value)
    自动刷新定时器.value = null
  }
}

/**
 * 批量处理选中文档
 */
const 批量处理选中文档 = async () => {
  if (选中文档列表.value.length === 0) {
    message.warning('请先选择要处理的文档')
    return
  }

  try {
    console.log('⚡ 批量处理文档:', 选中文档列表.value)
    
    let 成功数量 = 0
    let 失败数量 = 0
    
    for (const documentId of 选中文档列表.value) {
      try {
        const 响应 = await documentService.reprocessDocument(documentId)
        if (响应.success) {
          成功数量++
        } else {
          失败数量++
        }
      } catch (error) {
        失败数量++
      }
    }
    
    if (失败数量 === 0) {
      message.success(`批量处理成功！共处理 ${成功数量} 个文档`)
    } else {
      message.warning(`批量处理完成！成功 ${成功数量} 个，失败 ${失败数量} 个`)
    }
    
    // 刷新列表
    获取文档列表()
    
  } catch (error) {
    console.error('❌ 批量处理异常:', error)
    message.error('批量处理失败')
  }
}

/**
 * 批量删除选中文档
 */
const 批量删除选中文档 = () => {
  if (选中文档列表.value.length === 0) {
    message.warning('请先选择要删除的文档')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${选中文档列表.value.length} 个文档吗？此操作不可撤销。`,
    okText: '确定删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        console.log('🗑️ 批量删除文档:', 选中文档列表.value)
        
        const 响应 = await documentService.batchDeleteDocuments(选中文档列表.value)
        
        if (响应.success) {
          const { 成功数量, 失败数量 } = 响应.data
          if (失败数量 === 0) {
            message.success(`批量删除成功！共删除 ${成功数量} 个文档`)
          } else {
            message.warning(`批量删除完成！成功 ${成功数量} 个，失败 ${失败数量} 个`)
          }
        } else {
          message.error(响应.error || '批量删除失败')
        }
        
        // 刷新列表
        获取文档列表()
        
      } catch (error) {
        console.error('❌ 批量删除异常:', error)
        message.error('批量删除失败')
      }
    }
  })
}

/**
 * 确认上传文档
 */
const 确认上传文档 = async () => {
  if (上传文件列表.value.length === 0) {
    message.warning('请选择要上传的文件')
    return
  }

  try {
    上传中.value = true
    上传进度信息.显示 = true
    上传进度信息.状态 = 'active'
    上传进度信息.总文件数 = 上传文件列表.value.length

    console.log('📤 开始上传文档，文件数量:', 上传文件列表.value.length)

    const 文件列表 = 上传文件列表.value.map(item => item.originFileObj || item)
    
    const 响应 = await documentService.batchUploadDocuments(知识库ID.value, 文件列表, {
      ...上传选项,
      onProgress: (progress) => {
        console.log('上传进度:', progress)
        上传进度信息.百分比 = Math.round(progress.loaded / progress.total * 100)
        上传进度信息.当前文件 = Math.ceil(progress.loaded / progress.total * 上传进度信息.总文件数)
        
        if (progress.rate) {
          const 速度 = (progress.rate / 1024 / 1024).toFixed(2)
          上传进度信息.速度 = `${速度} MB/s`
        }
      }
    })

    console.log('📡 上传响应:', 响应)

    if (响应.success) {
      const { 上传成功数量, 上传失败数量, 总数量 } = 响应.data
      
      上传进度信息.百分比 = 100
      上传进度信息.状态 = 上传失败数量 > 0 ? 'exception' : 'success'
      
      setTimeout(() => {
        if (上传失败数量 === 0) {
          message.success(`所有文档上传成功！共 ${上传成功数量} 个文件`)
        } else {
          message.warning(`部分文档上传成功！成功 ${上传成功数量} 个，失败 ${上传失败数量} 个，共 ${总数量} 个文件`)
        }
        上传对话框可见.value = false
        上传文件列表.value = []
        重置上传进度()
        获取文档列表()
      }, 1000)
    } else {
      上传进度信息.状态 = 'exception'
      message.error(响应.error || '文档上传失败')
    }

  } catch (error) {
    console.error('❌ 上传文档异常:', error)
    上传进度信息.状态 = 'exception'
    message.error('上传文档失败')
  } finally {
    上传中.value = false
  }
}

/**
 * 重置上传进度
 */
const 重置上传进度 = () => {
  上传进度信息.显示 = false
  上传进度信息.百分比 = 0
  上传进度信息.状态 = 'normal'
  上传进度信息.当前文件 = 0
  上传进度信息.总文件数 = 0
  上传进度信息.速度 = ''
}

/**
 * 格式化时间
 */
const 格式化时间 = (时间字符串) => {
  if (!时间字符串) return '-'
  const 时间 = new Date(时间字符串)
  return 时间.toLocaleDateString() + ' ' + 时间.toLocaleTimeString()
}

/**
 * 获取文件类型图标
 */
const 获取文件类型图标 = (文档类型) => {
  const 图标映射 = {
    'pdf': FilePdfOutlined,
    'docx': FileWordOutlined,
    'word': FileWordOutlined,
    'xlsx': FileExcelOutlined,
    'excel': FileExcelOutlined,
    'image': FileImageOutlined,
    'default': FileOutlined
  }
  return 图标映射[文档类型] || 图标映射.default
}

/**
 * 获取文件类型颜色
 */
const 获取文件类型颜色 = (文档类型) => {
  const 颜色映射 = {
    'pdf': '#ff4d4f',
    'docx': '#1890ff',
    'word': '#1890ff',
    'xlsx': '#52c41a',
    'excel': '#52c41a',
    'image': '#722ed1',
    'text': '#fa8c16',
    'markdown': '#13c2c2'
  }
  return 颜色映射[文档类型] || '#666'
}

/**
 * 获取文件类型描述
 */
const 获取文件类型描述 = (文档类型) => {
  const 描述映射 = {
    'pdf': 'PDF文档',
    'docx': 'Word文档',
    'word': 'Word文档',
    'xlsx': 'Excel表格',
    'excel': 'Excel表格',
    'text': '文本文件',
    'markdown': 'Markdown文档',
    'html': 'HTML文档',
    'image': '图像文件'
  }
  return 描述映射[文档类型] || '未知类型'
}

/**
 * 格式化文件大小
 */
const 格式化文件大小 = (字节数) => {
  if (!字节数) return '0 B'

  const 单位 = ['B', 'KB', 'MB', 'GB']
  let 大小 = 字节数
  let 单位索引 = 0

  while (大小 >= 1024 && 单位索引 < 单位.length - 1) {
    大小 /= 1024
    单位索引++
  }

  return `${大小.toFixed(1)} ${单位[单位索引]}`
}

/**
 * 获取状态颜色
 */
const 获取状态颜色 = (状态) => {
  const 状态映射 = {
    'processed': 'green',
    'processing': 'orange',
    'uploading': 'blue',
    'failed': 'red'
  }
  return 状态映射[状态] || 'default'
}

/**
 * 获取状态文本
 */
const 获取状态文本 = (状态) => {
  const 状态映射 = {
    'processed': '已处理',
    'processing': '处理中',
    'uploading': '上传中',
    'failed': '失败'
  }
  return 状态映射[状态] || '未知'
}

/**
 * 查看文档详情
 */
const 查看文档详情 = async (record) => {
  try {
    console.log('👁️ 查看文档详情:', record)

    const 响应 = await documentService.getDocumentDetail(record.id)

    if (响应.success) {
      当前查看文档.value = {
        ...record,
        ...响应.data
      }
      详情对话框可见.value = true
    } else {
      message.error(响应.error || '获取文档详情失败')
    }
  } catch (error) {
    console.error('❌ 查看文档详情异常:', error)
    message.error('获取文档详情失败')
  }
}

/**
 * 预览文档
 */
const 预览文档 = async (record) => {
  try {
    console.log('👀 预览文档:', record)

    预览加载中.value = true
    当前预览文档.value = record
    预览对话框可见.value = true

    const 响应 = await documentService.previewDocument(record.id, {
      最大长度: 5000,
      包含元数据: true
    })

    if (响应.success) {
      预览内容.value = 响应.data.内容预览 || 响应.data.文档内容 || '暂无预览内容'
    } else {
      预览内容.value = '预览失败：' + (响应.error || '未知错误')
    }
  } catch (error) {
    console.error('❌ 预览文档异常:', error)
    预览内容.value = '预览失败：' + error.message
  } finally {
    预览加载中.value = false
  }
}

/**
 * 删除文档
 */
const 删除文档 = async (record) => {
  try {
    console.log('🗑️ 删除文档:', record)

    const 响应 = await documentService.deleteDocument(record.id)

    console.log('📡 删除文档响应:', 响应)

    if (响应.success) {
      message.success('文档删除成功')
      获取文档列表()
    } else {
      message.error(响应.error || '删除文档失败')
    }

  } catch (error) {
    console.error('❌ 删除文档异常:', error)
    message.error('删除文档失败')
  }
}

/**
 * 编辑文档
 */
const 编辑文档 = async (record) => {
  try {
    console.log('✏️ 编辑文档:', record)

    // 获取文档详情
    const 响应 = await documentService.getDocumentDetail(record.id)

    if (响应.success) {
      当前编辑文档.value = 响应.data
      
      // 填充编辑表单数据
      编辑表单数据.文档名称 = 响应.data.文档名称 || record.文档名称
      编辑表单数据.文档类型 = 响应.data.文档类型 || record.文档类型
      编辑表单数据.状态 = 响应.data.状态 || record.状态
      编辑表单数据.文档内容 = 响应.data.文档内容 || ''
      编辑表单数据.元数据字符串 = 响应.data.元数据 ? JSON.stringify(响应.data.元数据, null, 2) : ''
      编辑表单数据.备注 = 响应.data.备注 || ''
      
      编辑对话框可见.value = true
    } else {
      message.error(响应.error || '获取文档详情失败')
    }
  } catch (error) {
    console.error('❌ 编辑文档异常:', error)
    message.error('编辑文档失败')
  }
}

/**
 * 确认编辑文档
 */
const 确认编辑文档 = async () => {
  try {
    编辑中.value = true
    元数据验证错误.value = ''

    // 验证表单
    await 编辑表单引用.value?.validate()

    // 准备更新数据
    const 更新数据 = {
      文档名称: 编辑表单数据.文档名称,
      状态: 编辑表单数据.状态,
      备注: 编辑表单数据.备注
    }

    // 如果是文本类型文档，包含内容
    if (编辑表单数据.文档类型 === 'text' || 编辑表单数据.文档类型 === 'markdown' || 编辑表单数据.文档类型 === 'json' || 编辑表单数据.文档类型 === 'html') {
      更新数据.文档内容 = 编辑表单数据.文档内容
    }

    // 处理元数据
    if (编辑表单数据.元数据字符串.trim()) {
      try {
        更新数据.元数据 = JSON.parse(编辑表单数据.元数据字符串)
        元数据验证错误.value = ''
      } catch (error) {
        元数据验证错误.value = '元数据格式错误：' + error.message
        message.error('元数据格式错误，请输入有效的JSON格式')
        return
      }
    } else {
      更新数据.元数据 = {}
    }

    console.log('📝 更新文档数据:', 更新数据)

    const 响应 = await documentService.updateDocument(当前编辑文档.value.id, 更新数据)

    if (响应.success) {
      message.success('文档更新成功')
      编辑对话框可见.value = false
      
      // 如果修改了文档内容，提示用户重新处理
      if (更新数据.文档内容) {
        message.info('文档内容已更新，建议重新处理文档以更新向量索引', 5)
      }
      
      获取文档列表()
    } else {
      message.error(响应.error || '文档更新失败')
    }

  } catch (error) {
    console.error('❌ 更新文档异常:', error)
    message.error('文档更新失败：' + error.message)
  } finally {
    编辑中.value = false
  }
}

/**
 * 取消编辑
 */
const 取消编辑 = () => {
  编辑对话框可见.value = false
  当前编辑文档.value = null
  // 重置表单数据
  Object.assign(编辑表单数据, {
    文档名称: '',
    文档类型: '',
    状态: '',
    文档内容: '',
    元数据字符串: '',
    备注: ''
  })
}

/**
 * 重新处理文档
 */
const 重新处理文档 = async (record) => {
  try {
    console.log('🔄 重新处理文档:', record)

    const 响应 = await documentService.reprocessDocument(record.id)

    if (响应.success) {
      message.success('开始重新处理文档')
      获取文档列表()
    } else {
      message.error(响应.error || '重新处理文档失败')
    }
  } catch (error) {
    console.error('❌ 重新处理文档异常:', error)
    message.error('重新处理文档失败')
  }
}

/**
 * 下载文档
 */
const 下载文档 = async (record) => {
  try {
    console.log('💾 下载文档:', record)

    const 响应 = await documentService.downloadDocument(record.id)

    if (响应.success) {
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([响应.data]))
      const link = document.createElement('a')
      link.href = url
      link.download = record.文档名称
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('文档下载成功')
    } else {
      message.error(响应.error || '文档下载失败')
    }
  } catch (error) {
    console.error('❌ 下载文档异常:', error)
    message.error('文档下载失败')
  }
}

/**
 * 处理表格变化
 */
const 处理表格变化 = (pagination) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  获取文档列表()
}

/**
 * 返回知识库列表
 */
const 返回知识库列表 = () => {
  router.push({ name: 'KnowledgeBaseManagement' })
}

/**
 * 显示上传对话框
 */
const 显示上传对话框 = () => {
  上传对话框可见.value = true
  上传文件列表.value = []
}

/**
 * 取消上传
 */
const 取消上传 = () => {
  上传对话框可见.value = false
  上传文件列表.value = []
}

/**
 * 查看支持格式
 */
const 查看支持格式 = () => {
  格式对话框可见.value = true
}

/**
 * 处理文件上传前
 */
const 处理文件上传前 = (file) => {
  console.log('准备上传文件:', file.name, '大小:', file.size)

  // 检查文件大小（100MB限制）
  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过100MB')
    return false
  }

  return false // 阻止自动上传
}

/**
 * 移除文件
 */
const 移除文件 = (file) => {
  console.log('移除文件:', file.name)
}

/**
 * 获取支持的文件格式
 */
const 获取支持的文件格式 = async () => {
  try {
    console.log('📋 获取支持的文件格式')

    const 响应 = await documentService.getSupportedFormats()

    if (响应.success) {
      支持的格式列表.value = 响应.data.支持的格式列表 || []
      格式统计.value = 响应.data.格式统计 || {}

      // 按类型分组格式
      const 类型分组 = {}
      支持的格式列表.value.forEach(格式 => {
        const 类型 = 格式.类型
        if (!类型分组[类型]) {
          类型分组[类型] = {
            类型名称: 类型,
            扩展名列表: [],
            描述: 格式.描述
          }
        }
        类型分组[类型].扩展名列表.push(格式.扩展名)
      })

      格式统计.value.文件类型详情 = Object.values(类型分组)
      console.log('✅ 获取支持的文件格式成功:', 支持的格式列表.value)
    } else {
      console.warn('⚠️ 获取支持的文件格式失败:', 响应.error)
      设置默认格式()
    }
  } catch (error) {
    console.error('❌ 获取支持的文件格式异常:', error)
    设置默认格式()
  }
}

/**
 * 设置默认支持的格式
 */
const 设置默认格式 = () => {
  支持的格式列表.value = [
    { 扩展名: '.pdf', 类型: 'pdf', 描述: 'PDF文档' },
    { 扩展名: '.docx', 类型: 'word', 描述: 'Word文档' },
    { 扩展名: '.doc', 类型: 'word', 描述: 'Word文档' },
    { 扩展名: '.xlsx', 类型: 'excel', 描述: 'Excel表格' },
    { 扩展名: '.xls', 类型: 'excel', 描述: 'Excel表格' },
    { 扩展名: '.txt', 类型: 'text', 描述: '文本文件' },
    { 扩展名: '.md', 类型: 'markdown', 描述: 'Markdown文档' },
    { 扩展名: '.html', 类型: 'html', 描述: 'HTML文档' },
    { 扩展名: '.json', 类型: 'json', 描述: 'JSON文件' },
    { 扩展名: '.csv', 类型: 'csv', 描述: 'CSV文件' }
  ]

  格式统计.value = {
    总格式数: 支持的格式列表.value.length,
    文档类型: 4,
    文本类型: 3,
    数据类型: 3,
    文件类型详情: [
      {
        类型名称: 'pdf',
        扩展名列表: ['.pdf'],
        描述: 'PDF文档格式'
      },
      {
        类型名称: 'word',
        扩展名列表: ['.docx', '.doc'],
        描述: 'Microsoft Word文档'
      },
      {
        类型名称: 'excel',
        扩展名列表: ['.xlsx', '.xls'],
        描述: 'Microsoft Excel表格'
      },
      {
        类型名称: 'text',
        扩展名列表: ['.txt'],
        描述: '纯文本文件'
      },
      {
        类型名称: 'markdown',
        扩展名列表: ['.md'],
        描述: 'Markdown标记语言'
      },
      {
        类型名称: 'html',
        扩展名列表: ['.html'],
        描述: 'HTML网页文件'
      },
      {
        类型名称: 'json',
        扩展名列表: ['.json'],
        描述: 'JSON数据文件'
      },
      {
        类型名称: 'csv',
        扩展名列表: ['.csv'],
        描述: 'CSV数据文件'
      }
    ]
  }
}

// ==================== 分块大小智能计算 ====================

// 🔧 新增：根据文件大小智能计算分块参数
const getMinChunkSize = () => {
  // 获取当前选中文件的总大小
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)

  if (totalSize === 0) return 50 // 没有文件时的默认最小值

  // 根据文件大小智能调整最小分块大小
  if (totalSize < 1024) return 20        // 小于1KB：最小20字符
  if (totalSize < 5 * 1024) return 50    // 小于5KB：最小50字符
  if (totalSize < 50 * 1024) return 100  // 小于50KB：最小100字符
  if (totalSize < 500 * 1024) return 200 // 小于500KB：最小200字符
  return 500                             // 大文件：最小500字符
}

const getChunkStep = () => {
  const minSize = getMinChunkSize()
  return minSize < 100 ? 10 : (minSize < 500 ? 50 : 100)
}

const getChunkSizeHint = () => {
  const totalSize = 上传文件列表.value.reduce((sum, file) => sum + (file.size || 0), 0)
  const currentStrategy = 上传选项.value.分块策略

  if (totalSize === 0) return '建议：根据文件大小自动调整'

  // 使用统一的文件大小描述
  const sizeDescription = getFileSizeDescription(totalSize, currentStrategy)
  const config = 分块策略配置[currentStrategy]

  if (!config) return `${sizeDescription}：建议根据策略调整`

  // 根据策略和文件大小给出具体建议
  if (currentStrategy === '行级精准分块') {
    if (totalSize > 文件大小分类.策略特定[currentStrategy].最大限制) {
      return `${sizeDescription}：建议切换到语义优化分块或智能递归分块`
    }
    if (totalSize > 文件大小分类.策略特定[currentStrategy].警告阈值) {
      return `${sizeDescription}：建议分块大小${config.minSize}-${config.recommendedSize}字符`
    }
    return `${sizeDescription}：建议分块大小${config.minSize}-${config.maxSize}字符`
  }

  return `${sizeDescription}：建议分块大小${config.minSize}-${config.maxSize}字符`
}

// ==================== 分块策略智能推荐系统 ====================

// 使用共享配置

// 获取当前策略信息
const getCurrentStrategyInfo = () => {
  const strategy = 上传选项.value.分块策略
  return 分块策略配置[strategy] || 分块策略配置['智能递归分块']
}

/**
 * 检查分块策略适用性（简化版）
 */
const 检查分块策略适用性 = () => {
  const 当前策略 = 上传选项.分块策略
  const 策略配置 = 分块策略配置[当前策略]

  if (!策略配置 || !策略配置.fileSizeLimit || 上传文件列表.value.length === 0) {
    return true
  }

  const 总文件大小 = 上传文件列表.value.reduce((sum, f) => sum + (f.size || 0), 0)
  const { warning, maximum } = 策略配置.fileSizeLimit

  if (总文件大小 > maximum) {
    const 文件大小MB = (总文件大小 / 1024 / 1024).toFixed(1)
    message.error(`文件过大（${文件大小MB}MB），不适合行级精准分块，建议使用其他策略`)
    // 自动切换到语义优化分块
    上传选项.分块策略 = '语义优化分块'
    return false
  } else if (总文件大小 > warning) {
    const 文件大小KB = (总文件大小 / 1024).toFixed(0)
    message.warning(`文件较大（${文件大小KB}KB），行级分块可能影响性能，建议控制在20KB以内`)
  }

  return true
}

// ==================== 生命周期 ====================

onMounted(() => {
  console.log('🎯 文档管理页面已挂载，知识库ID:', 知识库ID.value)
  获取文档列表()
  获取支持的文件格式()
})
</script>

<style scoped>
.document-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.list-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content .name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.info-content .type {
  font-size: 12px;
  color: #666;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-info .size {
  font-weight: 500;
  color: #1a1a1a;
}

.file-info .chunks {
  font-size: 12px;
  color: #666;
}

.upload-content {
  padding: 16px 0;
}

.supported-formats {
  margin-top: 16px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.supported-formats h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #1a1a1a;
}

.format-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.formats-content {
  padding: 16px 0;
}

.format-stats {
  margin-bottom: 24px;
}

.format-detail {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.format-detail .extensions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.format-detail .description {
  color: #666;
  font-size: 14px;
}

.formats-content {
  max-height: 500px;
  overflow-y: auto;
}

.format-detail {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.extensions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.description {
  font-size: 12px;
  color: #666;
}

/* 编辑功能样式 */
.edit-content {
  padding: 8px 0;
}

.help-text {
  margin-top: 4px;
}

.help-text small {
  color: #999;
  font-size: 12px;
}

/* 详情功能样式 */
.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.metadata-display {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  max-height: 200px;
  overflow-y: auto;
}

.content-preview {
  background: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.6;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}

/* 预览功能样式 */
.preview-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.preview-body {
  min-height: 400px;
}

.content-display {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.content-display pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 危险操作样式 */
.danger-item {
  color: #ff4d4f !important;
}

.danger-item:hover {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: flex-start;
  }
}

/* 编辑功能样式 */
.edit-content {
  max-height: 70vh;
  overflow-y: auto;
}

.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

/* 分块策略选择器样式 */
.strategy-selector {
  width: 100%;
}

.recommended-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 10px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 12px;
}

.strategy-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.strategy-name {
  display: flex;
  align-items: center;
  gap: 6px;
}

.recommended-badge {
  background: #52c41a;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.strategy-info-card {
  margin-top: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
  font-size: 12px;
  line-height: 1.5;
}

.strategy-description {
  color: #333;
  margin-bottom: 6px;
}

.strategy-advantages {
  color: #52c41a;
  margin-bottom: 4px;
}

.strategy-use-cases {
  color: #1890ff;
}

.help-text {
  margin-top: 4px;
  color: #666;
}

.help-text small {
  font-size: 12px;
}

.error-text {
  margin-top: 4px;
  color: #ff4d4f;
}

.error-text small {
  font-size: 12px;
}

.metadata-display {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.content-preview {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 1.5;
}

.preview-content {
  padding: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.preview-body {
  max-height: 60vh;
  overflow-y: auto;
}

.content-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
}

.content-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.no-content {
  text-align: center;
  padding: 40px 20px;
}
</style>
