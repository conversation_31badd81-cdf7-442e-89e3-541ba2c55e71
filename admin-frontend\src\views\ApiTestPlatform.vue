<template>
  <a-layout style="min-height: 100vh;" class="api-test-platform-container">
    <!-- 左侧边栏: 接口分类 和 请求历史 -->
    <a-layout-sider 
      width="280" 
      theme="light" 
      collapsible 
      :collapsed="siderCollapsed" 
      @collapse="onSiderCollapse" 
      class="sider-container"
    >
      <div class="sider-content">
        <div class="sider-section" style="margin-bottom: 16px;">
          <h3><folder-open-outlined /> 接口分类</h3>
          <api-category-menu />
        </div>
        <div class="sider-section">
          <request-history />
        </div>
      </div>
    </a-layout-sider>

    <!-- 中间主内容区: 接口列表 和 测试面板 -->
    <a-layout>
      <a-layout-content style="padding: 0 16px 16px; display: flex; flex-direction: column; overflow: hidden;">
        <a-page-header
          title="接口测试平台"
          sub-title="选择或搜索接口进行测试"
          style="background-color: #fff; margin: 16px 0; border-radius: 4px; flex-shrink: 0;"
        >
          <template #extra>
            <a-space>
              <a-button key="settings" @click="showSettingsModal">
                <template #icon><setting-outlined /></template>
                设置
              </a-button>
              <a-button key="docs" @click="goToDocs">帮助文档</a-button>
              <a-button key="clearCache" type="dashed" @click="clearPlatformCache">清空缓存</a-button>
            </a-space>
          </template>
        </a-page-header>

        <!-- 主要内容区域，根据加载状态显示 -->
        <div v-if="store.isLoading" style="display: flex; justify-content: center; align-items: center; flex-grow: 1; height: 100%;">
          <a-spin size="large" tip="正在从后端加载API定义..." />
        </div>

        <div v-else-if="!store.isLoading && (!store.apiCategories || store.apiCategories.length === 0)" 
             style="flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; text-align: center;">
          <a-empty description="未能加载API定义或后端未返回任何API。">
            <template #description>
              <span>未能加载API定义或后端未返回任何API。</span><br/>
              <span>请检查后端服务是否正常运行，以及OpenAPI文档路径是否配置正确。</span>
            </template>
            <a-button type="primary" @click="reloadApis">重新加载</a-button>
          </a-empty>
        </div>
        
        <a-row v-else :gutter="16" style="flex-grow: 1; display: flex; overflow: hidden;">
          <!-- 左半部分: 接口列表 -->
          <a-col :span="8" style="display: flex; flex-direction: column; height: 100%;">
            <a-card title="接口列表" class="flex-card" :bodyStyle="{ padding: '16px', flexGrow: 1, overflowY: 'auto' }">
              <api-list-display style="height: 100%;"/>
            </a-card>
          </a-col>

          <!-- 右半部分: 接口测试面板 -->
          <a-col :span="16" style="display: flex; flex-direction: column; height: 100%;">
            <div class="flex-card" style="height: 100%;"> 
              <api-test-panel style="height: 100%;"/>
            </div>
          </a-col>
        </a-row>
      </a-layout-content>
    </a-layout>

    <!-- 底部日志面板抽屉 -->
    <a-drawer
      title="操作日志"
      placement="bottom"
      :closable="true"
      :open="logDrawerVisible"
      height="300"
      @close="onLogDrawerClose"
    >
      <div v-if="store.logs && store.logs.length > 0" class="log-entries">
        <p v-for="(log, index) in store.logs" :key="index" class="log-entry">{{ log }}</p>
      </div>
      <a-empty v-else description="暂无操作日志" />
      <template #extra>
        <a-button size="small" @click="clearLogsFromDrawer" type="primary" danger>
          <template #icon><delete-outlined /></template>
          清空日志
        </a-button>
      </template>
    </a-drawer>
    
    <!-- 浮动日志按钮 -->
    <a-button 
      type="primary" 
      shape="circle" 
      @click="toggleLogDrawer" 
      style="position: fixed; bottom: 24px; right: 24px; z-index: 1000; box-shadow: 0 2px 8px rgba(0,0,0,0.15);"
      title="查看操作日志"
    >
      <template #icon><file-text-outlined /></template>
    </a-button>

    <!-- 设置模态框 -->
    <a-modal
      v-model:open="settingsModalVisible"
      title="接口测试平台设置"
      width="600px"
      @ok="saveSettings"
      @cancel="cancelSettings"
    >
      <a-form :model="settingsForm" layout="vertical">
        <a-form-item 
          label="API基础URL" 
          :help="`当前设置: ${store.apiBaseUrl}`"
        >
          <a-input
            v-model:value="settingsForm.apiBaseUrl"
            placeholder="例如: http://127.0.0.1:8000 或 https://api.example.com"
            style="margin-bottom: 8px;"
          />
          <div style="display: flex; gap: 8px;">
            <a-button size="small" @click="resetToDefault">重置为默认</a-button>
            <a-button size="small" @click="testConnection" :loading="testingConnection">测试连接</a-button>
          </div>
          <div v-if="connectionTestResult" :style="{ 
            marginTop: '8px', 
            padding: '8px', 
            borderRadius: '4px',
            backgroundColor: connectionTestResult.success ? '#f6ffed' : '#fff2f0',
            border: `1px solid ${connectionTestResult.success ? '#b7eb8f' : '#ffccc7'}`,
            color: connectionTestResult.success ? '#52c41a' : '#ff4d4f'
          }">
            {{ connectionTestResult.message }}
          </div>
        </a-form-item>
        
        <a-form-item label="其他设置">
          <a-checkbox v-model:checked="settingsForm.autoLoadOnStart">
            启动时自动加载接口定义
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-layout>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import {
  Button as AButton,
  Card as ACard,
  Checkbox as ACheckbox,
  Col as ACol,
  Drawer as ADrawer,
  Empty as AEmpty,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Layout as ALayout,
  LayoutContent as ALayoutContent,
  LayoutSider as ALayoutSider,
  message,
  Modal as AModal,
  PageHeader as APageHeader,
  Row as ARow,
  Space as ASpace,
  Spin as ASpin
} from 'ant-design-vue';
import {
  DeleteOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue';
import { useApiTestingModule } from '@/store/apiTestingModule.js';
import { useUserStore } from '@/store';
import ApiCategoryMenu from '@/components/api-testing/ApiCategoryMenu.vue';
import ApiListDisplay from '@/components/api-testing/ApiListDisplay.vue';
import ApiTestPanel from '@/components/api-testing/ApiTestPanel.vue';
import RequestHistory from '@/components/api-testing/RequestHistory.vue';

// 响应式状态
const store = useApiTestingModule();
const userStore = useUserStore();

const siderCollapsed = ref(false);
const logDrawerVisible = ref(false);
const settingsModalVisible = ref(false);
const settingsForm = ref({
  apiBaseUrl: store.apiBaseUrl,
  autoLoadOnStart: store.autoLoadOnStart
});
const testingConnection = ref(false);
const connectionTestResult = ref(null);

// 事件处理方法
const reloadApis = () => {
  store.loadApiDefinitions();
};

const onSiderCollapse = (collapsed) => {
  siderCollapsed.value = collapsed;
};

const goToDocs = () => {
  message.info('跳转到帮助文档功能待实现');
};

const clearPlatformCache = () => {
  store.clearRequestHistory();
  store.clearLogs();
  message.success('接口测试平台缓存已清空 (历史记录和日志)');
};

const toggleLogDrawer = () => {
  logDrawerVisible.value = !logDrawerVisible.value;
};

const onLogDrawerClose = () => {
  logDrawerVisible.value = false;
};

const clearLogsFromDrawer = () => {
  store.clearLogs();
  message.success('操作日志已清空');
};

const showSettingsModal = () => {
  settingsForm.value.apiBaseUrl = store.apiBaseUrl;
  settingsForm.value.autoLoadOnStart = store.autoLoadOnStart;
  connectionTestResult.value = null;
  settingsModalVisible.value = true;
};

const saveSettings = () => {
  if (store.setApiBaseUrl(settingsForm.value.apiBaseUrl)) {
    store.autoLoadOnStart = settingsForm.value.autoLoadOnStart;
    localStorage.setItem('apiTestingAutoLoad', settingsForm.value.autoLoadOnStart);
    
    settingsModalVisible.value = false;
    message.success('设置已保存');
    
    if (settingsForm.value.apiBaseUrl !== store.apiBaseUrl) {
      message.info('API基础URL已更改，建议重新加载接口定义');
    }
  } else {
    message.error('API基础URL格式不正确，请检查后重试');
  }
};

const cancelSettings = () => {
  settingsForm.value.apiBaseUrl = store.apiBaseUrl;
  settingsForm.value.autoLoadOnStart = store.autoLoadOnStart;
  connectionTestResult.value = null;
  settingsModalVisible.value = false;
};

const resetToDefault = () => {
  settingsForm.value.apiBaseUrl = 'http://127.0.0.1:8000';
  settingsForm.value.autoLoadOnStart = true;
  connectionTestResult.value = null;
  message.info('已重置为默认设置');
};

const testConnection = async () => {
  testingConnection.value = true;
  connectionTestResult.value = null;
  
  try {
    connectionTestResult.value = await store.testConnection(settingsForm.value.apiBaseUrl);
  } catch (error) {
    connectionTestResult.value = {
      success: false,
      message: `测试连接时发生错误: ${error.message}`
    };
  } finally {
    testingConnection.value = false;
  }
};

// 生命周期和监听器
onMounted(() => {
  console.log('[API测试平台] 组件挂载，开始初始化...');
  
  // 同步主应用的认证Token到接口测试模块
  if (userStore.token) {
    store.setMainAppToken(userStore.token);
    console.log('[API测试平台] 已同步主应用认证Token');
  } else {
    store.setMainAppToken(null);
    console.log('[API测试平台] 主应用无Token，已清空测试平台Token');
  }
  
  // 加载API定义
  store.loadApiDefinitions();
  console.log('[API测试平台] 已触发API定义加载');
});

// 监听主应用Token的变化，保持同步
watch(() => userStore.token, (newToken) => {
  console.log('[API测试平台] 主应用Token变化，新Token状态:', newToken ? '有Token' : '无Token');
  store.setMainAppToken(newToken);
}, { immediate: false });
</script>

<style scoped>
.api-test-platform-container {
  /* 顶层容器样式，由内部组件控制具体布局 */
}

.sider-container {
  /* 侧边栏容器样式 */
  box-shadow: 2px 0 6px rgba(0,21,41,.08);
}

.sider-content {
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.sider-section h3 {
  font-size: 14px;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  gap: 8px;
}

.flex-card {
  display: flex;
  flex-direction: column;
  height: 100%; 
}

.ant-page-header {
  padding: 12px 16px;
}

.log-entries {
  height: 100%;
  overflow-y: auto;
  font-size: 12px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.log-entry {
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-entry:last-child {
  border-bottom: none;
}
</style> 