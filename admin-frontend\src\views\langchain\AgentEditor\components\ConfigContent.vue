<template>
  <div class="center-content">
    <div class="config-container">
      <a-spin :spinning="loading" tip="加载中...">
        <!-- 动态配置内容 -->
        <div class="config-content">
          <!-- 基础信息配置 -->
          <BasicConfig
            v-show="currentStep === 'basic'"
            v-model="localForm"
            :errors="formErrors"
            :is-edit-mode="isEditMode"
            @validate="handleValidate"
            @change="handleFormChange"
          />

          <!-- 提示词配置 -->
          <PromptConfig
            v-show="currentStep === 'prompt'"
            v-model="localForm"
            :errors="formErrors"
            @validate="handleValidate"
            @change="handleFormChange"
          />

          <!-- 模型配置 -->
          <ModelConfig
            v-show="currentStep === 'model'"
            v-model="localForm"
            :errors="formErrors"
            :model-options="modelOptions"
            :loading="loadingStates"
            @validate="handleValidate"
            @change="handleFormChange"
          />

          <!-- RAG配置 -->
          <RAGConfig
            v-show="currentStep === 'rag'"
            v-model="localForm"
            :errors="formErrors"
            :knowledge-base-options="knowledgeBaseOptions"
            :embedding-model-options="embeddingModelOptions"
            :model-options="modelOptions"
            :loading="loadingStates"
            @validate="handleValidate"
            @change="handleFormChange"
          />

          <!-- 工具配置 -->
          <ToolsConfig
            v-show="currentStep === 'tools'"
            v-model="localForm"
            :available-tools="availableTools"
            :loading="loadingStates"
            :auto-save="autoSave"
            @change="handleFormChange"
            @test-tool="handleToolTest"
          />

          <!-- 结构化输出配置 -->
          <OutputConfig
            v-show="currentStep === 'output'"
            v-model="localForm"
            :errors="formErrors"
            @validate="handleValidate"
            @change="handleFormChange"
          />

          <!-- 自定义变量配置 -->
          <VariablesConfig
            v-show="currentStep === 'variables'"
            v-model="localForm"
            :errors="formErrors"
            @validate="handleValidate"
            @change="handleFormChange"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

// 导入配置组件
import BasicConfig from './config/BasicConfig.vue'
import PromptConfig from './config/PromptConfig.vue'
import ModelConfig from './config/ModelConfig.vue'
import RAGConfig from './config/RAGConfig.vue'
import ToolsConfig from './config/ToolsConfig.vue'
import OutputConfig from './config/OutputConfig.vue'
import VariablesConfig from './config/VariablesConfig.vue'

// Props
const props = defineProps({
  currentStep: {
    type: String,
    required: true
  },
  agentForm: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  modelOptions: {
    type: Array,
    default: () => []
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  },
  availableTools: {
    type: Array,
    default: () => []
  },
  embeddingModelOptions: {
    type: Array,
    default: () => []
  },
  autoSave: {
    type: Function,
    default: null
  }
})

// Emits
const emit = defineEmits(['form-change', 'tool-toggle'])

// 本地表单数据 - 使用computed实现双向绑定
const localForm = computed({
  get: () => props.agentForm,
  set: (value) => emit('form-change', value)
})

// 表单验证错误
const formErrors = ref({})

// 加载状态
const loadingStates = ref({
  模型列表: false,
  知识库列表: false,
  工具列表: false
})

// 是否编辑模式
const isEditMode = ref(false)

// 处理表单变化
const handleFormChange = (formData) => {
  emit('form-change', formData || localForm.value)
}

// 处理验证
const handleValidate = (errors) => {
  Object.assign(formErrors.value, errors)
}

// 处理工具测试
const handleToolTest = (toolName) => {
  emit('tool-toggle', toolName)
  message.info(`测试工具: ${toolName}`)
}
</script>

<style scoped>
.center-content {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.config-container {
  max-width: 800px;
  margin: 0 auto;
}

.config-content {
  background: white;
  border-radius: 8px;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .center-content {
    padding: 0 8px;
  }

  .config-container {
    max-width: 100%;
  }
}
</style>